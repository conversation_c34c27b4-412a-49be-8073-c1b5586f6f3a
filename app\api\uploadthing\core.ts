import { auth } from "@/lib/auth/auth";
import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";

const f = createUploadthing();

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
    // Define as many FileRoutes as you like, each with a unique routeSlug
    imageUploader: f({
        image: {
        maxFileSize: "2MB",
        maxFileCount: 1,
        },
    })
    .middleware(async () => {
      const session = await auth();
      if (!session?.user) throw new UploadThingError("Unauthorized");
      return { userId: session?.user?.id as string };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      return { uploadedBy: metadata.userId };
    }),
    docUploader: f({
        pdf: {
        maxFileSize: "2MB",
        maxFileCount: 1,
        },
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
        maxFileSize: "2MB",
        maxFileCount: 1,
        },
    })
    .middleware(async () => {
    const session = await auth();
    if (!session?.user) throw new UploadThingError("Unauthorized");
    return { userId: session?.user?.id as string };
    })
    .onUploadComplete(async ({ metadata, file }) => {
    return { uploadedBy: metadata.userId };
    }),
    companyDocumentUploader: f({
        pdf: {
          maxFileSize: "2MB",
          maxFileCount: 25,
        },
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
          maxFileSize: "2MB",
          maxFileCount: 25,
        },
      })
        .middleware(async () => {
          const session = await auth();
          if (!session?.user) throw new UploadThingError("Unauthorized");
          return { userId: session?.user?.id as string };
        })
        .onUploadComplete(async ({ metadata, file }) => {
          return { uploadedBy: metadata.userId };
        }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
