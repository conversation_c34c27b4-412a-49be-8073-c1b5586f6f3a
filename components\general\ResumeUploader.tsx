"use client";

import { SaveUserFile } from "@/actions/file/saveFile";
import { UserFile } from "@/types/customTypes";
import { UploadDropzone } from "@/utils/uploadthing";
import { useState } from "react";
import { toast } from "sonner";

interface ResumeUploaderProps {
  userId: string;
  fileUse: string;
  docType: string;
}

export const ResumeUploader = ({ userId, fileUse }: ResumeUploaderProps) => {
  const [isUploaded, setIsUploaded] = useState(false);
  const [uploadResult, setUploadResult] = useState("");

  async function saveFileDate() {
    if (isUploaded) {
      if (uploadResult) {
        const data = JSON.parse(uploadResult);

        if (data) {
          const resumeFile: UserFile = {
            id: "",
            userId: userId,
            fileUse: fileUse,
            fileType: data[0].name.split(".")[1].toUpperCase(),
            fileName: data[0].name,
            description: "User Resume",
            key: data[0].key,
            url: data[0].ufsUrl,
            fileSize: data[0].size,
          };

          await SaveUserFile(resumeFile);
        }
      }
    }
  }

  return (
    <div>
      <UploadDropzone
        endpoint="docUploader"
        onClientUploadComplete={(res) => {
          // Do something with the response
          toast.success("Logo uploaded successfully.");
          setUploadResult(JSON.stringify(res));
          setIsUploaded(true);
          saveFileDate();
        }}
        onUploadError={(error: Error) => {
          toast.success(`ERROR! A problem was encountered. ${error.message}`);
        }}
        onUploadBegin={(name) => {
          // Do something once upload begins
          toast.info(`Uploading: ${name}`);
        }}
        onChange={(acceptedFiles) => {
          // Do something with the accepted files
        }}
      />
    </div>
  );
};
