"use server";

import { auth } from "@/lib/auth/auth";
import {
  getUserJobApplicationMatchesData,
  getUserJobResumeMatchesData,
} from "@/data/job/job";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const GetUserJobMatches = async (
  resumeId: string,
  companyId: string
) => {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  if (!resumeId) {
    return { error: "Missing user id." };
  }

  try {
    const data = await getUserJobResumeMatchesData(resumeId, companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    return {
      error: `Error! Failed to save file: ${error}`,
      data: null,
    };
  }
};

export const GetApplicationsJobMatches = async (jobId: string) => {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();
  

  if (!jobId) {
    return { error: "Missing user id." };
  }

  try {
    const data = await getUserJobApplicationMatchesData(jobId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    return {
      error: `Error! Failed to save file: ${error}`,
      data: null,
    };
  }
};
