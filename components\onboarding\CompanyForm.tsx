"use client";

import { z } from "zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CompanySchema, ExistingCompanySchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countryList } from "@/data/location/countryList";
import { UploadButton } from "@/utils/uploadthing";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { CheckIcon, XIcon } from "lucide-react";
import { toast } from "sonner";
import { useSearchCompany } from "@/hooks/useSearchCompany";
import { CreateCompany } from "@/actions/company/createCompany";
import { TextEditor } from "../general/TextEditor";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { BenefitsSelector } from "../job/BenefitsSelector";
import { UserRole, UserType } from "@prisma/client";
import { Textarea } from "@/components/ui/textarea";
import { UpdateUserCompany } from "@/actions/user/getUser";

export function CompanyForm() {
  const { data: session, status, update } = useSession();
  const [pending, setPending] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [isNewCompany, setIsNewCompany] = useState(false);
  const router = useRouter();

  // Use our custom hook for searching companies
  const { results: searchResults } = useSearchCompany(searchQuery);

  const newCompanyForm = useForm<z.infer<typeof CompanySchema>>({
    resolver: zodResolver(CompanySchema),
    defaultValues: {
      name: "",
      location: "",
      about: "",
      description: "",
      logo: "",
      website: "",
      xAccount: "",
      linkedIn: "",
      tin: "",
      foreignerRatio: 0,
      englishUsageRatio: 0,
      benefits: [],
    },
  });

  const existingCompanyForm = useForm<z.infer<typeof ExistingCompanySchema>>({
    resolver: zodResolver(ExistingCompanySchema),
    defaultValues: { companyId: "" },
  });

  // Handle existing company submission
  const handleExistingSubmit = async (
    data: z.infer<typeof ExistingCompanySchema>
  ) => {
    try {
      setPending(true);
      const response = await UpdateUserCompany(data.companyId);
      if (response?.success) {
        toast.success(response.success);
        if (session?.user) {
          await update({
            ...session,
            user: {
              ...session.user,
              onboarded: true,
              role: UserRole.USER,
              userType: UserType.COMPANY,
              companyId: data.companyId,
            },
          });
        }
        window.location.href = "/home/<USER>/dashboard";
      } else if (response?.error) {
        toast.error(response.error);
      }
    } finally {
      setPending(false);
    }
  };

  async function onSubmit(data: z.infer<typeof CompanySchema>) {
    try {
      setPending(true);
      // New company mode – create the company.
      const response = await CreateCompany(data);
      if (response?.success) {
        toast.success(response.success);
        if (session?.user) {
          await update({
            ...session,
            user: {
              ...session.user,
              onboarded: true,
              userType: UserType.COMPANY,
              role: UserRole.ADMIN,
              companyId: response.data?.id,
              companyName: response.data?.name,
            },
          });
        }
        router.push("/home/<USER>/dashboard");
      } else if (response?.error) {
        toast.error(response.error);
      }
    } catch (error) {
      console.error("Error in company form", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setPending(false);
    }
  }

  return (
    <div>
      <div className="text-center space-y-2 mb-10">
        <h2 className="text-2xl font-bold">Company Profile</h2>
        <p className="text-muted-foreground">
          Tell us about your company so job seekers can learn more about you.
        </p>
        <p className="text-muted-foreground text-left">
          We adhere to the Data Privacy regulations worldwide. All your
          personal and sentive data are encrypted and stored securely.
        </p>
      </div>

      {/* When NOT creating a new company, show search UI and a submit button */}
      {!isNewCompany && (
        <Form {...existingCompanyForm}>
          <form
            onSubmit={existingCompanyForm.handleSubmit(handleExistingSubmit)}
          >
            <div className="mb-6">
              {/* Search input */}
              <Input
                placeholder="Search company by name..."
                value={selectedCompany?.name || searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  if (selectedCompany) {
                    setSelectedCompany(null);
                    existingCompanyForm.setValue("companyId", "");
                  }
                }}
              />

              {/* Search results */}
              {searchQuery && searchResults.length > 0 && !selectedCompany && (
                <div className="border p-2 mt-2 rounded max-h-48 overflow-y-auto">
                  {searchResults.map((company) => (
                    <div
                      key={company.id as string}
                      className="cursor-pointer p-2 hover:bg-muted flex items-center justify-between"
                      onClick={() => {
                        setSelectedCompany(company);
                        existingCompanyForm.setValue("companyId", company.id as string);
                        setSearchQuery("");
                      }}
                    >
                      <span>{company.name}</span>
                      {existingCompanyForm.watch("companyId") ===
                        company.id && (
                        <CheckIcon className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                  ))}
                </div>
              )}
              {searchQuery && searchResults.length === 0 && !selectedCompany && (
                <div className="p-2 mt-2 text-sm text-muted-foreground">
                  No company with that name found. Check box below to add a new company.
                </div>
              )}

              {/* Hidden company ID field */}
              <FormField
                control={existingCompanyForm.control}
                name="companyId"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} type="hidden" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              type="submit"
              className="w-full cursor-pointer"
              disabled={!existingCompanyForm.watch("companyId") || pending}
            >
              {pending ? "Submitting..." : "Complete Setup"}
            </Button>
          </form>
        </Form>
      )}

      {/* Toggle Section */}
      <div className="mb-6 mt-6">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            className="cursor-pointer"
            checked={isNewCompany}
            onChange={(e) => {
              setIsNewCompany(e.target.checked);
              // When switching modes, clear any existing company selection and search query.
              if (e.target.checked) {
                setSelectedCompany(null);
                setSearchQuery("");
              }
            }}
          />
          <span>My company does not exist yet on this platform.</span>
        </label>
      </div>

      {/* When creating a new company, show full company form */}
      {isNewCompany && (
        <Form {...newCompanyForm}>
          <form
            className="space-y-6"
            onSubmit={newCompanyForm.handleSubmit(onSubmit)}
          >
            <div className="py-2 pb-2 text-muted-foreground text-sm">
              <span className="text-red-500 text-sm">*</span> Required fields
            </div>
            <div className="grid grid-cols-2 grid-rows-2 gap-4">
              {/* Company Name */}
              <div>
                <FormField
                  control={newCompanyForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-semibold">
                        Company Name<span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter company name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Company Logo */}
              <div className="row-span-2">
                <FormField
                  control={newCompanyForm.control}
                  name="logo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-semibold">
                        Company Logo<span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex justify-center items-center border-1 border-dashed p-2">
                          {field.value ? (
                            <div className="flex justify-center items-center">
                              <div className="relative w-fit">
                                <Image
                                  src={field.value}
                                  alt="Company Logo"
                                  width={100}
                                  height={100}
                                  className="rounded-lg"
                                />
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="icon"
                                  className="absolute -top-2 -right-2"
                                  onClick={() => field.onChange("")}
                                >
                                  <XIcon className="size-4" />
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="w-full flex justify-center items-center h-[120px]">
                              <UploadButton
                                endpoint="imageUploader"
                                onClientUploadComplete={(res) => {
                                  toast.success("Logo uploaded successfully.");
                                  field.onChange(res[0].ufsUrl);
                                }}
                                onUploadError={(error: Error) => {
                                  toast.error(`Error! ${error.message}`);
                                }}
                                onUploadBegin={(name) => {
                                  toast.info(`Uploading: ${name}`);
                                }}
                                appearance={{
                                  button:
                                    "ut-ready:bg-green-500 ut-uploading:cursor-not-allowed rounded-r-none bg-blue-500 bg-none after:bg-orange-400",
                                  container:
                                    "w-full p-4 flex-row rounded-md border-cyan-300",
                                  allowedContent:
                                    "flex flex-col items-center justify-center p-6 text-muted-foreground text-center",
                                }}
                                content={{
                                  button({ ready }) {
                                    if (ready) return <div>Upload File</div>;
                                    return "Getting ready...";
                                  },
                                  allowedContent({ ready, isUploading }) {
                                    if (!ready)
                                      return "Checking allowed file types";
                                    if (isUploading) return "Uploading image...";
                                    return "File you can upload: images (max size of 4MB)";
                                  },
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Company Location */}
              <div className="row-start-2 items-start justify-start">
                <FormField
                  control={newCompanyForm.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-semibold">
                        Company Location<span className="text-red-500">*</span>
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full cursor-pointer">
                            <SelectValue placeholder="Select Country" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="p-4">
                          <SelectGroup className="cursor-pointer">
                            {countryList.map((country) => (
                              <SelectItem
                                key={country.code}
                                value={country.code}
                                className="cursor-pointer"
                              >
                                <span className="emoji">
                                  {country.flagEmoji}
                                </span>
                                <span className="p-2">{country.name}</span>
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Company About */}
            <FormField
              control={newCompanyForm.control}
              name="about"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-semibold">About (Two liner or update later)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Short description about your company or a tagline."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Company Description */}
            <FormField
              control={newCompanyForm.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-semibold">
                    Company Description (Longer or update later)
                  </FormLabel>
                  <FormControl>
                    <TextEditor field={{
                      value: field.value || "",
                      onChange: field.onChange,
                      onBlur: field.onBlur,
                      ref: field.ref
                    }} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Company Social Media */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={newCompanyForm.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">Website</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://www.yourcompany.com"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={newCompanyForm.control}
                name="xAccount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">X (twitter) Account</FormLabel>
                    <FormControl>
                      <Input placeholder="@yourcompany" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={newCompanyForm.control}
                name="linkedIn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">LinkedIn</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://www.linkedin.com/company/..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Company TIN */}
              <FormField
                control={newCompanyForm.control}
                name="tin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">Tax Information Number (TIN)</FormLabel>
                    <FormControl>
                      <Input placeholder="TIN" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Company Ratio */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={newCompanyForm.control}
                name="foreignerRatio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">Foreigner Ratio</FormLabel>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span className="w-50">
                          What percentage of your employees are foreigners?
                        </span>
                        <span className="font-bold text-lg">
                          {field.value}%
                        </span>
                      </div>
                      <input
                        id="foreignerRatio"
                        aria-label="Foreigner Ratio"
                        type="range"
                        min="0"
                        max="100"
                        step="1"
                        value={field.value}
                        onChange={
                          (e) => field.onChange(parseInt(e.target.value, 10)) // Ensure parsing as integer
                        }
                        className="w-full cursor-pointer"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={newCompanyForm.control}
                name="englishUsageRatio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">English Usage Ratio</FormLabel>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span className="w-50">
                          What percentage of communication is in English?
                        </span>
                        <span className="font-bold text-lg">
                          {field.value}%
                        </span>
                      </div>
                      <input
                        id="englishUsageRatio"
                        aria-label="English Usage Ratio"
                        type="range"
                        min="0"
                        max="100"
                        step="1"
                        value={field.value}
                        onChange={
                          (e) => field.onChange(parseInt(e.target.value, 10)) // Ensure parsing as integer
                        }
                        className="w-full cursor-pointer"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex flex-col">
              <FormField
                control={newCompanyForm.control}
                name="benefits"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-semibold">
                      Benefits
                    </FormLabel>
                    <FormControl>
                      <BenefitsSelector field={field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              type="submit"
              className="w-full cursor-pointer"
              disabled={pending}
              size="lg"
            >
              {pending ? "Submitting..." : "Create Company"}
            </Button>
          </form>
        </Form>
      )}
    </div>
  );
}
