import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { GetUserNameById } from "@/actions/user/getUser";

export function useUser() {
  const { data: session, status } = useSession();
  const [userId, setUserId] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [isFetchingUserData, setIsFetchingUserData] = useState(false);


  useEffect(() => {
    const currentSessionUserId = session?.user?.id as string;

    if (currentSessionUserId) {
      async function fetchUserData() {
        setIsFetchingUserData(true);
        // Ensure we are fetching for the current session user
        const user = await GetUserNameById(currentSessionUserId);
        if (user && !user.error) {
          setFirstName(user?.firstName as string);
          setLastName(user?.lastName as string);
          setUserId(currentSessionUserId);
        } else {
          // Handle case where user data couldn't be fetched or error occurred
          setFirstName("");
          setLastName("");
          setUserId(""); // Or keep currentSessionUserId if preferred on error
        }
        setIsFetchingUserData(false);
      }
      fetchUserData();
    } else {
      // No session or user ID, clear data and ensure not fetching
      setFirstName("");
      setLastName("");
      setUserId("");
      setIsFetchingUserData(false);
    }
  }, [session?.user?.id]);

  // Overall loading: true if session is loading OR if we are actively fetching user details
  const loading = status === "loading" || isFetchingUserData;

  return { userId, firstName, lastName, loading, status };
}
