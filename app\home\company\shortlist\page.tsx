import { EmptyState } from "@/components/general/EmptyState";
import { auth } from "@/lib/auth/auth";
import { getJobShortlistedCount } from "@/actions/job/getJob";
import { CompanyJobShortlist } from "@/components/company/CompanyJobShortlist";
import { getCompanyJobList } from "@/actions/company/getCompany";

export default async function CompanyShortlistPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>;
}) {
  const session = await auth();
  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50;

  const data = await getCompanyJobList(session?.user?.companyId as string);
  
  // Get shortlist counts for all jobs
  const shortlistCounts = await Promise.all(
    data.map(job => getJobShortlistedCount(job.id))
  );
  
  // Add shortlist counts to job data
  const jobsWithShortlistCounts = data.map((job, index) => ({
    ...job,
    shortlistCount: shortlistCounts[index] || 0
  })).filter(job => job.shortlistCount > 0); // Only show jobs with shortlisted candidates

  // Pagination logic
  const totalPages = Math.ceil(jobsWithShortlistCounts.length / pageSize);
  const paginatedJobs = jobsWithShortlistCounts.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <>
      {jobsWithShortlistCounts?.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
          <h1 className="text-2xl font-semibold">Job Applications Shortlist</h1>
          <EmptyState
            title="No resumes found"
            description="You don't have shortlisted resumes yet."
            buttonText="Shortlist Resumes"
            href="/home/<USER>/job/list"
          />
        </div>
      ) : (
        <div className="space-y-6">
          <CompanyJobShortlist 
            paginatedJobs={paginatedJobs}
            currentPage={currentPage}
            totalPages={totalPages}
          />      
        </div>
      )}
    </>
  );
}


