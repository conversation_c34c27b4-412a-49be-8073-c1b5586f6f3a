import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const rawBody = await req.text(); // PayPal sends raw JSON string
  const headers = req.headers;

  // If you want to verify the webhook signature (advanced)
  // you can fetch PayPal's `transmission_id`, `cert_url`, `auth_algo`, etc. from headers here

  const webhookEvent = JSON.parse(rawBody);

  console.log('[PayPal Webhook] Event Type:', webhookEvent.event_type);

  switch (webhookEvent.event_type) {
    case 'BILLING.SUBSCRIPTION.CREATED':
      // Save subscription ID and user reference to DB
      console.log('Subscription created:', webhookEvent.resource.id);
      break;

    case 'BILLING.SUBSCRIPTION.CANCELLED':
      console.log('Subscription cancelled:', webhookEvent.resource.id);
      // Update user status in your DB
      break;

    case 'BILLING.SUBSCRIPTION.SUSPENDED':
      console.log('Subscription suspended:', webhookEvent.resource.id);
      // Optional: notify user or retry payment
      break;

    case 'PAYMENT.SALE.COMPLETED':
      console.log('Payment completed:', webhookEvent.resource);
      // Mark user as "paid" if first-time or recurring payment
      break;

    case 'PAYMENT.SALE.DENIED':
      console.log('Payment denied:', webhookEvent.resource);
      // Mark user as "paid" if first-time or recurring payment
      break;

    case 'PAYMENT.SALE.PENDING':
      console.log('Payment pending:', webhookEvent.resource);
      // Mark user as "paid" if first-time or recurring payment
      break;

    case 'PAYMENT.SALE.REFUNDED':
      console.log('Payment refunded:', webhookEvent.resource);
      // Mark user as "paid" if first-time or recurring payment
      break;

    case 'PAYMENT.SALE.REVERSED':
      console.log('Payment reversed:', webhookEvent.resource);
      // Mark user as "paid" if first-time or recurring payment
      break;

    default:
      console.log('Unhandled webhook:', webhookEvent.event_type);
  }

  return new NextResponse('Webhook received', { status: 200 });
}
