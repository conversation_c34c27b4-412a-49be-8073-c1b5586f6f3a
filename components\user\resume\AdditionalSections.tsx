import { Section } from '@/components/ui/section';
import { AdditionalSection } from '@/data/zod/resumeZod';
import { JsonToHTML } from '@/components/general/JsonToHTML';
import { normalizeHeaders } from '@/utils/stringHelpters';

/**
 * Individual additional section component
 */
function AdditionalSectionItem({
  section,
}: {
  section: Partial<AdditionalSection>;
}) {
  let detailsContent: React.ReactNode = null;
  if (typeof section.details === 'string' && section.details.trim() !== "") {
    try {
      const parsedDetails = JSON.parse(section.details); // `section.details` is now always a Tiptap JSON string
      detailsContent = <JsonToHTML json={parsedDetails} />;
    } catch (e) {
      console.warn("Additional section details could not be parsed as JSON, rendering as plain text:", section.details, e);
      detailsContent = <div className="text-sm text-[#6c737f] whitespace-pre-wrap">{section.details}</div>; // Fallback
    }
  } else if (Array.isArray(section.details) && section.details.length > 0) {
    // This `else if` block for Array.isArray(section.details) should no longer be necessary.
    console.warn("Additional section details received as an array, rendering as list (should be Tiptap JSON string):", section.details);
    detailsContent = <ul className="list-disc space-y-1 pl-4">{section.details.map((detail, idx) => <li key={idx} className="text-sm text-[#6c737f]">{detail}</li>)}</ul>;
  }

  return (
    <div className="space-y-2">
      <h3 className="text-lg font-bold">{normalizeHeaders(section.header || '')}</h3>
      {detailsContent && (
        <div className="text-sm text-[#6c737f]">{detailsContent}</div>
      )}
    </div>
  );
}

/**
 * Main additional sections component
 * Renders all additional sections like certifications, projects, etc.
 */
export function AdditionalSections({
  sections,
}: {
  sections: Partial<AdditionalSection>[];
}) {
  if (!sections || sections.length === 0) {
    return null;
  }

  return (
    <Section>
      <div className="space-y-6">
        {sections.map((section, idx) => (
          <article key={idx} role="article">
            <AdditionalSectionItem section={section} />
          </article>
        ))}
      </div>
    </Section>
  );
}
