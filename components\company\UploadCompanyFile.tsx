"use client";

import { SaveUserFile } from "@/actions/file/saveFile";
import { UserFile } from "@/types/customTypes";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";
import { Upload, X } from "lucide-react";
import { useState } from "react";
import "@/styles/uploadthing.css";
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

export function UploadCompanyFile({ companyId }: { companyId: String }) {
  const [open, setOpen] = useState(false);
  const [fileType, setFileType] = useState("RESUME");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="default"
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <Upload className="w-4 h-4" />
          Upload File
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[425px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between pt-6 px-6">
              <DialogTitle className="text-lg font-semibold">
                Upload File
              </DialogTitle>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 cursor-pointer"
                onClick={() => setOpen(false)}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
            <div className="px-6 pb-6">
              <DialogDescription>
                Upload recent file ten at a time. Upload in group by type: Resume, Job Post, Other
              </DialogDescription>
              <div className="grid gap-4 py-4 justify-center">
                <div>                    
                    <RadioGroup defaultValue="RESUME" onValueChange={(value) => setFileType(value)}>
                        <div className="flex items-center justify-between space-x-2">
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="RESUME" id="r1" />
                                <Label htmlFor="r1">RESUME</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="JOB" id="r2" />
                                <Label htmlFor="r2">JOB POST</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="OTHER" id="r3" />
                                <Label htmlFor="r3">OTHER</Label>
                            </div>
                        </div>
                    </RadioGroup>
                </div>
                <UploadDropzone
                  endpoint="companyDocumentUploader"
                  className="custom-class cursor-pointer w-full"
                  onClientUploadComplete={(res) => {
                    toast.success("Resume uploaded successfully.");

                    const data = res;

                    if (data && data.length > 0) {
                      // Process each file in the
                      setOpen(false);
                      const savePromises = data.map((file) => {
                        const resumeFile: UserFile = {
                          id: "",
                          userId: "userId",
                          companyId: companyId ? (companyId as string) : "",
                          fileUse: fileType,
                          fileType: file.name.split(".")[1].toUpperCase(),
                          fileName: file.name,
                          description: "Company file",
                          key: file.key,
                          url: file.ufsUrl,
                          fileSize: file.size,
                        };

                        return SaveUserFile(resumeFile);
                      });

                      // Wait for all files to be saved
                      Promise.all(savePromises)
                        .then(() => {
                          toast.success(
                            `${data.length} file(s) saved successfully.`
                          );
                        })
                        .catch((error) => {
                          console.error("Failed to save to database:", error);
                          toast.error("Failed to save file information");
                        });
                    }
                  }}
                  onUploadError={(error: Error) => {
                    toast.error(
                      `ERROR! A problem was encountered. ${error.message}`
                    );
                  }}
                  onUploadBegin={(name) => {
                    toast.info(`Uploading: ${name}`);
                  }}
                  onChange={(acceptedFiles) => {
                  }}
                  content={{
                    allowedContent({ ready, isUploading }) {
                      if (!ready) return "Checking what you allow";
                      if (isUploading) return "Uploading your file...";
                      return (
                        <>
                          PDF or Word Document
                          <br />
                          (max size of 1MB per file)
                        </>
                      );
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
