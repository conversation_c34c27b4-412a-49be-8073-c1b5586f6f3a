import { Skeleton } from "@/components/ui/skeleton";
import { Section } from "@/components/ui/section";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useResumeEvaluation } from "@/hooks/useResumeEvaluation";

// Helper function to determine color based on score
const getScoreColor = (score: number) => {
  if (score >= 90) return "bg-emerald-500";
  if (score >= 80) return "bg-green-500";
  if (score >= 70) return "bg-yellow-500";
  if (score >= 60) return "bg-orange-500";
  return "bg-red-500";
};

// Score circle component
const ScoreCircle = ({
  score,
  label,
}: {
  score: number | undefined;
  label: string;
}) => {
  // Default to 0 if score is undefined
  const displayScore = score ?? 0;

  return (
    <div className="flex flex-col items-center">
      <div
        className={`${getScoreColor(displayScore)} text-white rounded-full w-10 h-10 flex items-center justify-center font-bold text-sm`}
      >
        {displayScore}
      </div>
      <span className="text-xs mt-1 text-gray-600">{label}</span>
    </div>
  );
};

// List component for strengths, improvements, etc.
const EvaluationList = ({
  items,
  title,
}: {
  items: string[] | undefined;
  title: string;
}) => (
  <div className="mb-4">
    <h4 className="font-medium text-sm mb-2">{title}</h4>
    <ul className="list-disc pl-5 text-sm space-y-1">
      {items?.map((item, index) => (
        <li key={index} className="text-gray-700">
          {item}
        </li>
      )) || <li className="text-gray-500">No data available</li>}
    </ul>
  </div>
);

export function ResumeEvaluation() {
  const { evaluation, isLoading, isError } = useResumeEvaluation();

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <Skeleton className="h-4 w-1/3" />
        <div className="flex justify-between">
          <Skeleton className="h-14 w-14 rounded-full" />
          <Skeleton className="h-14 w-14 rounded-full" />
          <Skeleton className="h-14 w-14 rounded-full" />
          <Skeleton className="h-14 w-14 rounded-full" />
        </div>
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-20 w-full" />
      </div>
    );
  }

  if (isError || !evaluation) {
    return (
      <div className="p-4">
        <p className="text-sm text-red-500">
          Unable to load resume evaluation. Please try again later.
        </p>
      </div>
    );
  }

  const { scores, assessment, evaluation_details } = evaluation;

  return (
    <Section className="p-4 w-full">
      <div className="flex md:flex-row md:items-center justify-between mb-0">
        <div>
          <h3 className="text-lg font-semibold">Resume Evaluation</h3>
          <em className="text-sm text-gray-500">AI analyzed your resume based on professional recruiting criteria.</em>
        </div>
        <div>
          {/* Scores */}
          {scores && (
            <div className="flex items-center justify-between gap-2 mt-2 md:mt-0">
              <ScoreCircle score={scores.overall} label="Overall" />
              <ScoreCircle
                score={scores.experience_quality}
                label="Experience"
              />
              <ScoreCircle score={scores.education} label="Education" />
              <ScoreCircle score={scores.skills} label="Skills" />
              <ScoreCircle score={scores.completeness} label="Completeness" />
            </div>
          )}
        </div>
      </div>

      <Separator className="my-0" />

      {/* Assessment */}
      {assessment && (
        <div className="mb-2">
          <ScrollArea className="h-[200px] pr-4">
            <EvaluationList items={assessment.strengths} title="Strengths" />
            <EvaluationList
              items={assessment.improvements}
              title="Areas for Improvement"
            />
            <EvaluationList
              items={assessment.industry_recommendations}
              title="Industry Recommendations"
            />
          </ScrollArea>
        </div>
      )}

      <Separator className="my-2" />

      {/* Detailed Evaluation */}
      {evaluation_details && (
        <div className="mb-2">
          <ScrollArea className="h-[200px] pr-4">
            <div className="text-sm space-y-3">
              <div>
                <h4 className="font-medium mb-1">Experience</h4>
                <p className="text-gray-700">{evaluation_details.experience}</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Education</h4>
                <p className="text-gray-700">{evaluation_details.education}</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Skills</h4>
                <p className="text-gray-700">{evaluation_details.skills}</p>
              </div>
            </div>
          </ScrollArea>
        </div>
      )}
    </Section>
  );
}

