{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clear-sessions": "ts-node prisma/clear-sessions.ts"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.8", "@arcjet/next": "^1.0.0-beta.8", "@auth/prisma-adapter": "^2.9.1", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.10.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.79.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@uploadthing/react": "^7.3.1", "ai": "^4.3.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "i": "^0.3.7", "inngest": "^3.38.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.511.0", "mammoth": "^1.9.1", "next": "^15.3.2", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "pdf-parse-new": "^1.4.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-documents": "^1.2.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-spinners": "^0.17.0", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.1", "uploadthing": "^7.7.2", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.36"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.15.24", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "eslint-config-next": "^15.3.2", "eslint-plugin-prettier": "^5.4.0", "prisma": "^6.10.1", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}