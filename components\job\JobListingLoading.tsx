import { Loader2 } from "lucide-react";
import { Card } from "../ui/card";
import { Skeleton } from "../ui/skeleton";

export function JobListingLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(9)].map((_, index) => ( // Changed to 9 skeletons for better grid representation
        <Card className="p-6" key={index}>
        <div className="flex items-start gap-4">
          <Skeleton className="size-14 rounded"/> <Loader2 className="size-4 text-gray-200 animate-spin" />
          <div className="flex-1 space-y-3">
            <Skeleton className="h-5 w-[300px]"/>
            <Skeleton className="h-5 w-[200px]"/>

            <div className="flex gap-4 mt-4">
                <Skeleton className="h-4 w-[120px]"/>
                <Skeleton className="h-4 w-[120px]"/>
                <Skeleton className="h-4 w-[120px]"/>
            </div>
          </div>
        </div>
      </Card>
      ))}
    </div>
  );
}
