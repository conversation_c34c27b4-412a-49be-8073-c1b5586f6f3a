import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useMemo } from 'react';
import { AddButton } from './AddButton';
import { WorkExperienceField } from './WorkExperienceField';
import { EducationField } from './EducationField';
import { SkillField } from './SkillField';
import { AddSkillDialog } from './AddSkillDialog';
import { AdditionalSectionField } from './AdditionalSectionField'; // Import the new component
import { TextEditor } from '@/components/general/TextEditor';
import { ResumeData } from "@/lib/resume/resumeActions";
import { v4 as uuidv4 } from 'uuid';

export const EditResume = ({
  resume,
  onChangeResume,
}: {
  resume: ResumeData;
  onChangeResume: (newResume: ResumeData) => void;
}) => {
  const [isAddSkillDialogOpen, setIsAddSkillDialogOpen] = useState(false);

  const handleAddSkill = (skillToAdd: string) => {
    if (resume.standardFields.skills.includes(skillToAdd)) {
      toast.warning('This skill is already added.');
    } else {
      onChangeResume({
        ...resume,
        standardFields: {
          ...resume.standardFields,
          skills: [...(resume.standardFields?.skills || []), skillToAdd],
        }
      });
      toast.success('Skill added successfully.');
    }
  };

  const enrichedExperience = useMemo(() => (resume.standardFields?.experience || []).map((work) => ({
    ...work,
    _tempId: work._tempId || uuidv4(), // Ensure _tempId exists or generate one
  })), [resume.standardFields?.experience]);

  const enrichedEducation = useMemo(() => (resume.standardFields?.education || []).map((edu) => ({
    ...edu,
    _tempId: edu._tempId || uuidv4(), // Ensure _tempId exists or generate one
  })), [resume.standardFields?.education]);

  const enrichedAdditionalSections = useMemo(() => (resume.additionalSections || []).map((section) => ({
    ...section,
    _tempId: section._tempId || uuidv4(), // Ensure _tempId exists or generate one
  })), [resume.additionalSections]);

  // Adapt the 'field' prop for Summary TextEditor
  const fieldForSummaryEditor = useMemo(() => ({
    value: resume.standardFields?.summary?.content || "",
    onChange: (newContent: string) => {
      onChangeResume({
        ...resume,
        standardFields: { ...resume.standardFields, summary: { ...resume.standardFields?.summary, content: newContent } },
      });
    },
    onBlur: () => {},
    ref: () => {},
  }), [resume.standardFields?.summary?.content, onChangeResume, resume]);



  return (
    <section
      className="mx-auto w-full max-w-2xl space-y-8 bg-white my-8"
      aria-label="Resume Content editing"
    >
      <div className="flex flex-col gap-2">
        <h2 className="text-xl font-bold">Header</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2 col-span-2 md:col-span-1">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700">
              Name
            </Label>
            <Input
              type="text"
              id="name"
              value={resume?.standardFields?.header?.name || ''}
              onChange={(e) => {
                onChangeResume({
                  ...resume,
                  standardFields: {
                    ...resume.standardFields,
                    header: {
                      ...resume.standardFields?.header,
                      name: e.target.value,
                    },
                  },
                });
              }}
              placeholder="Your full name"
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label
              htmlFor="address"
              className="text-sm font-medium text-gray-700"
            >
              Location
            </Label>
            <Input
              type="text"
              id="address"
              value={resume.standardFields?.header?.address || ''}
              onChange={(e) => {
                onChangeResume({
                  ...resume,
                  standardFields: {
                    ...resume.standardFields,
                    header: {
                      ...resume.standardFields?.header,
                      address: e.target.value,
                    },
                  },
                });
              }}
              placeholder="Your location"
            />
          </div>

          <div className="flex flex-col gap-2 col-span-2">
            <Label
              htmlFor="shortAbout"
              className="text-sm font-medium text-gray-700"
            >
              Short About
            </Label>
            <textarea
              className="w-full p-2 border rounded-md font-mono text-sm"
              value={resume.standardFields?.header?.shortAbout || ''}
              onChange={(e) => {
                onChangeResume({
                  ...resume,
                  standardFields: {
                    ...resume.standardFields,
                    header: {
                      ...resume.standardFields?.header,
                      shortAbout: e.target.value,
                    },
                  },
                });
              }}
              rows={4}
              placeholder="Brief description about yourself..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 col-span-2">
            <div className="flex flex-col gap-2">
              <Label
                htmlFor="email"
                className="text-sm font-medium text-gray-700"
              >
                Email
              </Label>
              <Input
                type="email"
                id="email"
                value={resume.standardFields?.header?.email || ''}
                onChange={(e) => {
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      header: {
                        ...resume.standardFields?.header,
                        email: e.target.value,
                      },
                    },
                  });
                }}
                placeholder="Your email address"
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label
                htmlFor="phone"
                className="text-sm font-medium text-gray-700"
              >
                Phone Number
              </Label>
              <Input
                type="tel"
                id="phone"
                value={resume.standardFields?.header?.phone?.[0] || ''}
                onChange={(e) => {
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      header: {
                        ...resume.standardFields?.header,
                        phone: [e.target.value], // Store as an array with one item
                      },
                    },
                  });
                }}
                placeholder="Your phone number"
              />
            </div>
          </div>

          <div className="flex flex-col gap-2 col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              Social Links
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                {
                  id: 'website',
                  label: 'Website',
                  prefix: '',
                  placeholder: 'your-website.com',
                  key: 'website',
                },
                {
                  id: 'github',
                  label: 'GitHub',
                  prefix: 'github.com/',
                  placeholder: 'username',
                  key: 'github',
                },
                {
                  id: 'linkedin',
                  label: 'LinkedIn',
                  prefix: 'linkedin.com/in/',
                  placeholder: 'linkedin profile',
                  key: 'linkedin',
                },
                {
                  id: 'xaccount',
                  label: 'Twitter/X',
                  prefix: 'x.com/',
                  placeholder: 'your handle',
                  key: 'xaccount',
                },
              ].map(({ id, label, prefix, placeholder, key }) => (
                <div key={id} className="flex flex-col gap-2">
                  <Label htmlFor={id} className="text-sm text-gray-600">
                    {label}
                  </Label>
                  <div className="flex items-center">
                    {prefix && (
                      <span className="text-sm text-gray-500 mr-2">
                        {prefix}
                      </span>
                    )}
                    <Input
                      type="text"
                      id={id}
                      value={
                        (resume?.standardFields?.header?.[
                          key as keyof typeof resume.standardFields.header
                        ] as string) || ''
                      }
                      onChange={(e) => {
                        onChangeResume({
                          ...resume,
                          standardFields: {
                            ...resume.standardFields,
                            header: {
                              ...resume.standardFields?.header,
                              [key as keyof typeof resume.standardFields.header]: e.target.value,
                            }
                          },
                        });
                      }}
                      placeholder={placeholder}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-6">
        {/* Summary Section */}
        <div className="space-y-2">
          <h2 className="text-xl font-bold">Summary</h2>
          <TextEditor
            key={`summary-editor`} // Add key if needed for re-mount
            field={fieldForSummaryEditor}
            // You might want to pass a specific minHeight prop if TextEditor supports it
          />
        </div>

        {/* Work Experience Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold">Work Experience</h2>
          <div className="space-y-4">
            {enrichedExperience.map((work, index) => (
               <WorkExperienceField
                key={work._tempId}
                work={work}
                index={index}
                onUpdate={(index, updatedWork) => {
                  const newWorkExperience = [...(resume.standardFields?.experience || [])];
                  newWorkExperience[index] = updatedWork;
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      experience: newWorkExperience,
                    },
                  });
                }}
                onDelete={(index) => {
                  const newWorkExperience = [...(resume.standardFields?.experience || [])];
                  newWorkExperience.splice(index, 1);
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      experience: newWorkExperience,
                    },
                  });
                }}
              />
            ))}
            <AddButton
              label="Add Work Experience"
              onClick={() => {
                onChangeResume({
                  ...resume,
                  standardFields: {
                    ...resume.standardFields,
                    experience: [
                      ...(resume.standardFields?.experience || []),
                      {
                        title: '',
                        company: '',
                        description: '', // Initialize as empty string for TextEditor
                        location: '',
                        highlights: [],
                        technologies: [],
                        employmentType: '',
                        _tempId: uuidv4(), // Add _tempId for new items
                        startDate: '',
                        endDate: '',
                      },
                    ],
                  }
                });
              }}
            />
          </div>
        </div>

        {/* Education Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold">Education</h2>
          <div className="space-y-4">
            {enrichedEducation.map((edu, index) => (
              <EducationField
                key={edu._tempId} // Use stable _tempId
                edu={edu}
                index={index}
                onUpdate={(index, updatedEdu) => {
                  const newEducation = [...(resume.standardFields?.education || [])];
                  newEducation[index] = updatedEdu;
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      education: newEducation,
                    },
                  });
                }}
                onDelete={(index) => {
                  const newEducation = [...(resume.standardFields?.education || [])];
                  newEducation.splice(index, 1);
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      education: newEducation,
                    },
                  });
                }}
              />
            ))}
            <AddButton
              label="Add Education"
              onClick={() => {
                onChangeResume({
                  ...resume,
                  standardFields: {
                    ...resume.standardFields,
                    education: [
                      ...(resume.standardFields?.education || []),
                      { 
                        degree: '', 
                        institution: '', 
                        location: '', 
                        details: '', 
                        _tempId: uuidv4(), // Add _tempId for new items
                        gpa: '', 
                        honors: [], 
                        startDate: '', 
                        endDate: '' },
                    ],
                  }
                });
              }}
            />
          </div>
        </div>

        
        {/* Additional Section */}        
        <div className="space-y-4">
          <h2 className="text-xl font-bold">Additional Sections</h2>
          <div className="space-y-4">
            {enrichedAdditionalSections.map((section, index) => (
              <AdditionalSectionField
                key={section._tempId}
                section={section}
                index={index}
                onUpdate={(index, updatedSection) => {
                  const newSections = [...(resume.additionalSections || [])];
                  newSections[index] = updatedSection;
                  onChangeResume({
                    ...resume,
                    additionalSections: newSections,
                  });
                }}
                onDelete={(index) => {
                  const newSections = [...(resume.additionalSections || [])];
                  newSections.splice(index, 1);
                  onChangeResume({
                    ...resume,
                    additionalSections: newSections,
                  });
                }}
              />
            ))}
            <AddButton
              label="Add Additional Section"
              onClick={() => {
                onChangeResume({
                  ...resume,
                  additionalSections: [
                    ...(resume.additionalSections || []),
                    {
                      // id: uuidv4(), // Consider if ID is needed on client or generated on save
                      header: '',
                      details: '', // Initialize as empty string for TextEditor
                      _tempId: uuidv4(), // Add _tempId for new items
                    },
                  ],
                });
              }}
            />
          </div>
        </div>

        {/* Skills Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold">Skills</h2>
          <div className="flex flex-wrap gap-2">
            {(resume.standardFields?.skills || []).map((skill, index) => (
              <SkillField
                key={index}
                skill={skill}
                index={index}
                onUpdate={(index, updatedSkill) => {
                  const newSkills = [...(resume.standardFields?.skills || [])];
                  newSkills[index] = updatedSkill;
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      skills: newSkills,
                    },
                  });
                }}
                onDelete={(index) => {
                  const newSkills = [...(resume.standardFields?.skills || [])];
                  newSkills.splice(index, 1);
                  onChangeResume({
                    ...resume,
                    standardFields: {
                      ...resume.standardFields,
                      skills: newSkills,
                    },
                  });
                }}
              />
            ))}
          </div>
          <AddButton
            label="Add Skill"
            onClick={() => setIsAddSkillDialogOpen(true)}
          />
          <AddSkillDialog
            open={isAddSkillDialogOpen}
            onOpenChange={setIsAddSkillDialogOpen}
            onAddSkill={handleAddSkill}
          />
        </div>

      </div>
    </section>
  );
};
