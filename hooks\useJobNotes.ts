import { getJobNotes } from "@/actions/job/getJob";
import { useQuery } from "@tanstack/react-query";

const fetchJobNotes = async (
  jobId: string,
  resumeId: string
): Promise<any[]> => {
  try {
    const data = await getJobNotes(jobId, resumeId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

export function useJobNotes(
  jobId: string,
  resumeId: string
) {
  const jobNotesQuery = useQuery({
    queryKey: ["jobNotes", jobId, resumeId],
    queryFn: () => {
      if (!jobId && !resumeId) return null;
      return fetchJobNotes(jobId, resumeId);
    },
    enabled: !!jobId && !!resumeId,
    staleTime: 30000,
  });

  return {
    jobNotesQuery
  };
}
