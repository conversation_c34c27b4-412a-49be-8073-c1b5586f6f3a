import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MainPagination } from "@/components/general/MainPagination";
import { Suspense } from "react";
import { getJobResumeMatches } from "@/actions/job/getJob";
import { JobMatchesInline } from "./JobMatchesInline";
import { ScoreFilterDropdownServer } from "./ScoreFilterDropdownServer";

interface CompanyJobMatchesProps {
  paginatedJobs: any[];
  currentPage: number;
  totalPages: number;
  currentScore?: string; // Score from URL (e.g., "70", "all", undefined)
  allSearchParams: { [key: string]: string | string[] | undefined };
}

export function CompanyJobMatches({
  paginatedJobs,
  currentPage,
  totalPages,
  currentScore,
  allSearchParams,
}: CompanyJobMatchesProps) {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Job Matches</CardTitle>
          <CardDescription>
            <div className="flex items-center justify-between gap-2 pb-4">
              <span>
                Jobs that match resumes with a score set by you (default 70+).
              </span>
              <ScoreFilterDropdownServer
                currentScoreFromUrl={currentScore}
                basePath="/home/<USER>/matches"
                allSearchParams={allSearchParams}
              />
            </div>            
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader className="font-bold">
              <TableRow>
                <TableHead style={{ width: '10%' }}>Posted</TableHead>
                <TableHead style={{ width: '32.5%' }}>Job</TableHead>
                <TableHead style={{ width: '20%' }}>Name</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Match</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Evaluation</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Resume</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Shortlist</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedJobs.map((job, index) => (
                <TableRow 
                  key={job.id} 
                  className={index % 2 === 1 ? "bg-muted/30" : ""}
                >
                  <TableCell style={{ width: '10%', verticalAlign: 'top' }}>{job.createdAt.toLocaleDateString()}</TableCell>
                  <TableCell className="font-medium overflow-hidden text-ellipsis whitespace-nowrap min-w-0" style={{ width: '32.5%', verticalAlign: 'top' }}>{job.jobTitle}</TableCell>
                  <TableCell colSpan={6} style={{ padding: '0' }}>
                    <Suspense fallback={<div className="p-2">Loading job matches...</div>}>
                      <JobMatchesWrapper
                        jobId={job.id}
                        rowIndex={index}
                        scoreFilter={currentScore}
                      />
                    </Suspense>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <MainPagination totalPages={totalPages} currentPage={currentPage} />
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}

// This wrapper component fetches the resumes using the server action
async function JobMatchesWrapper({
  jobId,
  rowIndex,
  scoreFilter,
}: {
  jobId: string;
  rowIndex: number;
  scoreFilter?: string; // Score from URL (e.g., "70", "all", undefined)
}) {
  let actualMinScore: number | undefined;
  if (scoreFilter === "all") actualMinScore = undefined;
  else if (scoreFilter) actualMinScore = parseInt(scoreFilter);
  else actualMinScore = 70; // Default to 70 if scoreFilter is undefined

  const jobMatches = await getJobResumeMatches(jobId, "", actualMinScore);

  return (
    <JobMatchesInline 
      jobId={jobId}
      jobTitle={jobMatches[0]?.jobPost?.jobTitle || ""} 
      jobMatches={jobMatches}
      isOddRow={rowIndex % 2 === 1}
    />
  );
}
