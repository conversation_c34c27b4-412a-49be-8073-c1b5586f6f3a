"use client";

interface ObjectDocumentViewerProps {
  documentUrl: string;
  documentType: 'application/pdf' | 'application/msword' | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  title?: string;
  width?: string;
  height?: string;
}

export const DocumentViewerPdf = ({
  documentUrl,
  documentType,
  title = "Document Viewer",
  width = "100%",
  height = "1000px"
}: ObjectDocumentViewerProps) => {
  return (
    <object
      data={documentUrl}
      type={documentType}
      width={width}
      height={height}
      aria-label={title}
    >
      <p>
        It appears your browser does not support embedding this file type.
        You can <a href={documentUrl} download>download the document</a> instead.
      </p>
      {/* Fallback for older IE versions */}
      {/* <embed src={documentUrl} type={documentType} width={width} height={height} /> */}
    </object>
  );
};