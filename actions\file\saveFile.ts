"use server";

import { UserFile } from "@/types/customTypes";
import { auth } from "@/lib/auth/auth";
import { saveFileData } from "@/data/site/file";
import { redirect } from "next/navigation";
import { updateUserProfilePictureData } from "@/data/user/user";
import { updateUserResumePictureData } from "@/data/user/resume";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const SaveUserFile = async (userFile: UserFile) => {
  const session = await auth();

 if (!session?.user?.id) {
    // Or handle as per your middleware strategy, e.g., throw new Error("Unauthorized");
    // This assumes middleware might not cover this specific action directly,
    // or as a defense-in-depth. If covered by middleware, this error indicates an issue.
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  if (!userFile) {
    return { error: "Missing user userFile." };
  }

  try {
    userFile.userId = session?.user?.id as string;
    userFile.companyId = session?.user?.companyId as string;

    const result = await saveFileData(userFile);

    if (result.success) {
      return {
        success: result.success,
        fileResult: result.file,
      };
    } else {
      return {
        error: result.error,
        fileResult: result.file,
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: error,
      fileResult: null,
    };
  }
};

export const SaveUserProfilePicture = async (
  type: number,
  url: string,
  resumeId?: string
) => {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  if (!url) {
    return { error: "Missing user picture url." };
  }

  try {
    const userId = session?.user?.id as string;
    let result = null;
    if (type === 1) {
      result = await updateUserProfilePictureData(userId, url);
    } else if (type === 2) {
      result = await updateUserResumePictureData(resumeId as string, url);
    }

    if (result) {
      return result;
    }

    return null;
  } catch (error) {
    console.error(error);
    return null;
  }
};
