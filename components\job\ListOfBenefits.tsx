import {
  <PERSON><PERSON>case,
  Users,
  Eye,
  SmileIcon as <PERSON><PERSON>,
  Heart,
  Umbrella,
  Clock,
  Calendar,
  Building,
  GraduationCap,
  Dumbbell,
  Brain,
  Home,
  Bitcoin,
  UserCircle,
  PieChart,
  Coins,
  MonitorOff,
  Shield,
  UserPlus,
  TicketsPlane,
  HeartHandshake
} from "lucide-react";
import { FaHeartbeat, FaMoneyBill } from "react-icons/fa";
import { MdBabyChangingStation, MdFamilyRestroom, MdOutlineOnDeviceTraining, MdOutlineSick, MdOutlineVolunteerActivism, MdPregnantWoman } from "react-icons/md";
import { PiBuildingOffice, PiCertificate } from "react-icons/pi";
import { VscRemoteExplorer } from "react-icons/vsc";
import { GrDocumentPerformance } from "react-icons/gr";
import { LiaCertificateSolid } from "react-icons/lia";

interface Benefit {
  id: string;
  label: string;
  icon: React.ReactNode;
}

export const benefits: Benefit[] = [
  {
    id: "vision",
    label: "Vision insurance",
    icon: <Eye className="w-3 h-3" />,
  },
  {
    id: "dental",
    label: "Dental insurance",
    icon: <Tooth className="w-3 h-3" />,
  },
  {
    id: "health",
    label: "Health Insurance (HMO)",
    icon: <FaHeartbeat className="w-3 h-3" />,
  },
  {
    id: "life",
    label: "Life Insurance",
    icon: <Heart className="w-3 h-3" />,
  },
  {
    id: "gym",
    label: "Free gym membership",
    icon: <Dumbbell className="w-3 h-3" />,
  },
  {
    id: "mental_wellness",
    label: "Mental wellness budget",
    icon: <Brain className="w-3 h-3" />,
  },
  {
    id: "vacation",
    label: "Vacation Leave",
    icon: <TicketsPlane className="w-3 h-3" />,
  },
  {
    id: "sick",
    label: "Sick Leave",
    icon: <MdOutlineSick className="w-3 h-3" />,
  },
  {
    id: "maternity",
    label: "Maternity Benefits",
    icon: <MdPregnantWoman className="w-3 h-3" />,
  },
  {
    id: "paternity",
    label: "Paternity Benefits",
    icon: <MdBabyChangingStation className="w-3 h-3" />,
  },
  {
    id: "deminimis",
    label: "Non-Taxable Allowance (De-minimis)",
    icon: <FaMoneyBill className="w-3 h-3" />,
  },
  {
    id: "trainings",
    label: "Sponsored Trainings",
    icon: <MdOutlineOnDeviceTraining className="w-3 h-3" />,
  },
  {
    id: "certification",
    label: "Sponsored Certifications",
    icon: <PiCertificate className="w-3 h-3" />,
  },
  {
    id: "flexiblework",
    label: "Flexible Work Arrangements",
    icon: <VscRemoteExplorer className="w-3 h-3" />,
  },
  {
    id: "performance",
    label: "Performance Recognitions",
    icon: <GrDocumentPerformance className="w-3 h-3" />,
  },
  {
    id: "retirement",
    label: "Retirement Program",
    icon: <LiaCertificateSolid className="w-3 h-3" />,
  },
  {
    id: "assistance",
    label: "Employee Assistance Program",
    icon: <HeartHandshake className="w-3 h-3" />,
  },
  {
    id: "workenvironment",
    label: "Great Work Environment",
    icon: <PiBuildingOffice className="w-3 h-3" />,
  },
  {
    id: "family",
    label: "Family Friendly",
    icon: <MdFamilyRestroom className="w-3 h-3" />,
  },
  {
    id: "wellbeing",
    label: "Wellbeing Programs",
    icon: <MdOutlineVolunteerActivism className="w-3 h-3" />,
  },
  { id: "401k", label: "401(k)", icon: <Briefcase className="w-3 h-3" /> },
  {
    id: "distributed",
    label: "Distributed team",
    icon: <Users className="w-3 h-3" />,
  },
  {
    id: "unlimited_vacation",
    label: "Unlimited vacation",
    icon: <Umbrella className="w-3 h-3" />,
  },
  { id: "pto", label: "Paid time off", icon: <Clock className="w-3 h-3" /> },
  {
    id: "four_day",
    label: "4 day workweek",
    icon: <Calendar className="w-3 h-3" />,
  },
  {
    id: "401k_matching",
    label: "401k matching",
    icon: <Coins className="w-3 h-3" />,
  },
  {
    id: "company_retreats",
    label: "Company retreats",
    icon: <Building className="w-3 h-3" />,
  },
  {
    id: "coworking_budget",
    label: "Coworking budget",
    icon: <Building className="w-3 h-3" />,
  },
  {
    id: "learning_budget",
    label: "Learning budget",
    icon: <GraduationCap className="w-3 h-3" />,
  },
  {
    id: "home_office",
    label: "Home office budget",
    icon: <Home className="w-3 h-3" />,
  },
  {
    id: "crypto",
    label: "Pay in crypto",
    icon: <Bitcoin className="w-3 h-3" />,
  },
  {
    id: "pseudonymous",
    label: "Pseudonymous",
    icon: <UserCircle className="w-3 h-3" />,
  },
  {
    id: "profit_sharing",
    label: "Profit sharing",
    icon: <PieChart className="w-3 h-3" />,
  },
  {
    id: "equity",
    label: "Equity compensation",
    icon: <Coins className="w-3 h-3" />,
  },
  {
    id: "no_whiteboard",
    label: "No whiteboard interview",
    icon: <MonitorOff className="w-3 h-3" />,
  },
  {
    id: "no_monitoring",
    label: "No monitoring system",
    icon: <Shield className="w-3 h-3" />,
  },
  {
    id: "hire_old_young",
    label: "We hire old (and young)",
    icon: <UserPlus className="w-3 h-3" />,
  },
];
