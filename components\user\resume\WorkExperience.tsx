import { Section } from '@/components/ui/section';
import { ResumeDataSchemaType } from '@/data/zod/resumeZod';
import { JsonToHTML } from '@/components/general/JsonToHTML';
import { normalizeHeaders } from '@/utils/stringHelpters';

export function WorkExperience({
  work,
}: {
  work: ResumeDataSchemaType['standardFields']['experience'];
}) {
  // Assuming item.description can be a Tiptap JSON string or fallback to string[]
  const renderDescription = (description: string | string[] | undefined) => {
    if (typeof description === 'string' && description.trim() !== "") {
      try {
        const parsedDescription = JSON.parse(description);
        return <JsonToHTML json={parsedDescription} />;
      } catch (e) {
        console.warn("Work experience description could not be parsed as JSON, rendering as plain text:", description, e);
        return <div className="whitespace-pre-wrap">{description}</div>;
      }
    } else if (Array.isArray(description) && description.length > 0) {
      // Fallback for old array format or if it's intentionally an array of plain strings
      console.warn("Work experience description received as an array, rendering as list (should be Tiptap JSON string):", description);
      return <ul className="list-disc pl-5 space-y-1">{description.map((desc, index) => <li key={index}>{desc}</li>)}</ul>;
    }
    return null;
  };
  return (
    <Section>
      <h2 className="text-lg font-bold" id="work-experience">
        Work Experience
      </h2>
      <div
        className="flex flex-col gap-4"
        role="feed"
        aria-labelledby="work-experience"
      >
        {work.map((item) => {
          return (
            <div
              key={`${item.company || ''}${item.location || ''}${item.title || ''}`}
              className="font-mono flex flex-col justify-start items-start gap-1 print:mb-4"
            >
              <div className="flex flex-wrap justify-between items-start self-stretch gap-2">
                <div className="flex flex-wrap justify-start items-center gap-2">
                  <p className="text-base font-semibold text-left text-[#050914]">
                    {normalizeHeaders(item.title || '')}
                  </p>
                  <div className="flex justify-center items-center relative overflow-hidden gap-2.5 px-[7px] py-0.5 rounded bg-[#eeeff0]">
                    <p className="text-[12px] font-semibold text-center text-[#54575e]">
                      {normalizeHeaders(item.location || '')}
                    </p>
                  </div>
                </div>
                <p className="text-sm text-right text-[#54575e]">
                  {item.startDate || ''}
                  {item.endDate ? ` - ${item.endDate}` : ''}
                </p>
              </div>
              <div className="flex flex-col justify-start items-start relative gap-1.5">
                <p className="self-stretch text-sm font-medium text-left text-[#54575e] font-mono capitalize flex flex-wrap gap-1">
                  <span>{normalizeHeaders(item.company || '')}</span>
                  {item.company && item.employmentType && <span>·</span>}
                  <span>{normalizeHeaders(item.employmentType || '')}</span>
                </p>
                <div className="self-stretch text-sm font-medium text-left text-[#6c737f]">
                  {/* Render description which might be Tiptap JSON string or string[] */}
                  {renderDescription(item.description)}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Section>
  );
}
