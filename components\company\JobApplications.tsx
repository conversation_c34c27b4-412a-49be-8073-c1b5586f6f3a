import { getJobApplicants } from "@/actions/job/getJob";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { EyeIcon } from "lucide-react";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import Image from "next/image";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { normalizeHeaders } from "@/utils/stringHelpters";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export async function JobApplications({ jobId, userId }: { jobId: string, userId?: string }) {
  const data = await getJobApplicants(jobId);
  let userUrl = "";

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        No applications yet
      </div>
    );
  }

  if (userId && userId.length > 0) {
    userUrl = "/home/<USER>/resume/";
  } else {
    userUrl = "/home/<USER>/resume/";
  }

  // Calculate table height - approximately 50px per row for 10 rows
  const tableMaxHeight = data.length > 10 ? "500px" : "auto";

  return (
    <>
      <div className="w-full">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead style={{ width: "45%" }}>Name</TableHead>
              <TableHead style={{ width: "15%" }}>Match</TableHead>
              <TableHead style={{ width: "15%" }}>Evaluation</TableHead>
              <TableHead style={{ width: "15%" }}>Resume</TableHead>
              <TableHead style={{ width: "10%" }}>Shortlist</TableHead>
            </TableRow>
          </TableHeader>
        </Table>

        <ScrollArea className={`w-full ${data.length > 5 ? "h-[300px]" : ""}`}>
          <Table>
            <TableBody>
              {data.map((job) => (
                <TableRow key={job.id}>
                  <TableCell
                    style={{ width: "45%" }}
                    className="flex items-center gap-2"
                  >
                    <Image
                      src={job.user.resumes?.picture || "/icons/profile.png"}
                      alt={job.user.firstName || ""}
                      width={28}
                      height={28}
                      className="rounded-full"
                    />
                    {job.user.firstName}&nbsp;{job.user.lastName}
                  </TableCell>
                  <TableCell style={{ width: "15%" }}>
                    <MatchScoreCircle
                      score={
                        job.user.resumes?.resumeMatches?.[0]?.overall_score
                      }
                    />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-0">
                    <ResumeMatchDialog
                      resumeMatch={job.user.resumes?.resumeMatches?.[0]}
                      applicantName={`${job.user.firstName} ${job.user.lastName}`}
                    />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-0">
                    <a
                      href={`${APP_URL}${userUrl}${job.user?.resumes?.id}`}
                      target="_blank"
                      className="text-primary hover:underline cursor-pointer"
                      title="View Resume"
                    >
                      <Button
                        variant="ghost"
                        className="text-primary hover:underline cursor-pointer"
                        title="View Resume"
                      >
                        <EyeIcon className="w-6 h-6" />
                      </Button>
                    </a>
                  </TableCell>
                  <TableCell
                    style={{ width: "10%" }}
                    className="pl-4 cursor-pointer"
                  >
                        {userId && userId.length > 0 ? (
                            job.user?.isShortlisted ? "Yes" : "No"
                        ) : (
                            <ShortlistCheckbox
                            jobId={jobId}
                            resumeId={job.user.resumes?.id || ""}
                            initialShortlisted={job.user?.isShortlisted || false}
                            />
                        )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    </>
  );
}
