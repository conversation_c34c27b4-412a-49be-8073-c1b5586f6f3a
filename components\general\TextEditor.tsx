import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { TextEditorMenuBar } from "./TextEditorMenuBar";
import TextAlign from "@tiptap/extension-text-align";
import Typography from "@tiptap/extension-typography";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Underline from "@tiptap/extension-underline";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import { useEffect, useMemo } from "react";
import debounce from 'lodash.debounce';

interface TextEditorFieldProps {
  value: string; // Expects a string (plain text, HTML, or JSON string from Tiptap)
  onChange: (value: string) => void; // Callback with the new content (JSON string from Tiptap)
  onBlur?: (...event: any[]) => void; // Optional: For compatibility if a full react-hook-form field is passed
  ref?: React.Ref<any>; // Optional: For compatibility
}

interface TextEditorProps {
  field: TextEditorFieldProps;
}

export function TextEditor({ field }: TextEditorProps) {
  // console.log("TextEditor received field.value:", field.value); // For debugging

  // Determine initial content: try to parse as JSON, otherwise use as string (for plain text/HTML)
  let initialContent: string | Record<string, any> = field.value || ""; 
  if (typeof field.value === 'string') {
    const trimmedValue = field.value.trim();
    if (trimmedValue.startsWith('{') && trimmedValue.endsWith('}')) { // Basic check for JSON
      try {
        const parsed = JSON.parse(trimmedValue);
        // Optionally, add more specific checks for Tiptap's doc structure if needed
        if (parsed && typeof parsed === 'object') { // && parsed.type === 'doc'
          initialContent = parsed;
        }
      } catch (error) {
        // Not valid JSON, or not the expected structure. Tiptap will treat `initialContent` (which is field.value string) as plain text/HTML.
        // If it's plain text, Tiptap's StarterKit should handle it by creating paragraphs.
        // If it's invalid JSON that's not plain text/HTML, Tiptap might render an empty editor.
        initialContent = field.value; // Ensure the string value is used if JSON parsing fails
        console.warn("TextEditor: field.value looked like JSON but failed to parse or was not Tiptap format. Treating as plain text/HTML. Value:", field.value);
      }
    }
  }

  // Use useMemo to create the debounced function once
  // Recreate if field.onChange changes (should be stable from react-hook-form or parent)
  const debouncedOnChange = useMemo(
    () => debounce((jsonContent: string) => {
      // console.log("TextEditor: Debounced update triggered");
      field.onChange(jsonContent);
    }, 500), // Adjust debounce delay (e.g., 300ms, 500ms)
    [field.onChange] 
  );

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
            levels: [1, 2, 3],
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
        alignments: ["left", "center", "right", "justify"],
      }),
      Typography,
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline',
        },
      }),
      TextStyle,
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      Image.configure({
        allowBase64: true,
        HTMLAttributes: {
          class: 'rounded-lg max-w-full',
        },
      }),
    ],
    editorProps: {
      attributes: {
        class:
          "min-h-[300px] p-4 max-w-none focus:outline-none prose prose-sm text-gray-900 dark:prose-invert",
      },
    },
    onUpdate: ({ editor: currentEditor }) => {
      if (currentEditor) {
        debouncedOnChange(JSON.stringify(currentEditor.getJSON()));
      }
    },
    onBlur: ({ editor: currentEditor }) => {
      debouncedOnChange.cancel(); // Cancel any pending debounced update
      if (currentEditor) { // Null check for editor
        // console.log("TextEditor: onBlur triggered"); // For debugging
        field.onChange(JSON.stringify(currentEditor.getJSON()));
      }
    },
    content: initialContent,
    immediatelyRender: false,
  });

  // Cleanup the debounced function when the component unmounts
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);

  return (
    <div className="w-full border rounded-lg overflow-hidden bg-card">
      <TextEditorMenuBar editor={editor} />
      <EditorContent editor={editor} />
    </div>
  );
}
