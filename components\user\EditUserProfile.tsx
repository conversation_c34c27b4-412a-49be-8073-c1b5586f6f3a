import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { UserProfileForm } from "./UserProfileForm";
import { PencilIcon } from "lucide-react";
import { useState } from "react";

export function EditUserProfile() {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="default" 
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <PencilIcon className="h-4 w-4" />
          Update Profile
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[750px]">
        <DialogHeader>
          <DialogTitle>Update Profile</DialogTitle>
          <DialogDescription>
            Keep your profile updated.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <UserProfileForm onSuccess={() => setOpen(false)} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
