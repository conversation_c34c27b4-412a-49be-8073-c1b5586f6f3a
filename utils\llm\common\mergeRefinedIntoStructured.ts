export type StructuredSections = Record<string, string | string[] | object[]>;

export function mergeRefinedIntoStructured({
  original,
  refinedGeneral,
}: {
  original: StructuredSections;
  refinedGeneral: StructuredSections;
}): StructuredSections {
  const merged: StructuredSections = { ...original };

  delete merged['General'];

  for (const [key, value] of Object.entries(refinedGeneral)) {
    const isMeaningful =
      typeof value === 'string'
        ? value.trim()
        : Array.isArray(value)
        ? value.length > 0
        : typeof value === 'object' && value !== null;

    if (isMeaningful) {
      merged[key] = value;
    }
  }

  return merged;
}
