"use server";

import { auth } from "@/lib/auth/auth";;
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { deleteUserFeedbackData, getUserFeedbackData, saveUserFeedbackData } from "@/data/user/user";

export async function SaveUserFeedback(message: string[], userId?: string) {
  // Arcjet protection
  await ensureServerActionProtection();

  if (!message){
      return {
            data: null,
            error: "Message is required." 
        };
  }

  try {
    const data = await saveUserFeedbackData(message, userId);
    if (data) {
      return {
            data: data,
            success: "Successfully saved user feedback."
        };
    } else {
      return {
            data: null,
            error: "Failed to save user feedback." 
        };
    }
  } catch (error) {
      return {
            data: null,
            error: `Failed to save user feedback. Error: ${error}` 
      };
  }
}

export async function GetUserFeedback(page: number = 1, pageSize: number = 10) {
  const session = await auth();

   if (!session?.user?.id) {
    return { data: null, totalItems: 0, error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const result = await getUserFeedbackData(page, pageSize);
    if (result) {
      return {
            data: result.data,
            totalItems: result.totalItems,
            success: "Successfully fetched user feedback."
        };
    } else {
      return {
            data: null,
            totalItems: 0,
            error: "Failed to fetch user feedback."
        };
    }
  } catch (error) {
      return {
            data: null,
            error: `Failed to fetch user feedback. Error: ${error}` 
      };
  }
};

export async function DelteUserFeedback(id: string) {
  const session = await auth();

   if (!session?.user?.id) {
    return { data: null, totalItems: 0, error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  if (!id){
      return {
            error: "ID is required." 
        };
  }
  
  try {
    const result = await deleteUserFeedbackData(id);
    if (result) {
      return {
            success: "Successfully deleted user feedback."
        };
    } else {
      return {
            error: "Failed to delete user feedback."
        };
    }
  } catch (error) {
      return {
            error: `Failed to delete user feedback. Error: ${error}` 
      };
  }
};
