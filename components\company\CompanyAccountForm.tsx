"use client";

import { z } from "zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CompanySchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countryList } from "@/data/location/countryList";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { TextEditor } from "@/components/general/TextEditor";
import { useRouter } from "next/navigation"; // For client-side navigation
import { useCompanyActions } from "@/hooks/useCompanyActions";
import { UpdateCompany } from "@/actions/company/updateCompany";
import { BenefitsSelector } from "../job/BenefitsSelector";
import { parseToNumberOrDefault } from "@/utils/stringHelpters";
import { Textarea } from "../ui/textarea";

export function CompanyAccountForm({ companyId }: { companyId?: string }) {
    const [pending, setPending] = useState(false);
    const [formInitialized, setFormInitialized] = useState(false);
    const [error, setError] = useState<string | undefined>("");
    const router = useRouter();
    
    const { company, isLoading } = useCompanyActions(companyId as string);

    const form = useForm<z.infer<typeof CompanySchema>>({
        resolver: zodResolver(CompanySchema),
        defaultValues: {
        id: "",
        name: "",
        location: "",
        address: "",
        about: "",
        description: "",
        email: "",
        phone: "",
        logo: "",
        website: "",
        xAccount: "",
        linkedIn: "",
        tin: "",
        benefits: [],
        foreignerRatio: 0,
        englishUsageRatio: 0,
        },
    });

    useEffect(() => {
        if (!isLoading && company && !formInitialized) {
        
            form.reset({
                id: company?.id || "",
                name: company?.name || "",
                location: company?.location || "",
                address: company?.address || "",
                about: company?.about || "",
                description: company?.description || "",
                email: company?.email || "",
                phone: company?.phone || "",
                logo: company?.logo || "",
                website: company?.website || "",
                xAccount: company?.xAccount || "",
                linkedIn: company?.linkedIn || "",
                tin: company?.tin || "",
                benefits: (company?.benefits as string[]) || [],
                foreignerRatio: parseToNumberOrDefault(company?.foreignerRatio, 0),
                englishUsageRatio: parseToNumberOrDefault(company?.englishUsageRatio, 0),
            });

            setFormInitialized(true);
        }
    }, [isLoading, company, formInitialized, form, parseToNumberOrDefault]); 

    async function onSubmit(data: z.infer<typeof CompanySchema>) {
        try {
            setPending(true);

            const response = await UpdateCompany(data);

            if (response) {
                if (response.success) {
                    toast.success(response.success);
                    router.push("/home/<USER>/account");
                } else if (response.error) {
                    toast.error(response.error);
                    setError(response.error);
                }
            }
        } catch (error) {
            toast.error(`Unexpected error: ${error instanceof Error ? error.message : String(error)}`);
            setError(`Unexpected error: ${error instanceof Error ? error.message : String(error)}`);
        } finally {
            setPending(false);
        }
    }

    if (isLoading) {
        return <div>Loading form...</div>;
    }

  return (
        <Form {...form}>
            <form
            className="space-y-6"
            onSubmit={form.handleSubmit(onSubmit)}
            >
            <div className="flex text-muted-foreground text-sm justify-end">
                <div > {/* Simplified layout for "Required fields" */}
                <span className="text-red-500 text-sm">*</span> Required
                fields{" "}
                </div>
            </div>
            {/* COMPANY INFO */}
            <div
                className="grid grid-cols-2 gap-2 overflow-y-auto pr-2"
                style={{ maxHeight: "calc(120vh - 250px)" }}
            >
                {/* Company Name */}
                <div>
                    <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel>
                            Company Name<span className="text-red-500">*</span>
                            </FormLabel>
                            <FormControl>
                            <Input
                                placeholder="Enter company name"
                                {...field}
                            />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>

                {/* Company Address */}
                <div className="row-span-2">
                    <div className="mb-2">
                        <FormField
                            control={form.control}
                            name="location"
                            render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                Company Location
                                <span className="text-red-500">*</span>
                                </FormLabel>
                                <Select
                                // Force re-mount when formInitialized changes to ensure fresh state
                                key={String(formInitialized)}
                                onValueChange={field.onChange}
                                value={field.value}
                                >
                                <FormControl>
                                    <SelectTrigger className="w-full cursor-pointer">
                                    <SelectValue placeholder="Select Country" />
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent className="p-4">
                                    <SelectGroup className="cursor-pointer">
                                        {countryList.map((country) => (
                                            <SelectItem
                                            key={country.code}
                                            value={country.code}
                                            className="cursor-pointer"
                                            >
                                            <span className="emoji">
                                                {country.flagEmoji}
                                            </span>
                                            <span className="p-2">{country.name}</span>
                                            </SelectItem>
                                        ))}
                                    </SelectGroup>
                                </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                            )}
                        />
                    </div>
                    <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel> Company Address </FormLabel>
                            <FormControl>
                            <Textarea
                                className="h-25"
                                rows={4}
                                placeholder="Enter company address"
                                {...field}
                            />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>                

                <div>
                    {/* Company TIN */}
                    <div className="mb-2">
                        <FormField
                            control={form.control}
                            name="tin"
                            render={({ field }) => (
                            <FormItem>
                                <FormLabel>Tax Information Number (TIN)</FormLabel>
                                <FormControl>
                                <Input placeholder="TIN" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                            )}
                        />
                    </div>        

                    {/* Company Website */}
                    <div>
                        <FormField
                            control={form.control}
                            name="website"
                            render={({ field }) => (
                            <FormItem>
                                <FormLabel>Website</FormLabel>
                                <FormControl>
                                <Input
                                    placeholder="https://www.yourcompany.com"
                                    {...field}
                                />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                            )}
                        />       
                    </div>             
                </div>

                {/* Company LinkedIn */}
                <div className="row-start-3">                    
                    <FormField
                        control={form.control}
                        name="linkedIn"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel>LinkedIn</FormLabel>
                            <FormControl>
                            <Input
                                placeholder="https://www.linkedin.com/company/..."
                                {...field}
                            />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>

                {/* Company X Account */}
                <div className="row-start-3">
                    <FormField
                        control={form.control}
                        name="xAccount"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel>X (twitter) Account</FormLabel>
                            <FormControl>
                            <Input placeholder="@yourcompany" {...field} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>

                {/* Contact info, Foreigner, Language Usage */}
                <div className="col-span-2 mt-4">                        
                    <div className="grid grid-cols-4 grid-rows-1 gap-4">
                        <div>
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Company Email</FormLabel>
                                    <FormControl>
                                    <Input placeholder="<EMAIL>" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                                )}
                            />
                        </div>
                        <div>
                            <FormField
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Company Phone/Mobile</FormLabel>
                                    <FormControl>
                                        <Input placeholder="****** 456 7890" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                                )}
                            />
                        </div>
                        <div>
                            <FormField
                                control={form.control}
                                name="foreignerRatio"
                                render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Foreigner Ratio (%)</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            placeholder="10"
                                            {...field}
                                            onChange={event => field.onChange(event.target.valueAsNumber)}
                                            value={field.value ?? ''}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                                )}
                            />
                        </div>
                        <div>
                            <FormField
                                control={form.control}
                                name="englishUsageRatio"
                                render={({ field }) => (
                                <FormItem>
                                    <FormLabel>English Usage Ratio (%)</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="number"
                                            placeholder="10"
                                            {...field}
                                            onChange={event => field.onChange(event.target.valueAsNumber)}
                                            value={field.value ?? ''}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                                )}
                            />
                        </div>
                    </div>
                </div>

                {/* Company About */}
                <div className="col-span-2 mt-4">
                    <FormField
                        control={form.control}
                        name="about"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel>
                            About (one liner or tagline)
                            </FormLabel>
                            <FormControl>                                
                                <Textarea
                                  key={`about-${String(formInitialized)}`}
                                    placeholder="Short description about your company or a tagline. Example: Web design and development company."
                                  {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>

                {/* Company Description */}
                <div className="col-span-2 row-start-6">
                    <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                        <FormItem>
                            <FormLabel>
                            Company Description (About, Leadership, Mission, Vision, Life and Culture, etc.)
                            </FormLabel>
                            <FormControl>
                                <TextEditor
                                  // Force re-mount when formInitialized changes to ensure fresh state
                                  key={`description-${String(formInitialized)}`}
                                  field={{
                                    value: field.value || "",
                                    onChange: field.onChange,
                                    onBlur: field.onBlur,
                                    ref: field.ref
                                  }}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                        )}
                    />
                </div>
            </div>

            {/* Company Benefits */}
            <div className="flex flex-col pt-6 pb-6">
                {/* Benefits */}
                <FormField
                    control={form.control}
                    name="benefits"
                    render={({ field }) => (
                        <FormItem>
                        <FormLabel>Company Provided Benefits</FormLabel>
                        <FormControl>
                            <BenefitsSelector field={field as any} />
                        </FormControl>
                        <FormMessage />
                        </FormItem>
                    )}
                />
            </div>

            <div className="flex flex-col">
                <ErrorMessage message={error} />
            </div>
            <div className="flex flex-col">
                <Button
                type="submit"
                className="w-full cursor-pointer"
                disabled={pending}
                size="lg"
                >
                {pending ? "Submitting..." : "Update Company"}
                </Button>
            </div>
            </form>
        </Form>
    
    );
};