"use server";

import { auth } from "@/lib/auth/auth";
import { inngest } from "@/lib/inngest/client";
import { deleteJobData } from "@/data/job/job";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { sendInngestEvent } from "@/utils/sendInngestEvent";

export async function deleteJobPost(id: string) {
  const session = await auth();

  if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  if (!id) {
    return { error: "Missing job id." };
  }

  try {
    const result = await deleteJobData(id);

    if (result.success) {
      await sendInngestEvent({
        name: "job/cancel.expiration",
        data: {
          jobId: id,
        },
      });

      return {
        success: result.success,
        deleteResult: result.job,
      };
    } else {
      return {
        error: result.error,
        deleteResult: result.job,
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: error,
      deleteResult: null,
    };
  }
}
