import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";

const scoreOptions = [
  { value: "all", label: "All Scores" },
  { value: "50", label: "50+" },
  { value: "60", label: "60+" },
  { value: "70", label: "70+" },
  { value: "80", label: "80+" },
  { value: "90", label: "90+" },
];

interface ScoreFilterDropdownServerProps {
  currentScoreFromUrl?: string; // The 'score' value directly from URL search params
  basePath: string;
  allSearchParams: { [key: string]: string | string[] | undefined };
}

export function ScoreFilterDropdownServer({
  currentScoreFromUrl,
  basePath,
  allSearchParams,
}: ScoreFilterDropdownServerProps) {
  let selectedLabel: string;
  if (currentScoreFromUrl === "all") {
    selectedLabel = "All Scores";
  } else if (currentScoreFromUrl === undefined) {
    selectedLabel = "70+"; // Default active filter display
  } else {
    selectedLabel =
      scoreOptions.find((s) => s.value === currentScoreFromUrl)?.label ||
      `${currentScoreFromUrl}+`;
  }

  const buildHref = (newScoreValue: string) => {
    const params = new URLSearchParams();
    for (const [key, value] of Object.entries(allSearchParams)) {
      if (value !== undefined) {
        // Preserve existing params
        if (Array.isArray(value)) value.forEach((v) => params.append(key, v));
        else params.set(key, value as string);
      }
    }
    // When "all" is selected, set score=all in the URL.
    // Other scores will also be set directly.
    // This ensures that the page can distinguish between "no score set" (param absent) and "all scores selected" (param is 'all').
    params.set("score", newScoreValue);

    const queryString = params.toString();
    return `${basePath}${queryString ? `?${queryString}` : ""}`;
  };

  return (
    <div className="flex items-center gap-2 justify-end">
    <span className="text-sm font-semibold">Selected score:</span>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="ml-auto cursor-pointer">
            {selectedLabel}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Minimum Score</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {scoreOptions.map((option) => (
            <DropdownMenuItem key={option.value} asChild className="cursor-pointer">
              <Link href={buildHref(option.value)}>{option.label}</Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
