"use server";

import { auth } from "@/lib/auth/auth";
import { getFileData } from "@/data/site/file";
import { redirect } from "next/navigation";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const GetFile = async (id: string) => {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  if (!id) {
    return { error: "Missing File." };
  }

  try {
    const result = await getFileData(id);
    return result;
  } catch (error) {
    console.error(error);
    return null;
  }
};
