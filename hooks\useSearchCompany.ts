/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { SearchCompany } from "@/actions/company/getCompany";

export function useSearchCompany(searchQuery: string) {
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      if (searchQuery.length >= 2) {
        try {
          const res = await SearchCompany(searchQuery);
          if ("error" in res) {
            setError(res.error);
            setResults([]);
          } else {
            setResults(res);
            setError(null);
          }
        } catch (err) {
          console.error(err);
          setError("Error searching company.");
          setResults([]);
        }
      } else {
        setResults([]);
        setError(null);
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery]);

  return { results, error };
}
