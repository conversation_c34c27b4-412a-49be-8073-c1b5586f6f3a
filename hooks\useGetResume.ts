import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { GetResume } from "@/actions/resume/userResumeActions";
import { DeleteResume } from "@/actions/resume/userResumeActions";
import { Resume } from "@/lib/resume/resumeActions";

// Fetch resume data
const fetchResume = async (id: string): Promise<Resume> => {
  try {
    const data = await GetResume(id);
    return { ...data };
  } catch (error) {
    console.error("Resume fetch error:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch resume"
    );
  }
};

export function useGetResume(id: string) {
  const queryClient = useQueryClient();

  const resumeQuery = useQuery({
    queryKey: ["resume", id],
    queryFn: () => {
      if (!id) return null;
      return fetchResume(id);
    },
    enabled: !!id,
    staleTime: 30000,
  });

  const deleteMutation = useMutation({
    mutationFn: async (resumeId: string) => {
      return await DeleteResume(resumeId);
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["resume"] });
    },
  });

  return {
    resumeQuery,
    isLoading: resumeQuery.isLoading,
    isError: resumeQuery.isError,
    resume: resumeQuery.data,
    deleteMutation,
    isDeleting: deleteMutation.isPending,
    deleteError: deleteMutation.isError,
    deleteResult: deleteMutation.data,
  };
}
