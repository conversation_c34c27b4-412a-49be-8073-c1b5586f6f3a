"use client";

import { BackButton } from "@/components/general/BackButton";
import { Head<PERSON> } from "@/components/general/Header";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";

interface CardWrapperProps {
  children: React.ReactNode;
  captionLabel: string;
  headerLabel: string;
  backButtonLabel: string;
  backButtonHref: string;
  customClassName: string;
}

export const CardWrapper = ({
  children,
  captionLabel,
  headerLabel,
  backButtonLabel,
  backButtonHref,
  customClassName
}: CardWrapperProps) => {
  return (
    <Card className={customClassName}>
      <CardHeader>
        <Header caption={captionLabel} label={headerLabel} />
      </CardHeader>
      <CardContent>{children}</CardContent>
      <CardFooter>
        <BackButton label={backButtonLabel} href={backButtonHref} />
      </CardFooter>
    </Card>
  );
};
