import { FullResume } from "@/components/user/resume/FullResume";
import {
  GetOriginalResume,
  GetResume,
} from "@/actions/resume/userResumeActions";
import { ResumeEvaluationCompany } from "@/components/user/resume/ResumeEvaluationCompany";
import { EmptyState } from "@/components/general/EmptyState";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export default async function CandidateResumePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const resume = await GetResume(id as string);

  if (!resume?.resumeData) {
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <EmptyState
          title="Resume Not Found"
          description="This resume could not be found."
          buttonText="Back to Resume Bank"
          href="/home/<USER>/resume/list"
        />
      </div>
    );
  }
  const profilePicture = resume.picture as string;
  const origResume = await GetOriginalResume(resume?.fileId as string);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: resume.resumeData.standardFields.header.name,
    image: profilePicture,
    jobTitle: resume.resumeData.standardFields.header.shortAbout,
    description: resume.resumeData.standardFields.summary.content,
    email:
      resume.resumeData.standardFields.header.email &&
      `mailto:${resume.resumeData.standardFields.header.email}`,
    url: `${APP_URL}`,
    skills: resume.resumeData.standardFields.skills,
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="max-w-4xl mx-auto w-full md:rounded-lg border-[0.5px] border-neutral-300 bg-white">
        <ResumeEvaluationCompany userId="" resumeId={resume.id as string} />
      </div>
      <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between px-4 bg-white">
        <em className="text-sm text-muted-foreground pt-2">
          This is a preview of the resume generated by our AI system. The
          original resume can be found{" "}
          <a
            href={origResume?.url || "#"}
            target="_blank"
            className="text-blue-600 hover:underline dark:text-blue-400 cursor-pointer"
          >
            here
          </a>
          .
        </em>
        <h1 className="text-2xl font-bold pt-10">Résumé</h1>
        <FullResume
          username=""
          resume={resume?.resumeData}
          profilePicture={profilePicture}
        />
      </div>
    </>
  );
}
