"use client";

import { usePathname, useRouter } from "next/navigation";
import { LucideIcon } from "lucide-react";
import clsx from "clsx";
import { But<PERSON> } from "../ui/button";

interface SidebarItemProps {
  icon: LucideIcon;
  label: string;
  href: string;
  color: string;
  id: string;
  collapsed: boolean;
}

export const SidebarItem = ({
  icon: Icon,
  label,
  href,
  color,
  collapsed,
}: SidebarItemProps) => {
  const pathname = usePathname();
  const router = useRouter();

  const isActive =
    (pathname === "/" && href === "/") ||
    pathname === href ||
    pathname?.startsWith("${href}/");

  const onClick = () => {
    router.push(href);
  };

  return (
    <Button
      onClick={onClick}
      variant={"ghost"}
      className={clsx(
        "flex items-center gap-x-2 text-[#46556c] text-sm font-[500] pl-6 transition-all hover:text-slate-600 hover:bg-yellow-500/20 cursor-pointer",
        isActive &&
          "text-[#46556c] bg-sky-200/20 hover:bg-yellow-500/20 hover:text-sky-200 cursor-pointer"
      )}
      title={label}
    >
      <div className="flex items-center text-start gap-x-2 py-4 text-[#46556c]  text-sm font-[400]">
        <Icon size={22} className={clsx(color)} />
        {!collapsed && label}
      </div>
      <div
        className={clsx(
          "ml-auto opacity-0 border-2 border-sky-200 h-full transition-all",
          isActive && "opacity-100"
        )}
      />
    </Button>
  );
};
