import { segmentJobMarkdown } from "./segmentJobMarkdown";
import { refineFlatTextToCanonicalSections } from "./refineFlatJobTextToCanonicalSections";
import { ResumeSections } from "@/types/customTypes";

/**
 * Segments the raw job text and refines its 'General' section into canonical sub-sections.
 * It also includes the raw text as a fallback 'General' section if no other sections are detected.
 *
 * @param rawJobText The raw job description text.
 * @returns An object containing the structured job data, metadata, and any unknown headers.
 */
export function refineJobFlatTextToCanonicalSections(rawJobText: string) {
  // Step 1: Segment the text into initial structured sections
  const { structured, metadata, unknownHeaders } =
    segmentJobMarkdown(rawJobText);

  let finalStructured: ResumeSections = { ...structured }; // Start with the initially segmented structure

  // Step 2: Refine the 'General' subsection and merge its canonical sections into the final structure
  if (structured["General"]) {
    const refinedGeneral = refineFlatTextToCanonicalSections(
      structured["General"]
    );
    // Merge refinedGeneral properties into finalStructured. This will add new canonical sections
    // (e.g., "Overview", "Responsibilities") and potentially overwrite the original "General" if refinedGeneral also contains it.
    Object.assign(finalStructured, refinedGeneral);
  }

  // Step 3: Include raw text as fallback "General" if no sections were detected after processing
  if (Object.keys(finalStructured).length === 0) {
    finalStructured.General = rawJobText;
  }

  return {
    structured: finalStructured,
    metadata,
    unknownHeaders,
  };

}
