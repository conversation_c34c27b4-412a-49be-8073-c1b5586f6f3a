"use server";

import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { Resume } from "@/lib/resume/resumeActions";
import { searchResumeData } from "@/data/user/resume";
import { ResumeData } from "@/data/zod/resumeZod";

interface ResumeSearchResult {
  id: string;
  name: string;
  picture: string | null;
  resume: ResumeData;
  createdAt: string; 
}

export async function searchResume(
  searchTerm: string,
  page: number = 1,
  pageSize: number = 10
): Promise<{ resumes?: ResumeSearchResult[], totalPages?: number, error?: string }> {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const searchData = await searchResumeData(searchTerm, page, pageSize);

    if (searchData.error) {
      return { error: searchData.error };
    }


    const resumes: ResumeSearchResult[] = searchData.resumes.map(resume => ({
      ...resume,
      createdAt: resume.createdAt.toISOString(), // Convert Date to ISO string
      name: resume.name,
      picture: resume.picture,      
      resume: resume.resumeData,
    }));
    return { resumes: resumes, totalPages: searchData.totalPages };
  } catch (error) {
    console.error("Error fetching job:", error);
    return { error: "Failed to search for jobs. Please try again." };
  }
}