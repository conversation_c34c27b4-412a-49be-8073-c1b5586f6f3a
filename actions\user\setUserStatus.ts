"use server";

import { auth } from "@/lib/auth/auth";;
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { setUserStatusData } from "@/data/user/user";
import { UserStatus } from "@prisma/client";

export async function SetUserStatus(id: string, status: UserStatus) {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await setUserStatusData(id, status);
    if (data) {
      return {
            data: data,
            success: "Successfully set user status." 
        };
    } else {
      return {
            data: null,
            error: "Failed to set user status." 
        };
    }
  } catch (error) {
      return {
            data: null,
            error: `Failed to set user status. Error: ${error}` 
      };
  }
}
