generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  SUPERADMIN
  ADMIN
  SUPERVISOR
  USER
}

enum Gender {
  MALE
  FEMALE
}

enum UserType {
  COMPANY
  JOB_SEEKER
  OWNER
  RECRUITER
  TENANT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BLOCKED
}

enum JobPostStatus {
  DRAFT
  ACTIVE
  EXPIRED
  DELETED
}

enum PublishStatus {
  DRAFT
  LIVE
}

model User {
  id                    String                     @id @default(uuid())
  email                 Bytes                      @unique @db.ByteA // encrypted
  emailHash             String                     @unique // hashed
  name                  Bytes?                     @db.ByteA // encrypted
  firstName             Bytes?                     @db.ByteA // encrypted
  lastName              Bytes?                     @db.ByteA // encrypted
  mobilePhone           Bytes?                     @db.ByteA // encrypted
  birthDate             Bytes?                     @db.ByteA // encrypted
  image                 Bytes?                     @db.ByteA // encrypted 
  username              String?
  password              String?
  emailVerified         DateTime?
  companyId             String?
  stripeCustomerId      String?                    @unique
  gender                Gender?
  role                  UserRole?                  @default(USER)
  userType              UserType?                  @default(JOB_SEEKER)
  status                UserStatus?                @default(ACTIVE)
  onboarded             Boolean                    @default(false)
  planSet               Boolean                    @default(false)
  isTwoFactorEnabled    Boolean?                   @default(false)
  twoFactorConfirmation UserTwoFactorConfirmation?
  jobSeeker             JobSeeker?
  savedJobPosts         SavedJobPost[]
  appliedJobPosts       AppliedJobPost[]
  accounts              Account[]
  sessions              Session[]
  files                 UserFile[]
  resumes               UserResume[]
  resumeEvaluations     UserResumeEvaluation[]
  jobPosts              JobPost[]
  notes                 JobResumeNote[]
  plan                  UserPlan?
  feedbacks             UserFeedback[]
  subscriptions         Subscription[]

  company Company? @relation(fields: [companyId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserVerificationToken {
  id        String   @id @default(uuid())
  emailHash String   @unique
  token     String   @unique
  expires   DateTime

  @@unique([emailHash, token])
}

model UserPasswordResetToken {
  id        String   @id @default(uuid())
  emailHash String   @unique
  token     String   @unique
  expires   DateTime

  @@unique([emailHash, token])
}

model UserTwoFactorToken {
  id        String   @id @default(uuid())
  emailHash String   @unique
  token     String   @unique
  expires   DateTime

  @@unique([emailHash, token])
}

model UserTwoFactorConfirmation {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Company {
  id                String            @id @default(uuid())
  name              String
  location          String
  address           String?
  about             String?
  description       String?
  email             String?
  phone             String?
  logo              String?
  website           String?
  xAccount          String?
  linkedIn          String?
  tin               String?
  benefits          String[]
  tags              String[]
  foreignerRatio    Int               @default(0)
  englishUsageRatio Int               @default(0)
  userId            String
  locations         CompanyLocation[]
  contacts          CompanyContact[]
  jobPosts          JobPost[]
  users             User[]
  resumes           UserResume[]
  plan              UserPlan?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CompanyLocation {
  id                String  @id @default(uuid())
  companyId         String
  locationType      Int     @default(1)
  firstLineAddress  String?
  secondLineAddress String?
  thirdLineAddress  String?
  city              String?
  province          String?
  countryCode       String
  zipcode           String?
  addedById         String?

  country InfoCountry @relation(fields: [countryCode], references: [alpha2Code])
  company Company     @relation(fields: [companyId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CompanyContact {
  id          String  @id @default(uuid())
  companyId   String
  contactname String
  designation String?
  email       String  @unique
  mobilephone String?
  officephone String?
  addedById   String  @default("")

  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model JobSeeker {
  id           String  @id @default(uuid())
  title        String?
  countryCode  String?
  about        String?
  resumeFileId String? @unique
  resumeId     String? @unique
  portfolio    Bytes?  @db.ByteA // encrypted
  linkedin     Bytes?  @db.ByteA // encrypted
  github       Bytes?  @db.ByteA // encrypted
  writing      Bytes?  @db.ByteA // encrypted
  userId       String  @unique
  user         User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  resumeFile UserFile?   @relation(fields: [resumeFileId], references: [id])
  resume     UserResume? @relation(fields: [resumeId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserFile {
  id              String      @id @default(uuid())
  userId          String
  companyId       String?
  fileUse         String
  fileType        String?
  fileName        String?
  fileDescription String?
  fileSize        Int?
  key             Bytes?      @db.ByteA
  url             Bytes?      @db.ByteA
  parseStatus     String?
  parseResult     String?
  parseCount      Int?        @default(0)
  jobSeeker       JobSeeker?
  resume          UserResume?
  job             JobPost?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserResume {
  id               String                @id @default(uuid())
  userId           String?
  companyId        String?
  fileId           String?               @unique
  username         String?
  name             Bytes?                @db.ByteA // encrypted raw text
  title            Bytes?                @db.ByteA // encrypted raw text
  about            Bytes?                @db.ByteA // encrypted raw text
  email            Bytes?                @db.ByteA // encrypted raw text
  phone            Bytes?                @db.ByteA // encrypted raw text
  website          Bytes?                @db.ByteA // encrypted raw text
  xaccount         Bytes?                @db.ByteA // encrypted raw text
  linkedin         Bytes?                @db.ByteA // encrypted raw text
  github           Bytes?                @db.ByteA // encrypted raw text
  address          Bytes?                @db.ByteA // encrypted raw text
  picture          Bytes?                @db.ByteA // encrypted raw text
  fileContent      Bytes?                @db.ByteA // encrypted raw text
  resumeData       Bytes?                @db.ByteA // encrypted json
  promptMarkdown   Bytes?                @db.ByteA // encrypted raw text
  structured       Bytes?                @db.ByteA // encrypted json
  metadata         Bytes?                @db.ByteA // encrypted json
  status           PublishStatus?        @default(DRAFT)
  jobSeeker        JobSeeker?
  resumeEvaluation UserResumeEvaluation?
  resumeMatches    JobResumeMatch[]
  shortlists       JobShortlist[]
  notes            JobResumeNote[]

  user    User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
  company Company?  @relation(fields: [companyId], references: [id])
  file    UserFile? @relation(fields: [fileId], references: [id])

  createdAt                          DateTime  @default(now())
  updatedAt                          DateTime? @updatedAt
  lastProcessedForFallbackMatchingAt DateTime?
}

model UserResumeEvaluation {
  id                     String    @id @default(uuid())
  resumeId               String    @unique
  userId                 String?
  overallScore           Int?
  completenessScore      Int?
  experienceQualityScore Int?
  educationScore         Int?
  skillsScore            Int?
  strengths              String?
  improvements           String?
  recommendations        String?
  experienceEvaluation   String?
  educationEvaluation    String?
  skillsEvaluation       String?
  evaluationData         String
  createdAt              DateTime  @default(now())
  updatedAt              DateTime? @updatedAt

  user   User?       @relation(fields: [userId], references: [id], onDelete: Cascade)
  resume UserResume? @relation(fields: [resumeId], references: [id], onDelete: Cascade)
}

model JobPost {
  id                   String           @id @default(uuid())
  jobTitle             String
  employmentType       String
  experienceLevel      String?
  country              String
  location             String?
  department           String?
  salaryCurrency       String?
  salaryFrom           Int?
  salaryTo             Int?
  jobDescription       String           @db.Text
  listingDuration      Int
  interviewType        String?
  localRemoteWork      Boolean          @default(false)
  overseasRemoteWork   Boolean          @default(false)
  skills               String[]
  languageRequirements String[]
  tags                 String[]
  status               String           @default("DRAFT")
  rawText              String?          @db.Text
  promptMarkdown       String?          @db.Text
  structured           Json?
  metadata             Json?
  companyId            String
  fileId               String?          @unique
  userId               String
  savedBy              SavedJobPost[]
  applications         AppliedJobPost[]
  resumeMatches        JobResumeMatch[]
  shortlists           JobShortlist[]
  notes                JobResumeNote[]

  expiresAt                          DateTime?
  createdAt                          DateTime  @default(now())
  updatedAt                          DateTime  @updatedAt
  lastProcessedForFallbackMatchingAt DateTime?

  company Company   @relation(fields: [companyId], references: [id])
  user    User      @relation(fields: [userId], references: [id])
  file    UserFile? @relation(fields: [fileId], references: [id])

  @@index([id, companyId])
}

model SavedJobPost {
  id        String  @id @default(uuid())
  userId    String
  jobPostId String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobPost   JobPost @relation(fields: [jobPostId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, jobPostId])
}

model AppliedJobPost {
  id        String  @id @default(uuid())
  userId    String
  jobPostId String
  status    String?
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobPost   JobPost @relation(fields: [jobPostId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, jobPostId])
}

model InfoCountry {
  id               String            @id @default(uuid())
  countryName      String
  alpha2Code       String?           @unique
  alpha3Code       String?
  unCode           String?
  cities           InfoCity[]
  companyLocations CompanyLocation[]
}

model InfoCity {
  id        String @id @default(uuid())
  cityName  String
  countryId String

  country InfoCountry @relation(fields: [countryId], references: [id], onDelete: Cascade)
}

model UserResumeTemp {
  id         String  @id @default(uuid())
  userId     String
  companyId  String?
  fileId     String?
  rawText    String
  resumeData String

  createdAt DateTime @default(now())
}

model UserJobTemp {
  id        String @id @default(uuid())
  userId    String
  companyId String
  rawText   String
  jobData   String

  createdAt DateTime @default(now())
}

model JobResumeMatch {
  id                   String  @id @default(uuid())
  jobId                String
  resumeId             String
  criteria             String?
  overall_score        Int?
  skills_match         Int?
  experience_alignment Int?
  education_fit        Int?
  match_analysis       String?
  candidate_strengths  String?
  matching_skills      String?
  missing_requirements String?
  experience_relevance String?
  matchResult          String?

  jobPost JobPost    @relation(fields: [jobId], references: [id], onDelete: Cascade)
  resume  UserResume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model JobResumeNote {
  id                   String @id @default(uuid())
  userId               String
  companyId            String
  jobId                String
  resumeId             String
  note                 String
  otherNotes           Json?
  overall_score        Int    @default(0)
  skills_match         Int    @default(0)
  experience_alignment Int    @default(0)
  education_fit        Int    @default(0)

  jobPost JobPost    @relation(fields: [jobId], references: [id], onDelete: Cascade)
  resume  UserResume @relation(fields: [resumeId], references: [id], onDelete: Cascade)
  user    User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model JobShortlist {
  id       String @id @default(uuid())
  jobId    String
  resumeId String

  jobPost JobPost    @relation(fields: [jobId], references: [id], onDelete: Cascade)
  resume  UserResume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([jobId, resumeId])
}

model SystemFlag {
  name  String @id
  value String
}

model CachedExchangeRate {
  id                 String   @id @default(cuid())
  baseCurrencyCode   String // The currency the rate is based on, e.g., "USD"
  targetCurrencyCode String // The currency we are converting to, e.g., "PHP"
  exchangeRate       Float // The actual exchange rate: 1 baseCurrencyCode = X targetCurrencyCode
  currencySymbol     String // The symbol for the target currency, e.g., "₱", "$", "€"
  currencyName       String? // The name for the target currency, e.g., "₱", "$", "€"
  locale             String // The BCP 47 locale string for formatting, e.g., "en-PH", "en-US", "de-DE"
  countryCode        String? // The country code
  country            String? // The country name
  TLD                String? // The country internet domain
  lastFetchedAt      DateTime @updatedAt // Timestamp of when this rate was last fetched/updated

  @@unique([baseCurrencyCode, targetCurrencyCode], name: "unique_currency_pair") // Ensures one entry per currency pair
  @@index([targetCurrencyCode]) // Optional: Index for faster lookups if you often query by target currency
}

model UserPlan {
  id                     String         @id @default(uuid())
  userId                 String         @unique
  companyId              String?        @unique
  subscriptionId         String?
  pricingPlanId          String
  amount                 Float
  paymentFrequency       String
  exchangeRate           Float
  exchangeCurrencyCode   String
  exchangeCurrencySymbol String
  userLocale             String
  baseCurrencyCode       String
  baseCurrencySymbol     String
  createdAt              DateTime       @default(now())
  updatedAt              DateTime       @updatedAt
  endedAt                DateTime?
  subscriptions          Subscription[]

  user    User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  company Company?    @relation(fields: [companyId], references: [id], onDelete: Cascade)
  plan    PricingPlan @relation(fields: [pricingPlanId], references: [id], onDelete: Cascade)
}

model PricingPlan {
  id            String         @id @default(cuid())
  planName      String?
  description   String?
  currency      String?
  amountMonth   Float?
  amountYear    Float?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  users         UserPlan[]
  subscriptions Subscription[]
}

model UserFeedback {
  id        String   @id @default(cuid())
  userId    String?
  message   String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User?    @relation(fields: [userId], references: [id])
}

model Subscription {
  id             String   @id @default(cuid())
  userPlanId     String
  processor      String // Stripe, Paymongo, or PayPal
  userId         String
  subscriptionId String // subscription ID from processor
  pricingPlanId  String
  chargedAmount  Float
  currency       String
  billingCycle   String // "monthly" or "yearly"
  status         String // "active", "cancelled", "suspended"
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user     User        @relation(fields: [userId], references: [id])
  userPlan UserPlan    @relation(fields: [userPlanId], references: [id])
  plan     PricingPlan @relation(fields: [pricingPlanId], references: [id], onDelete: Cascade)
}
