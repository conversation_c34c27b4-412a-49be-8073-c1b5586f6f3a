import Image from "next/image";

interface ImagePopupProps {
  src: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void; // For the X button and explicit close actions
}

export function ImagePopup( { 
    src, 
    alt, 
    isOpen, 
    onClose
}: ImagePopupProps ) {
    if (!isOpen) {
        return null;
    }

    return (
    <div
      className="fixed inset-0 bg-opacity-75 flex items-center justify-center z-50 p-4"
    >
      <div
        className="bg-white p-5 rounded-lg shadow-2xl relative max-w-3xl max-h-[90vh] flex flex-col"
      >
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-600 hover:text-black bg-gray-200 hover:bg-gray-300 rounded-full p-1.5 text-2xl leading-none z-10"
          aria-label="Close"
        >
          &times;
        </button>
        <div className="w-full h-full flex items-center justify-center">
          <Image
            src={src}
            alt={alt}
            width={550} // Provide large base width for better quality scaledown
            height={750} // Provide large base height for better quality scaledown
            className="object-contain rounded-md max-w-full max-h-[calc(90vh-50px)]" // Adjust max-h to account for padding/button
          />
        </div>
      </div>
    </div>
  );
}