import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { TextEditorMenuBar } from "../general/TextEditorMenuBar";
import TextAlign from "@tiptap/extension-text-align";
import Typography from "@tiptap/extension-typography";
import Image from "@tiptap/extension-image"; // Import the Image extension
import Underline from "@tiptap/extension-underline"; // Import the Underline extension
import Link from "@tiptap/extension-link"; // Ensure Link is imported
import { ControllerRenderProps } from "react-hook-form";

interface JobDescriptionEditorProps {
  field: Pick<ControllerRenderProps, "value" | "onChange" | "onBlur" | "ref">;
}

export function JobDescriptionEditor({ field }: JobDescriptionEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Typography,
      Underline,
      Link.configure({ // Ensure Link extension is configured
        openOnClick: false, // Recommended: open links on ctrl/cmd + click
        autolink: true, // Automatically detect and create links from URLs
        HTMLAttributes: {
          class: 'text-primary underline cursor-pointer',
        },
      }),
      Image.configure({ // Add and configure the Image extension
        allowBase64: true, // Optional: if you want to allow base64 images
        HTMLAttributes: {
          class: 'rounded-lg max-w-full',
        },
      }),
    ],
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class:
          "min-h-[300px] p-4 max-w-none focus:outline-none prose dark:prose-invert list-none",
      },
    },
    onUpdate: ({ editor }) => {
      field.onChange(JSON.stringify(editor.getJSON()));
    },
    content: field.value ? JSON.parse(field.value) : "",
  });

  return (
    <div className="w-full border rounded-lg overflow-hidden bg-card">
      <TextEditorMenuBar editor={editor} />
      <EditorContent editor={editor} />
    </div>
  );
}
