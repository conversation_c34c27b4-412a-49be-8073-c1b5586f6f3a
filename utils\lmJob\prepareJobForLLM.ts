import { formatStructuredToMarkdown } from "../llm/common/formatStructuredToMarkdown";
import { refineJobFlatTextToCanonicalSections } from "./refineJobFlatTextToCanonicalSections";


export function prepareJobForLLM(rawJobText: string) {
  // Step 1: Refine flat text to canonical sections, extracting metadata and unknown headers
  // This new function encapsulates the segmentation, refinement of 'General' section,
  // and merging of refined sections, similar to how prepareResumeForLLM works.
  const { structured, metadata, unknownHeaders } = refineJobFlatTextToCanonicalSections(rawJobText);

  // Step 2: Format the structured data as a Markdown prompt
  // This is the final step before returning the prompt-ready markdown.
  const markdown = formatStructuredToMarkdown(structured);

  return {
    promptMarkdown: markdown,
    metadata,
    unknownHeaders,
    structured: structured,
  };
}

// Usage
// const { promptReadyMarkdown, metadata, unknownHeaders } = prepareJobForLLM(jobText);

// console.log(promptReadyMarkdown); // for scoring
// console.log(metadata);            // for display or analytics
// console.log(unknownHeaders);      // for pipeline learning
