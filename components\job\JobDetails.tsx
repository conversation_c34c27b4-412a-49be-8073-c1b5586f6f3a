"use client";

import { useEffect, useState, useActionState } from "react"; // Import useActionState
import { getJobDetailsAction } from "@/actions/job/getJobDetailsAction";
import { Badge } from "@/components/ui/badge";
import { buttonVariants } from "@/components/ui/button";
import { GlobeIcon, Heart, Loader2, MapPin, MapPinIcon } from "lucide-react";
import { getFlagEmoji } from "@/data/location/countryList";
import { JsonToHTML } from "@/components/general/JsonToHTML";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";
import { SaveJobButton } from "@/components/general/SubmitButton";
import { saveJobPost } from "@/actions/job/saveJobPost";
import { useSession } from "next-auth/react";
import { FaLinkedin } from "react-icons/fa";
import { ApplyButton } from "@/components/job/ApplyJobButton";
import { snakeCaseToTitleCase } from "@/utils/stringHelpters";
import { formatCurrency } from "@/utils/formatCurrency";
import { benefits as benefitsData } from "@/components/job/ListOfBenefits";
import { toast } from "sonner";
import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { transformJobDescriptionToTiptapJson } from "@/utils/jobDescriptionUtils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { JobDetailsDoc } from "./JobDetailsDoc";

// Define a type for the job data structure expected from the action
type JobDataFromAction = NonNullable<Awaited<ReturnType<typeof getJobDetailsAction>>["jobData"]>;

interface JobDetailsProps {
  jobId: string;
}

// Define the state structure for useActionState
interface SaveActionState {
  isSaved?: boolean;
  error?: string;
  message?: string;
  timestamp?: number; // To help useEffect trigger on state change
}

export function JobDetails({ jobId }: JobDetailsProps) {
  const { data: session, status: sessionStatus } = useSession();
  const [jobDetails, setJobDetails] = useState<{
    jobData: JobDataFromAction | null;
    isSaved: boolean;
    hasApplied: boolean;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Action handler for useActionState
  // This function will call your actual server action `saveJobPost`
  async function handleSaveJobAction(
    prevState: SaveActionState,
    formData: FormData
  ): Promise<SaveActionState> {
    const jobIdFromForm = formData.get("jobIdToSave") as string; // Use a unique name for the hidden input
    if (!jobIdFromForm) {
      return { error: "Job ID missing in form.", isSaved: prevState.isSaved, timestamp: Date.now() };
    }

    // Assuming saveJobPost is your actual server action:
    // saveJobPost(jobId: string): Promise<{ success: boolean; isSaved?: boolean; error?: string; message?: string }>
    const result = await saveJobPost(jobIdFromForm);

    if (result.success) {
      return { isSaved: result.isSaved, message: result.message, timestamp: Date.now() };
    } else {
      // On error, return the previous saved status to help maintain UI consistency
      return { error: result.error || "Failed to update save status.", isSaved: prevState.isSaved, timestamp: Date.now() };
    }
  }

  const [saveActionState, dispatchSaveAction] = useActionState(
    handleSaveJobAction,
    { isSaved: jobDetails?.isSaved ?? false } // Initial state for the action
  );

  useEffect(() => {
    if (!jobId) return;

    async function fetchDetails() {
      setLoading(true);
      setError(null);
      // Use session.data?.user?.id, which is correct for useSession hook
      const result = await getJobDetailsAction(jobId, session?.user?.id);
      if (result.error) {
        setError(result.error);
        setJobDetails(null);
      } else if (result.jobData) {
        setJobDetails({
          jobData: result.jobData as JobDataFromAction,
          isSaved: result.isSaved || false,
          hasApplied: result.hasApplied || false,
        });
      }
      setLoading(false);
    }

    if (sessionStatus !== 'loading') {
        fetchDetails();
    } // The fetchDetails() call above handles both 'authenticated' and 'unauthenticated' states.

  }, [jobId, session?.user?.id, sessionStatus]);

  // useEffect to react to changes from useActionState and update jobDetails
  useEffect(() => {
    if (saveActionState.timestamp) { // Indicates the action has run
      if (saveActionState.error) {
        toast.error(saveActionState.error);
        // If an error occurred, and useActionState provides a previous 'isSaved' state,
        // ensure our main 'jobDetails.isSaved' reflects that to prevent incorrect UI.
        if (typeof saveActionState.isSaved === 'boolean' && jobDetails?.isSaved !== saveActionState.isSaved) {
            setJobDetails(prev => prev ? { ...prev, isSaved: saveActionState.isSaved! } : null);
        }
      } else { // Success
        if (saveActionState.message) toast.success(saveActionState.message);
        if (typeof saveActionState.isSaved === 'boolean') {
          setJobDetails(prev => prev ? { ...prev, isSaved: saveActionState.isSaved! } : null);
        }
      }
    }
  }, [saveActionState, jobDetails?.isSaved]); // Listen to saveActionState and jobDetails.isSaved for comparison

  if (loading || sessionStatus === 'loading') {
    return (
      <div className="flex justify-center items-center h-[70vh] p-6">
        <Loader2 className="size-8 animate-spin text-primary" />
        <p className="ml-2">Loading job details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-[70vh] p-6">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  if (!jobDetails || !jobDetails.jobData) {
    return (
      <div className="flex justify-center items-center h-[70vh] p-6">
        <p>Job details not found.</p>
      </div>
    );
  }

  const { jobData, isSaved, hasApplied } = jobDetails;
  const locationFlag = getFlagEmoji(jobData.country);

  const skillsDisplay = (() => {
    if (!jobData.skills || jobData.skills.length === 0) return null;
    const parsedSkills = jobData.skills.map((skill) => {
      if (typeof skill === "string" && (skill.startsWith("{") || skill.includes('":"'))) {
        try { return JSON.parse(skill); } catch (e) { return { name: skill, category: null }; }
      }
      return { name: skill, category: null };
    });
    const hasCategorizedSkills = parsedSkills.some((skill) => skill.category);
    if (hasCategorizedSkills) {
      const groupedSkills = parsedSkills.reduce((acc, skill) => {
        const category = skill.category || "Other";
        if (!acc[category]) { acc[category] = []; }
        acc[category].push(skill.name);
        return acc;
      }, {} as Record<string, string[]>);
      const sortedCategories = Object.keys(groupedSkills).sort();
      return (
        <div className="space-y-4">
          {sortedCategories.map((category) => (
            <div key={category} className="space-y-1">
              <h4 className="text-sm text-muted-foreground">{category}</h4>
              <div className="flex flex-wrap gap-1">
                {groupedSkills[category].map((skillName: any, index: any) => (
                  <Badge key={`${category}-${index}-${skillName}`} variant="outline" className="text-xs px-2 py-0 rounded-md">{skillName}</Badge>
                ))}
              </div>
            </div>
          ))}
        </div>
      );
    } else {
      return (
        <div className="flex flex-wrap gap-3">
          {parsedSkills.map((skill, index) => (
            <Badge key={`skill-${index}-${skill.name}`} variant="default" className="text-sm px-3 py-1 rounded-md">{skill.name}</Badge>
          ))}
        </div>
      );
    }
  })();

  const languagesDisplay = (() => {
    if (!jobData.languageRequirements || jobData.languageRequirements.length === 0) return null;
    const parsedLanguages = jobData.languageRequirements.map((lang) => {
      if (typeof lang === "string" && (lang.startsWith("{") || lang.includes('":"'))) {
        try { return JSON.parse(lang); } catch (e) { return { language: lang, level: null, type: "REQUIRED", certification: "" }; }
      }
      return { language: lang, level: null, type: "REQUIRED", certification: "" };
    });
    const languageMap: Record<string, string> = { TAGALOG: "Tagalog", CEBUANO: "Cebuano", JAPANESE: "Japanese", ENGLISH: "English", MANDARIN: "Mandarin", KOREAN: "Korean", VIETNAMESE: "Vietnamese" };
    const levelMap: Record<string, string> = { NATIVE: "Native", FLUENT: "Fluent", BUSINESS: "Business", CONVERSATIONAL: "Conversational" };
    const requiredLanguages = parsedLanguages.filter((lang) => lang.type === "REQUIRED" || lang.type === "REQUIRED"); // Original had typo, corrected
    const preferredLanguages = parsedLanguages.filter((lang) => lang.type === "PREFERRED" || lang.type === "PREFERED"); // Original had typo, corrected
    if (parsedLanguages.length === 0) return null;
    return (
      <div className="flex flex-col">
        <div><h3 className="font-semibold mb-4">Language Requirements</h3></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted p-3 font-medium text-center border-b">Required</div>
            <div className="p-4">
              {requiredLanguages.length > 0 ? (
                <div className="space-y-2">
                  {requiredLanguages.map((lang, index) => {
                    const languageName = languageMap[lang.language as keyof typeof languageMap] || lang.language;
                    const levelName = levelMap[lang.level as keyof typeof levelMap] || lang.level;
                    return (<div key={`required-${index}`}><span className="font-medium">{languageName}:</span> {levelName}</div>);
                  })}
                  {requiredLanguages.some((lang) => lang.certification) && (
                    <div className="mt-3 pt-3 border-t">
                      {requiredLanguages.filter((lang) => lang.certification).map((lang, index) => (<div key={`cert-${index}`} className="text-sm">{lang.certification}</div>))}
                    </div>
                  )}
                </div>
              ) : (<div className="text-muted-foreground text-center py-2">No Data</div>)}
            </div>
          </div>
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted p-3 font-medium text-center border-b">Preferred</div>
            <div className="p-4">
              {preferredLanguages.length > 0 ? (
                <div className="space-y-2">
                  {preferredLanguages.map((lang, index) => {
                    const languageName = languageMap[lang.language as keyof typeof languageMap] || lang.language;
                    const levelName = levelMap[lang.level as keyof typeof levelMap] || lang.level;
                    return (<div key={`preferred-${index}`}><span className="font-medium">{languageName}:</span> {levelName}</div>);
                  })}
                  {preferredLanguages.some((lang) => lang.certification) && (
                    <div className="mt-3 pt-3 border-t">
                      {preferredLanguages.filter((lang) => lang.certification).map((lang, index) => (<div key={`cert-${index}`} className="text-sm">{lang.certification}</div>))}
                    </div>
                  )}
                </div>
              ) : (<div className="text-muted-foreground text-center py-2">No Data</div>)}
            </div>
          </div>
        </div>
      </div>
    );
  })();

  const jobDescriptionJson = (() => {
    if (typeof jobData.jobDescription === "string") { 
      if (jobData.jobDescription.trim() === "") return { type: "doc", content: [] }; // Handle empty string
      
      // Try to parse the string as JSON. It could be either AI format or a stringified Tiptap doc.
      try {
        const parsed = JSON.parse(jobData.jobDescription);
        // If it's a valid object, pass it to JsonToHTML, which will determine the format.
        if (typeof parsed === 'object' && parsed !== null) {
          return parsed;
        }
      } catch (e) {
        // Not JSON. Assume it's plain text that needs to be wrapped in Tiptap format.
        // The transform function should handle this case.
        return transformJobDescriptionToTiptapJson(jobData.jobDescription);
      }
    }
    return jobData.jobDescription || { type: "doc", content: [] };
  })();

  const getPercentageColorClass = (percentage: number | null | undefined): string => {
    if (percentage === null || percentage === undefined || percentage === 0) {
      return "text-gray-400"; // Default or 0%
    }
    if (percentage >= 70) {
      return "text-green-500";
    } else if (percentage >= 40) {
      return "text-yellow-500";
    }
    return "text-red-500";
  };
  return (
    // This new outer div will be a flex container for the header and scrollable content.
    // flex-1 allows it to take available space in its parent (JobCard's dialog content box).
    // min-h-0 allows it to shrink correctly.
    <div className="flex flex-col flex-1 min-h-0">        
      <DialogHeader className="p-6 pb-2 border-b sticky top-0 z-10">
        <DialogTitle className="text-2xl font-bold">{jobData.jobTitle}</DialogTitle>
        <DialogDescription asChild>            
            <div className="flex justify-between items-center">
                <div className="flex gap-x-4">
                    <p className="font-medium">{jobData.company.name}</p>
                    {jobData.employmentType ? (
                        <>
                        <span className="hidden md:inline text-sm text-muted-foreground">
                            *
                        </span>
                        <Badge className="rounded-full" variant="secondary">
                            {snakeCaseToTitleCase(jobData.employmentType)}
                        </Badge>
                        </>
                    ) : null}

                    {jobData.experienceLevel ? (
                        <>
                        <span className="hidden md:inline text-sm text-muted-foreground">
                            *
                        </span>
                        <p className="text-sm text-muted-foreground">
                            {jobData.experienceLevel}
                        </p>
                        </>
                    ) : null}

                    {jobData.salaryFrom !== null && jobData.salaryFrom > 0 && (
                            <>
                            <span className="hidden md:inline text-sm text-muted-foreground">
                                *
                            </span>
                                <span className="text-muted-foreground">
                                {formatCurrency(jobData.salaryFrom, jobData.salaryCurrency ?? "USD")}
                                {jobData.salaryTo && jobData.salaryTo > 0 && jobData.salaryTo > jobData.salaryFrom ? ` - ${formatCurrency(jobData.salaryTo, jobData.salaryCurrency ?? "USD")}` : ""}
                                </span>
                            </>
                        )}
                </div>
                <div className="flex items-end">
                    {session?.user ? (
                        <form action={dispatchSaveAction}>
                          <input type="hidden" name="jobIdToSave" value={jobData.id} />
                          <SaveJobButton savedJob={jobDetails?.isSaved ?? false} />
                        </form>
                    ) : (
                        <Link href="/auth/signin" className={buttonVariants({ variant: "outline", size: "sm" })}>
                        <Heart className="size-4 mr-2" /> Save Job
                        </Link>
                    )}
                </div>
            </div>
        </DialogDescription>
      </DialogHeader>
      {/* Replaced ScrollArea with a div for main content scrolling */}
      {/* Added min-h-0 to help flexbox correctly size this scrollable area */}
      <div className="flex-1 overflow-y-auto min-h-0" tabIndex={0}>
        <div className="md:grid lg:grid-cols-3 gap-x-8 gap-y-6 p-6">
          <div className="space-y-6 lg:col-span-2">
            <Tabs defaultValue="account" className="w-full">
            <TabsList className="w-full">
                <TabsTrigger value="account">Webview</TabsTrigger>
                <TabsTrigger value="password">View PDF/DOC</TabsTrigger>
            </TabsList>
                <TabsContent value="account" className="flex flex-col w-full">
                    {/* WEBVIEW*/}
                    {/* Location Info Block */}
                    <div className="text-xs pb-2 w-full border rounded-lg overflow-clip p-2">
                        <div className="flex items-start">
                            <MapPin className="size-3.5 mr-1.5 flex-shrink-0 mt-[1px]" />
                            <div className="flex-grow min-w-0"> 
                                <Badge variant="default" className="rounded-md px-2 py-0.2 text-xs mr-1 whitespace-nowrap align-baseline">
                                    {jobData.country}
                                </Badge>
                                {jobData.location && jobData.location.toLowerCase() !== 'worldwide' && (
                                    <span className="text-muted-foreground align-baseline">{jobData.location}</span>
                                )}
                            </div>
                        </div>
                    </div>
                    <br />

                    {skillsDisplay && (
                    <section className="border rounded-lg p-2">
                        <h3 className="font-semibold mb-3 text-md">Required Skills</h3>
                        {skillsDisplay}
                    </section>
                    )}

                    {languagesDisplay && (
                    <section className="mt-6">
                        {languagesDisplay}
                    </section>
                    )}

                    <section>
                    {/* <h3 className="font-semibold mb-2 mt-6 text-md">Job Description</h3> */}
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                        <JsonToHTML json={jobDescriptionJson} hideSkills={true} />
                    </div>
                    </section>
                </TabsContent>
                <TabsContent value="password">
                    {/* DOC VIEW*/}
                    <JobDetailsDoc url={jobData.file?.url || ""} fileType={jobData.file?.fileType || ""} /> 
                </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6 lg:col-span-1 lg:mt-0 mt-8">
            <Card className="p-4">
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-md">Apply now</h3>
                  <p className="text-xs text-muted-foreground mt-0.5">
                    Let {jobData.company.name} know you found this job on TalentAiXchange.
                  </p>
                </div>
                <ApplyButton jobId={jobData.id} userId={session?.user?.id as string} hasApplied={hasApplied} />
              </div>
            </Card>

            <Card className="p-4">
              <h3 className="font-semibold mb-3 text-md">About the Job</h3>
              <div className="space-y-1.5 text-sm">
                <div className="flex justify-between"><span className="text-muted-foreground">Apply before:</span><span>{new Date(new Date(jobData.createdAt).getTime() + (jobData.listingDuration || 30) * 24 * 60 * 60 * 1000).toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" })}</span></div>
                <div className="flex justify-between"><span className="text-muted-foreground">Posted on:</span><span>{new Date(jobData.createdAt).toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" })}</span></div>
                <hr className="my-2"/>
                <div className="flex justify-between"><span className="text-muted-foreground">Type:</span><span>{snakeCaseToTitleCase(jobData.employmentType || "")}</span></div>
                <div className="flex justify-between"><span className="text-muted-foreground">Interview:</span><span>{snakeCaseToTitleCase(jobData.interviewType || "")}</span></div>
                <div className="flex justify-between"><span className="text-muted-foreground">Remote (Local):</span><span>{jobData.localRemoteWork ? "Yes" : "No"}</span></div>
                <div className="flex justify-between"><span className="text-muted-foreground">Remote (Global):</span><span>{jobData.overseasRemoteWork ? "Yes" : "No"}</span></div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="space-y-3">
                <div className="flex gap-3 items-start">
                  <Image src={jobData.company.logo || "/images/company-logo-placeholder.svg"} alt={jobData.company.name} width={40} height={40} className="rounded-lg size-10 object-cover flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-md">{jobData.company.name}</h3>
                    {jobData.company.location && jobData.company.address && (
                      <a href={`https://maps.google.com/?q=${encodeURIComponent(jobData.company.address + ", " + jobData.company.location)}`} target="_blank" rel="noopener noreferrer" className="text-xs text-primary hover:underline flex items-center gap-1"><MapPinIcon className="w-3 h-3" /><span>{jobData.company.location}: {jobData.company.address}</span></a>
                    )}
                    {jobData.company.website && (
                      <a href={jobData.company.website} target="_blank" rel="noopener noreferrer" className="text-xs text-primary hover:underline flex items-center gap-1"><GlobeIcon className="w-3 h-3" /><span>Website</span></a>
                    )}
                    {jobData.company.linkedIn && (
                       <a href={jobData.company.linkedIn} target="_blank" rel="noopener noreferrer" className="text-xs text-primary hover:underline flex items-center gap-1"><FaLinkedin className="w-3 h-3" /><span>LinkedIn</span></a>
                    )}
                    {jobData.company.xAccount && (<p className="text-xs text-muted-foreground">X: {jobData.company.xAccount}</p>)}
                  </div>
                </div>
                {jobData.company.about && (<p className="text-xs text-muted-foreground line-clamp-4">{jobData.company.about}</p>)}
                
                {(jobData.company.foreignerRatio !== null || jobData.company.englishUsageRatio !== null) && (
                  <div className="border-t pt-3 mt-3">
                    <div className="grid grid-cols-2 gap-3">
                      {/* Always render the foreigner ratio circle if the section is shown */}
                        <div className="flex flex-col items-center">
                          <p className="text-xs text-muted-foreground mb-1 text-center">Foreigners</p>
                          <div className="relative w-14 h-14"> {/* Smaller size */}
                            <div className="absolute inset-0 rounded-full border-2 border-gray-200 dark:border-gray-700"></div>
                            <svg className="absolute inset-0 w-full h-full rotate-[-90deg]" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="currentColor" className={getPercentageColorClass(jobData.company.foreignerRatio)} strokeWidth="4" strokeDasharray="301.59289474462014" strokeDashoffset={301.59289474462014 * (1 - (jobData.company.foreignerRatio || 0) / 100)} strokeLinecap="round" /></svg>
                            <div className="absolute inset-0 flex items-center justify-center"><span className="text-sm font-bold">{jobData.company.foreignerRatio ?? 0}%</span></div>
                          </div>
                        </div>
                      {/* Always render the English usage ratio circle if the section is shown */}
                        <div className="flex flex-col items-center">
                          <p className="text-xs text-muted-foreground mb-1 text-center">English Usage</p>
                          <div className="relative w-14 h-14"> {/* Smaller size */}
                            <div className="absolute inset-0 rounded-full border-2 border-gray-200 dark:border-gray-700"></div>
                            <svg className="absolute inset-0 w-full h-full rotate-[-90deg]" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="currentColor" className={getPercentageColorClass(jobData.company.englishUsageRatio)} strokeWidth="4" strokeDasharray="301.59289474462014" strokeDashoffset={301.59289474462014 * (1 - (jobData.company.englishUsageRatio || 0) / 100)} strokeLinecap="round" /></svg>
                            <div className="absolute inset-0 flex items-center justify-center"><span className="text-sm font-bold">{jobData.company.englishUsageRatio ?? 0}%</span></div>
                          </div>
                        </div>
                    </div>
                  </div>
                )}

                {jobData.company.benefits && jobData.company.benefits.length > 0 && (
                  <div className="border-t pt-3 mt-3">
                    <h3 className="font-semibold mb-1.5 text-sm">Benefits</h3>
                    <div className="flex flex-wrap gap-1.5">
                      {jobData.company.benefits.map((benefitId: string) => {
                        const benefit = benefitsData.find((b) => b.id === benefitId);
                        return benefit ? (<Badge key={benefit.id} variant="outline" className="text-xs px-1.5 py-0.5 font-normal">{benefit.icon}{benefit.label}</Badge>) : null;
                      })}
                    </div>
                  </div>
                )}

                {jobData.company.tags && jobData.company.tags.length > 0 && (
                  <div className="border-t pt-3 mt-3">
                    <h3 className="font-semibold mb-1.5 text-sm">Company Tags</h3>
                    <div className="flex flex-wrap gap-1.5">
                      {jobData.company.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs px-1.5 py-0.5 font-normal">{snakeCaseToTitleCase(tag)}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
