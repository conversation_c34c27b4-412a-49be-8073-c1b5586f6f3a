import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MainPagination } from "@/components/general/MainPagination";
import { Button } from "@/components/ui/button";
import {
  EyeIcon,
  MoreHorizontal,
  XCircleIcon,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import PdfImage from "@/public/icons/pdf.png";
import DocxImage from "@/public/icons/docx.png";
import { QuestionTooltip } from "../info/InfoTooltip";
import { UploadCompanyFile } from "./UploadCompanyFile";
import { auth } from "@/lib/auth/auth";

interface CompanyFilesListProps {
  paginatedResumes: any[];
  currentPage: number;
  totalPages: number;
}

export async function CompanyFilesList({
  paginatedResumes,
  currentPage,
  totalPages,
}: CompanyFilesListProps) {
  const session = await auth();
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Company Uploaded Files</CardTitle>
          <CardDescription>
            <div className="flex items-center justify-between gap-2 pb-4">
              <span className="flex">These are the files (resume and job) that have been uploaded by you or your team.</span>
                <UploadCompanyFile companyId={session?.user?.companyId as string} />
            </div>
            <div className="flex w-full border-1 rounded-lg p-2 bg-green-100/10">
                AI automatically processes, evaluates, and matches uploaded resumes to your existing job posts. These resumes will appear in the resume list soon. Uploaded jobs are listed in the job list.
            </div>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead style={{ width: "5%" }}>Date</TableHead>
                <TableHead style={{ width: "7.5%" }}>Type</TableHead>
                <TableHead style={{ width: "7.5%" }}>Filename</TableHead>
                <TableHead style={{ width: "7.5%" }}>Status</TableHead>
                <TableHead style={{ width: "7.5%" }}>Result</TableHead>
                <TableHead style={{ width: "5%" }}>
                    In Use
                    <QuestionTooltip 
                        content="This indicates if the file has associated AI-processed resume data. You cannot delete a file that is in use. Delete the resume first."
                    />
                </TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">
                    Actions
                    <QuestionTooltip 
                        content="Clicking view will open the file in a new tab or download the file. You cannot delete a file if it is in use. Delete the resume first."
                    />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedResumes.map((file, index) => (
                <TableRow
                  key={file.id}
                  className={index % 2 === 1 ? "bg-muted/30" : ""}
                >
                  <TableCell>{file.createdAt.toLocaleDateString()}</TableCell>
                  <TableCell>{file.fileUse}</TableCell>
                  <TableCell className="text-center">
                    <a
                      href={file?.url}
                      target="_blank"
                      className="text-primary hover:underline cursor-pointer"
                      title="Click to view"
                    >
                        <div className="flex items-center gap-2">
                            <Image
                                src={
                                    file?.fileType === "PDF" ? PdfImage : DocxImage
                                }
                                alt="Resume"
                                width={30}
                                height={30}
                                className="rounded-lg"
                                title={file?.fileName}
                            />
                        {file?.fileName}
                        </div>                        
                    </a>
                  </TableCell>
                  <TableCell>
                    {file?.parseStatus}
                  </TableCell>
                  <TableCell>
                    {file?.parseResult}
                  </TableCell>
                  <TableCell className="text-center">
                    {file?.resume?.id ? "Yes" : "No"}
                  </TableCell>
                  <TableCell className="text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="cursor-pointer"
                        >
                          <MoreHorizontal />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link
                            href={file.url}
                            target="_blank"
                          >
                            <EyeIcon className="mr-2" />
                            View
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild className="cursor-pointer" disabled={file.resume?.id ? true : false} title="Cannot delete if file is in use.">
                          <Link
                            href={`/home/<USER>/files/${file.id}/delete`}
                            title="Delete File"
                          >
                            <XCircleIcon className="mr-2" />
                            Delete
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <MainPagination
                totalPages={totalPages}
                currentPage={currentPage}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
