"use server";

import { auth } from "@/lib/auth/auth";
import { prisma } from "@/lib/prisma/prismaClient";
import { revalidatePath } from "next/cache";

import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function saveJobPost(jobId: string): Promise<{
  success: boolean;
  isSaved?: boolean;
  message?: string;
  error?: string;
}> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { success: false, error: "User not authenticated." };
    }

    // Arcjet protection
    await ensureServerActionProtection();

    const existingSavedJob = await prisma.savedJobPost.findUnique({
      where: {
        userId_jobPostId: {
          userId: session.user.id,
          jobPostId: jobId,
        },
      },
    });

    if (existingSavedJob) {
      // Unsave the job
      await prisma.savedJobPost.delete({
        where: {
          userId_jobPostId: {
            userId: session.user.id,
            jobPostId: jobId,
          },
        },
      });
      revalidatePath(`/job/${jobId}`);
      revalidatePath(`/public/job/${jobId}`); // Also revalidate public page if needed
      return {
        success: true,
        isSaved: false,
        message: "Job removed from saved list.",
      };
    } else {
      // Save the job
      await prisma.savedJobPost.create({
        data: {
          userId: session.user.id,
          jobPostId: jobId,
        },
      });
      revalidatePath(`/job/${jobId}`);
      revalidatePath(`/public/job/${jobId}`); // Also revalidate public page if needed
      return {
        success: true,
        isSaved: true,
        message: "Job saved successfully.",
      };
    }
  } catch (error) {
    console.error("Error in saveJobPost action:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred.",
    };
  }
}
