import { cn } from '@/lib/utils';

export const CustomSpinner = ({ className }: { className?: string }) => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('animate-spin', className)}
    >
      <g clipPath="url(#clip0_199_1000)">
        <path
          d="M16.7559 2.95038C15.5932 2.66086 14.3912 2.55816 13.1833 2.64516C12.5964 2.68744 12.0102 2.77499 11.4407 2.90559L10.8564 0.356437C11.5558 0.196148 12.2755 0.0885781 12.9954 0.0367343C14.4791 -0.0701251 15.9569 0.0564218 17.3879 0.412875L16.7559 2.95038Z"
          fill="black"
        />
        <path
          opacity="0.9"
          d="M22.7212 6.67958C21.5687 5.30939 20.091 4.21575 18.448 3.51711L19.4714 1.11053C21.4909 1.96918 23.3068 3.31286 24.7227 4.99636L22.7212 6.67958Z"
          fill="black"
        />
        <path
          opacity="0.8"
          d="M25.3575 13.2014L25.3561 13.182C25.2258 11.3728 24.6889 9.67405 23.7603 8.13273L26.0004 6.78326C27.1435 8.68059 27.8043 10.7703 27.9645 12.9941L25.3575 13.2014Z"
          fill="black"
        />
        <path
          opacity="0.7"
          d="M25.8806 21.4092L23.6626 20.0236C24.6109 18.5057 25.1926 16.764 25.3447 14.9866L27.9505 15.2098C27.7631 17.3978 27.0474 19.5417 25.8806 21.4092Z"
          fill="black"
        />
        <path
          opacity="0.6"
          d="M19.2622 26.9771L18.28 24.5535C19.9315 23.8842 21.4255 22.8146 22.6006 21.4604L24.5759 23.1743C23.1317 24.8388 21.2942 26.1538 19.2622 26.9771Z"
          fill="black"
        />
        <path
          opacity="0.5"
          d="M15.0071 27.9634C13.5302 28.0696 12.0594 27.9446 10.6353 27.5921L11.2635 25.0536C12.4207 25.34 13.6171 25.4413 14.8194 25.3549C15.4121 25.3122 16.004 25.2233 16.5785 25.0907L17.1669 27.6388C16.461 27.8017 15.7344 27.911 15.0071 27.9634Z"
          fill="black"
        />
        <path
          opacity="0.4"
          d="M8.5507 26.8974C6.52989 26.0419 4.71202 24.7009 3.2937 23.0195L5.29253 21.3333C6.44715 22.7018 7.92633 23.7932 9.5704 24.4892L8.5507 26.8974Z"
          fill="black"
        />
        <path
          opacity="0.3"
          d="M2.01317 21.2351C0.863315 19.3325 0.198807 17.2368 0.0380801 15.0057L0.0354004 14.9673L2.64383 14.7794L2.64722 14.8277C2.77727 16.6329 3.31714 18.3368 4.25131 19.8822L2.01317 21.2351Z"
          fill="black"
        />
        <path
          opacity="0.2"
          d="M2.65946 12.9962L0.0541992 12.7691C0.24473 10.5815 0.963762 8.4387 2.13342 6.57288L4.34925 7.96177C3.39861 9.47837 2.81428 11.2192 2.65946 12.9962Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_199_1000">
          <rect width="28" height="28" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
