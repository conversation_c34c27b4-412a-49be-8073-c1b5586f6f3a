import { EmptyState } from "@/components/general/EmptyState";
import { CompanyJobList } from "@/components/company/CompanyJobList";
import { auth } from "@/lib/auth/auth";
import { getCompanyJobList } from "@/actions/company/getCompany";

export default async function CompanyJobsPage() {
  const session = await auth();

  const data = await getCompanyJobList(session?.user?.companyId as string);
  return (
    <>
      {data?.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
            <h1 className="text-2xl font-semibold">Job Posts</h1>
            <EmptyState
            title="No job posts found"
            description="You don't have job posts yet."
            buttonText="Post a job"
            href="/home/<USER>/job/post"
            />
        </div>
      ) : (
        <CompanyJobList data={data} />
      )}
    </>
  );
}
