"use server";

import { auth } from "@/lib/auth/auth";
import { deleteFileData } from "@/data/site/file";
import { redirect } from "next/navigation";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const DeleteFile = async (id: string) => {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  if (!id) {
    return { error: "Missing File." };
  }

  try {
    const result = await deleteFileData(id);

    if (result.success) {
      return {
        success: result.success,
        fileResult: result.file,
      };
    } else {
      return {
        error: result.error,
        fileResult: result.file,
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: error,
      fileResult: null,
    };
  }
};
