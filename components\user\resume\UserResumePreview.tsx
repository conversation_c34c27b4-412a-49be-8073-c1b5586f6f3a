"use client";

import { LoadingState } from "@/components/general/LoadingState";
import PreviewActionbar from "./PreviewActionbar";
import { useUserActions } from "@/hooks/useJobSeekerActions";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { PublishStatus } from "@prisma/client";
import { FullResume } from "./FullResume";
import { PopupSiteLive } from "./PopupSiteLive";
import { getSiteUrl } from "@/utils/getSiteUrl";
import { ResumeEvaluation } from "@/components/user/resume/ResumeEvaluation";
import { UploadResume } from "./UploadResume";
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { ResumeData } from "@/lib/resume/resumeActions";
import { Edit, Eye, Save, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import LoadingFallback from "@/components/general/LoadingFallback";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { EditResume } from "./editing/EditResume";

export function UserResumePreview() {
  const { resumeQuery, toggleStatusMutation, usernameQuery, originalResume, saveResumeDataMutation } = useUserActions();
  const [localResumeData, setLocalResumeData] = useState<ResumeData>();
  const [showModalSiteLive, setModalSiteLive] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDiscardConfirmation, setShowDiscardConfirmation] = useState(false);

   useEffect(() => {
    if (resumeQuery.data?.resume?.resumeData) {
      setLocalResumeData(resumeQuery?.data?.resume?.resumeData);
    }
  }, [resumeQuery.data?.resume?.resumeData]);

  console.log({resumeQuery: resumeQuery.data});
  
  if (resumeQuery.isLoading || usernameQuery.isLoading || !usernameQuery.data || originalResume.isLoading) {
    return <LoadingState text="Loading..." />;
  }

  const handleSaveChanges = async () => {
    if (!localResumeData) {
      toast.error('No resume data to save');
      return;
    }

    try {
      await saveResumeDataMutation.mutateAsync(localResumeData);
      toast.success('Changes saved successfully');
      setHasUnsavedChanges(false);
      setIsEditMode(false);
    } catch (error) {
      if (error instanceof Error) {
        toast.error(`Failed to save changes: ${error.message}`);
      } else {
        toast.error('Failed to save changes');
      }
    }
  };

  const handleDiscardChanges = () => {
    // Show confirmation dialog instead of immediately discarding
    setShowDiscardConfirmation(true);
  };

  const confirmDiscardChanges = () => {
    // Reset to original data
    if (resumeQuery.data?.resume?.resumeData) {
      setLocalResumeData(resumeQuery.data?.resume?.resumeData);
    }
    setHasUnsavedChanges(false);
    setIsEditMode(false);
    setShowDiscardConfirmation(false);
    toast.info('Changes discarded');
  };

  const handleResumeChange = (newResume: ResumeData) => {
    setLocalResumeData(newResume);
    setHasUnsavedChanges(true);
  };

  if (
    resumeQuery.isLoading ||
    usernameQuery.isLoading ||
    !usernameQuery.data ||
    !localResumeData
  ) {
    return <LoadingFallback message="Loading..." />;
  }

  return (
    <div className="w-full flex flex-col">
      {/* Only center the title and description */}
      <div className="max-w-4xl mx-auto w-full flex justify-between items-center gap-2 mb-4">
        <h2 className="text-2xl font-bold">AI Generated Resume Preview</h2>
         <UploadResume />
      </div>
      <div className="max-w-4xl mx-auto w-full text-muted-foreground text-sm border-1 rounded-lg p-4">        
        <p className="text-muted-foreground text-sm">
          This is a preview of your uploaded{" "}
          <a
            href={originalResume.data?.url || "#"}
            target="_blank"
            className="text-blue-600 hover:underline dark:text-blue-400 cursor-pointer"
          >
            resume
          </a>{" "}
          generated by our AI system. Upload a new resume to replace this one.
          Clicking the publish button will make your resume public with a
          shareable link.
        </p>
        <br />
        <em className="w-full text-muted-foreground text-sm font-semibold">
            You understand that by clicking the publish button, you are making your resume public and
            shareable. This means that anyone with the link can view your resume. Please ensure that you are comfortable with this before proceeding. Only publish your resume if you are applying for a job and want to share your personal url instead of sending a resume file.
        </em>
        <br /><br />
        <p className="w-full text-muted-foreground text-sm">
            For your protection, the system will automatically unpublish your resume after 7 days. You can also unpublish it at any time. Please read our <a href="/public/privacy" target="_blank" className="text-blue-600">privacy policy</a> for more information.
        </p>
      </div>
      
      {/* Main content area - remove flex-col to prevent stacking */}
      <div className="w-full min-h-screen bg-background flex flex-col gap-4 pb-8 pt-4">
        {/* PreviewActionbar container - keep max width but align left */}
        <div className="max-w-4xl mx-auto w-full md:px-0 px-4">
          <PreviewActionbar
            resumeId={resumeQuery?.data?.resume?.id as string}
            initialUsername={usernameQuery?.data?.username as string}
            status={resumeQuery.data?.resume?.status}
            onStatusChange={async (newStatus) => {
              await toggleStatusMutation.mutateAsync(newStatus);
              const isFirstTime = !localStorage.getItem("publishedSite");

              if (isFirstTime && newStatus === PublishStatus.LIVE) {
                setModalSiteLive(true);
                localStorage.setItem(
                  "publishedSite",
                  new Date().toDateString()
                );
              } else {
                if (newStatus === PublishStatus.DRAFT) {
                  toast.warning("Your website has been unpublished.");
                } else {
                    toast.success("Your resume website has been published!");
                }
              }
            }}
            isChangingStatus={toggleStatusMutation.isPending}
          />
        </div>

        <div className="max-w-4xl mx-auto w-full md:rounded-lg border-[0.5px] border-neutral-300">
          <ResumeEvaluation />
        </div>

        <div className="max-w-3xl mx-auto w-full flex flex-col md:flex-row justify-between items-center px-4 md:px-0 gap-4">
            <ToggleGroup
                type="single"
                value={isEditMode ? 'edit' : 'preview'}
                onValueChange={(value) => setIsEditMode(value === 'edit')}
                aria-label="View mode"
                className="flex gap-4"
            >
            <ToggleGroupItem value="preview" aria-label="Preview mode" className="cursor-pointer">
                <Eye className="h-4 w-4 mr-1" />
                <span>Preview</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="edit" aria-label="Edit mode" className="cursor-pointer">
                <Edit className="h-4 w-4 mr-1" />
                <span>Edit</span>
            </ToggleGroupItem>
            </ToggleGroup>

            {isEditMode && (
            <div className="flex gap-2">
                <Button
                variant="outline"
                onClick={handleDiscardChanges}
                className="flex items-center gap-1"
                disabled={!hasUnsavedChanges || saveResumeDataMutation.isPending}
                >
                <X className="h-4 w-4" />
                <span>Discard</span>
                </Button>
                <Button
                onClick={handleSaveChanges}
                className="flex items-center gap-1"
                disabled={!hasUnsavedChanges || saveResumeDataMutation.isPending}
                >
                {saveResumeDataMutation.isPending ? (
                    <span className="animate-spin">⌛</span>
                ) : (
                    <Save className="h-4 w-4" />
                )}
                <span>
                    {saveResumeDataMutation.isPending ? 'Saving...' : 'Save'}
                </span>
                </Button>
            </div>
            )}
        </div>

        <div className="max-w-4xl mx-auto w-full md:rounded-lg border-[0.5px] border-neutral-300 flex items-center justify-between px-4">
            {isEditMode ? (
                <EditResume
                    resume={localResumeData}
                    onChangeResume={handleResumeChange}
                />
            ) : (
                <FullResume
                    username={usernameQuery?.data?.username as string}
                    resumeId={resumeQuery.data?.resume?.id}
                    resume={resumeQuery.data?.resume?.resumeData}
                    profilePicture={resumeQuery.data?.resume?.picture ?? ""}
                />
            )}
        </div>
      </div>

      <AlertDialog
        open={showDiscardConfirmation}
        onOpenChange={setShowDiscardConfirmation}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Discard Changes?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to discard your changes? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDiscardChanges}>
              Discard
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <PopupSiteLive
        isOpen={showModalSiteLive}
        websiteUrl={getSiteUrl(usernameQuery.data.username)}
        onClose={() => {
          setModalSiteLive(false);
        }}
      />
    </div>
  );
}
