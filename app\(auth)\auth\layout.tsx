import { Navbar } from "@/components/header/Navbar";
import { Footer } from "@/components/footer/Footer";

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex w-full flex-col bg-athena-gradient min-h-screen">
        <div className="w-full pl-10 pr-10">
            <Navbar />
        </div>
      <div className="flex-1 flex flex-col items-center justify-center p-10">
        {children}
      </div>
      <Footer />
    </div>
  );
}
