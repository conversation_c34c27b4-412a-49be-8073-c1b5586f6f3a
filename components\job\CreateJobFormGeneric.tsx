"use client";

import { JobSchema } from "@/data/zod/zodSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Control, useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
} from "../ui/select";
import { SelectGroup } from "@radix-ui/react-select";
import { countryList } from "@/data/location/countryList";
import { SalaryRangeSelector } from "./SalaryRangeSelector";
import { useEffect, useState } from "react";
import { JobDescriptionEditor } from "./JobDescriptionEditor";
import { Button } from "../ui/button";
import { createJob } from "@/actions/job/createJob";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { LanguageRequirementsSection } from "../company/LanguageRequirementsSection";
import { SkillsSection } from "./SkillsSection";
import { Switch } from "../ui/switch";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { UserRole } from "@prisma/client";
import { CitySelector } from "./CitySelector";

interface CreateJobFormProps {
  userRole: UserRole;
}

export function CreateJobFormGeneric({
  userRole,
}: CreateJobFormProps) {
  const router = useRouter();
  const [currency, setCurrency] = useState("USD");
  const [isPending, setIsPending] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (userRole === "ADMIN") {
      setIsAdmin(true);
    }
  }, [userRole]);

  const form = useForm<z.infer<typeof JobSchema>>({
    resolver: zodResolver(JobSchema),
    defaultValues: {
      jobTitle: "",
      employmentType: "",
      experienceLevel: "",
      country: "",
      location: "",
      department: "",
      salaryCurrency: "PHP",
      salaryFrom: 0,
      salaryTo: 0,
      jobDescription: "",
      listingDuration: 30,
      companyId: "",
      interviewType: "ONLINE",
      localRemoteWork: false,
      overseasRemoteWork: false,
      skills: [],
      languageRequirements: [],
      tags: [],
    },
  });

  async function onSubmit(data: z.infer<typeof JobSchema>) {
    try {
      setIsPending(true);
      const jobPost = await createJob(data);

      if (jobPost?.success) {
        toast.success("Job posted successfully");
        if (isAdmin) {
          router.push("/home/<USER>/company/job/list");
        } else {
          router.push("/home/<USER>/job/list");
        }
      } else if (jobPost?.error) {
        toast.error(jobPost.error);
      }
    } catch (error) {
      if (error instanceof Error && error.message !== "NEXT_REDIRECT") {
        toast.error(error.message);
      }
    } finally {
      setIsPending(false);
    }
  }

  const onInvalid = (errors: any) => {
    console.error("Form validation errors:", errors);
    alert(JSON.stringify(errors));
    toast.error(
      "Submission failed. Please check the form for errors and ensure all required fields are correctly filled."
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit, onInvalid)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="jobTitle"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel id="jobTitle" className="font-bold text-sm">
                  Job Title<span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g. Senior Software Engineer"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Employment type */}
          <FormField
            control={form.control}
            name="employmentType"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel id="employmentType" className="font-bold text-sm">
                  Employment Type<span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full cursor-pointer">
                      <SelectValue placeholder="Select employment type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="w-full">
                    <SelectItem className="cursor-pointer" value="FULL_TIME">
                      Full Time (Permanent)
                    </SelectItem>
                    <SelectItem className="cursor-pointer" value="PART_TIME">
                      Part Time
                    </SelectItem>
                    <SelectItem className="cursor-pointer" value="CONTRACT">
                      Contract
                    </SelectItem>
                    <SelectItem className="cursor-pointer" value="INTERNSHIP">
                      Internship
                    </SelectItem>
                    <SelectItem className="cursor-pointer" value="POOLING">
                      For Pooling
                    </SelectItem>
                    <SelectItem className="cursor-pointer" value="TEMPORARY">
                      Temporary
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Location and Salary range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex flex-col w-full gap-1">
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel id="country" className="font-bold text-sm">
                    Job Location<span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full cursor-pointer">
                        <SelectValue placeholder="Select Country" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Worldwide</SelectLabel>
                        <SelectItem
                          value="worldwide"
                          className="w-full cursor-pointer"
                        >
                          <div className="flex items-center">
                            <span className="text-xl">🌎</span>
                            <span className="pl-2">Worldwide / Remote</span>
                          </div>
                        </SelectItem>
                      </SelectGroup>
                      <SelectGroup>
                        <SelectLabel>Location</SelectLabel>
                        {countryList.map((country) => (
                          <SelectItem
                            key={country.code}
                            value={country.code}
                            className="w-full cursor-pointer"
                          >
                            <div className="flex items-center">
                              <span className="emoji text-xl">
                                {String.fromCodePoint(
                                  0x1f1e6 + country.code.charCodeAt(0) - 65,
                                  0x1f1e6 + country.code.charCodeAt(1) - 65
                                )}
                              </span>
                              <span className="pl-2">{country.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* City selector */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <CitySelector
                      country={form.watch("country")}
                      value={field.value || ""}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Salary range */}
          <div className="flex flex-col w-full">
            <div className="flex w-full justify-between">
              <span className="font-bold text-sm">Salary Range</span>
              <div>
                <FormField
                  control={form.control}
                  name="salaryCurrency"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Select
                          value={currency}
                          onValueChange={(value) => {
                            field.onChange(value);
                            setCurrency(value);
                          }}
                          defaultValue="PHP"
                        >
                          <SelectTrigger className="w-[200] cursor-pointer">
                            <SelectValue placeholder="Currency" />
                          </SelectTrigger>
                          <SelectContent className="w-[200] cursor-pointer">
                            <SelectItem className="cursor-pointer" value="PHP">
                              PHP (₱)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="JPY">
                              JPY (¥)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="USD">
                              USD ($)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="KRW">
                              KRW (₩)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="CNY">
                              CNY (¥)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="AUD">
                              AUD ($)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="EUR">
                              EUR (€)
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="GBP">
                              GBP (£)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Salary range selector */}
            <div className="flex w-full">
              <FormItem className="w-full">
                <FormControl>
                  <SalaryRangeSelector
                    control={form.control as unknown as Control}
                    minSalary={1000}
                    maxSalary={20000000}
                    step={1000}
                    currency={currency}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </div>
          </div>

          {/* Local Remote Work */}
          <div className="flex flex-col rounded-lg border p-4 w-full gap-y-2">
            <FormField
              control={form.control}
              name="localRemoteWork"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <div className="space-y-0.5">
                    <FormLabel id="localRemoteWork" className="text-normal">
                      Local Remote Work (country)
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Overseas Remote Work */}
            <FormField
              control={form.control}
              name="overseasRemoteWork"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <div className="space-y-0.5">
                    <FormLabel id="overseasRemoteWork" className="text-normal">
                      Overseas Remote (worldwide)
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Interview type */}
          <div className="flex items-center gap-6 rounded-lg border p-4">
            <FormField
              control={form.control}
              name="interviewType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel id="interviewType" className="font-normal">Interview</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-row gap-6"
                    >
                      <FormItem className="flex items-center">
                        <FormControl>
                          <RadioGroupItem value="ONSITE" />
                        </FormControl>
                        <FormLabel id="interviewType" className="font-normal">On-site</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center">
                        <FormControl>
                          <RadioGroupItem value="ONLINE" />
                        </FormControl>
                        <FormLabel id="interviewType2" className="font-normal">Online</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center">
                        <FormControl>
                          <RadioGroupItem value="HYBRID" />
                        </FormControl>
                        <FormLabel id="interviewType3" className="font-normal">Hybrid</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        
        {/* Experience and Department */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

            {/* Experience level */}
            <div className="flex flex-col rounded-lg border gap-2 p-2 w-full gap-y-2">
                <span className="font-bold text-sm">Experience:</span>
                <FormField
                    control={form.control}
                    name="experienceLevel"
                    render={({ field }) => (
                    <FormItem className="w-full">
                        <FormControl>
                        <Input
                            placeholder="e.g. 1-2 years"
                            {...field}
                        />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
            </div>

            {/* Department */}
            <div className="flex flex-col rounded-lg border gap-2 p-2 w-full gap-y-2">
                <span className="font-bold text-sm">Department or Industry:</span>
                <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                    <FormItem className="w-full">
                        <FormControl>
                        <Input
                            placeholder="e.g. Accounting, Banking, Finance, HR"
                            {...field}
                        />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
            </div>
        </div>

        {/* Language requirements section */}
        <div className="flex w-full items-center rounded-lg border p-4">
          <LanguageRequirementsSection control={form.control} />
        </div>

        {/* Skills section */}
        <div className="flex w-full items-center rounded-lg border p-4">
          <SkillsSection control={form.control} />
        </div>

        {/* Job description */}
        <FormField
          control={form.control}
          name="jobDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel id="jobDescription" className="font-bold text-sm">
                Job Description <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <JobDescriptionEditor field={field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Listing duration */}
        <FormField
          control={form.control}
          name="listingDuration"
          render={({ field }) => (
            <FormItem>
                <div className="flex w-full justify-between items-center rounded-lg border p-4 cursor-pointer">
                    <FormLabel id="listingDuration" className="font-bold text-sm">
                        Listing Duration<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                        <RadioGroup
                            onValueChange={(value: string) => field.onChange(Number(value))}
                            value={String(field.value)} // Use value for controlled component
                            className="flex flex-row gap-6"
                            >
                            <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                <RadioGroupItem value="30" id="listingDuration30" />
                                </FormControl>
                                <FormLabel htmlFor="listingDuration30" className="font-normal cursor-pointer">30 Days</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                <RadioGroupItem value="60" id="listingDuration60" />
                                </FormControl>
                                <FormLabel htmlFor="listingDuration60" className="font-normal cursor-pointer">60 Days</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                <RadioGroupItem value="90" id="listingDuration90" />
                                </FormControl>
                                <FormLabel htmlFor="listingDuration90" className="font-normal cursor-pointer">90 Days</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                <RadioGroupItem value="120" id="listingDuration120" />
                                </FormControl>
                                <FormLabel htmlFor="listingDuration120" className="font-normal cursor-pointer">120 Days</FormLabel>
                            </FormItem>
                            </RadioGroup>
                    </FormControl>
                </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Submit button */}
        <div className="w-full justify-center pt-4">
          <Button
            type="submit"
            disabled={isPending}
            className="cursor-pointer w-full justify-center"
          >
            {isPending ? "Posting..." : "Post Job"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
