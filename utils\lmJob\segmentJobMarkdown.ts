export interface SegmentedJobText {
  structured: Record<string, string>;
  unknownHeaders: string[];
  metadata: {
    jobTitle?: string;
    company?: string;
    employmentType?: string;
    experienceLevel?: string;
    country?: string;
    location?: string;
    department?: string;
    salaryCurrency?: string;
    salaryFrom?: number;
    salaryTo?: number;
    interviewType?: string;
    localRemoteWork?: boolean;
    overseasRemoteWork?: boolean;
  };
}

export function segmentJobMarkdown(raw: string): SegmentedJobText {
  const cleaned = raw
    .replace(/\r\n|\r/g, '\n')
    .replace(/\n{2,}/g, '\n\n')
    .replace(/[ \t]+\n/g, '\n')
    .replace(/\n[ \t]+/g, '\n')
    .replace(/^\s+|\s+$/g, '')
    .replace(/•|●/g, '-'); // normalize bullet markers

  const lines = cleaned.split('\n');

  const segments: Record<string, string> = {};
  const unknownHeaders: string[] = [];
  // Initialize metadata with all possible keys, set to undefined or default values
  let metadata: SegmentedJobText['metadata'] = {
    jobTitle: undefined,
    company: undefined,
    employmentType: undefined,
    experienceLevel: undefined, // Not extracted here, but ensures key presence
    country: undefined,
    location: undefined,
    department: undefined,
    salaryCurrency: undefined,
    salaryFrom: undefined,
    salaryTo: undefined,
    interviewType: undefined,
    localRemoteWork: undefined,
    overseasRemoteWork: undefined,
  };

  let currentSection = 'Description';
  segments[currentSection] = '';

  const sectionHeaderMap: Record<string, string> = {
    Responsibilities: 'Responsibilities',
    Requirements: 'Requirements',
    Qualifications: 'Requirements',
    'What We’re Looking For': 'Requirements',
    'The Person': 'Requirements',
    'Must Haves': 'Requirements',
    'Main Duties': 'Responsibilities',
    'Main Responsibilities': 'Responsibilities',
    'What You’ll Do': 'Responsibilities',
    'The Role': 'Responsibilities',
    'Bonus points for': 'Nice to Have',
    'Desired Skills': 'Nice to Have',
    'Other Skills': 'Nice to Have',
    'Benefits': 'Benefits',
    'What You’ll Get': 'Benefits',
    'What Do We Offer?': 'Benefits',
    'The Rewards': 'Benefits',
  };

  const isURL = (str: string) => /https?:\/\/|www\.|\.co\.|\.com/.test(str);
  const headerLike = /^\[?(.*?)\]?$/;

  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed) continue; // Skip empty lines.
    const lowerCaseLine = trimmed.toLowerCase(); // Declare lowerCaseLine here for consistent case-insensitive matching

    let lineProcessedAsMetadata = false; // Flag to indicate if the line was consumed as metadata.

    // Possible metadata (first few lines)
    if (!segments['Description'].trim() && trimmed.length <= 60 && !metadata.jobTitle) { // Only process if Description section is still empty and jobTitle not set
      if (!metadata.jobTitle) metadata.jobTitle = trimmed;
      else if (!metadata.company && !/\d+ ?hours/i.test(trimmed)) metadata.company = trimmed; // Assume company if not hours
      lineProcessedAsMetadata = true; // Mark as processed for initial headers
    }

    // More specific metadata extraction from lines
    const employmentMatch = lowerCaseLine.match(/^employment:\s*(.*)/); // Use lowerCaseLine
    if (employmentMatch && metadata.employmentType === undefined) { // Check for undefined to avoid overwriting
      metadata.employmentType = employmentMatch[1].trim();
      lineProcessedAsMetadata = true;
    }

    const departmentMatch = lowerCaseLine.match(/^department:\s*(.*)/); // Corrected to use lowerCaseLine
    if (departmentMatch && metadata.department === undefined) { // Check for undefined
      metadata.department = departmentMatch[1].trim();
      lineProcessedAsMetadata = true;
    }

    // Robust salary parsing: handles "P20,000 – P30,000", "USD 50000-70000", "50000"
    // Adjusted regex to handle optional currency symbol before the second number
    const salaryMatch = lowerCaseLine.match(/^(?:salary:)?\s*([a-zA-Z$€£¥₩]{1,4})?\s*([\d,.]+)(?:\s*[–-]?\s*(?:[a-zA-Z$€£¥₩]{1,4})?\s*([\d,.]+))?/);
    if (salaryMatch && metadata.salaryFrom === undefined) { // Check if salaryFrom is not yet set to avoid overwriting
      metadata.salaryCurrency = salaryMatch[1] ? salaryMatch[1].trim().toUpperCase() : ''; // Ensure currency is uppercase, handle single-char
      const parseNumber = (s: string) => parseInt(s.replace(/[^0-9]/g, ''), 10); // Remove all non-digits for parsing

      const fromValue = parseNumber(salaryMatch[2]);
      metadata.salaryFrom = isNaN(fromValue) ? undefined : fromValue;

      if (salaryMatch[3]) {
        const toValue = parseNumber(salaryMatch[3]);
        metadata.salaryTo = isNaN(toValue) ? undefined : toValue; // Set to undefined if NaN
      } else {
        metadata.salaryTo = metadata.salaryFrom; // If only one number, assume it's both from and to
      }
      lineProcessedAsMetadata = true;
    }

    const locationMatch = lowerCaseLine.match(/^location:\s*(.*)/); // Use lowerCaseLine
    if (locationMatch && metadata.location === undefined) { // Check for undefined
      metadata.location = locationMatch[1].trim();
      lineProcessedAsMetadata = true;
      // Attempt to extract country from location if not already found (case-insensitive for country code)
      if (metadata.country === undefined) {
        // This regex is a basic heuristic; more robust geo-parsing might be needed for production
        const countryFromLocationMatch = metadata.location.match(/,\s*([a-zA-Z]{2,3})$/); // Allow 2 or 3 letter country codes, case-insensitive
        if (countryFromLocationMatch) {
          metadata.country = countryFromLocationMatch[1].trim().toUpperCase();
        }
      }
    }

    const interviewTypeMatch = lowerCaseLine.match(/^interview type:\s*(.*)/); // Use lowerCaseLine
    if (interviewTypeMatch && metadata.interviewType === undefined) { // Check for undefined
        metadata.interviewType = interviewTypeMatch[1].trim();
        lineProcessedAsMetadata = true;
    }

    // Remote work detection (can be a standalone line or part of a Description description)
    // This is a more Description check, refineFlatTextToCanonicalSections will do more detailed parsing
    const remoteWorkKeywords = ['remote', 'work from home', 'wfh', 'hybrid']; // Keywords for remote work
    if (metadata.localRemoteWork === undefined && metadata.overseasRemoteWork === undefined && remoteWorkKeywords.some(keyword => lowerCaseLine.includes(keyword))) { // Check for undefined
        // If any remote keyword is found, assume local remote unless specified otherwise
        metadata.localRemoteWork = lowerCaseLine.includes('local') || !lowerCaseLine.includes('overseas');
        metadata.overseasRemoteWork = lowerCaseLine.includes('overseas');
        // If it just says "remote" without local/overseas, default to localRemoteWork = true
        lineProcessedAsMetadata = true;
    }

    // If the line was primarily for metadata and not a section header, don't add it to content.
    // This prevents "Employment: Full time" from appearing in the Description section.
    // However, if it's a section header, it should be processed as such.
    const isSectionHeader = trimmed.match(/^\[(.*?)\]$/) !== null; // Check if it's a section header (e.g., [Requirements])

    if (lineProcessedAsMetadata && !isSectionHeader) {
        // If it was a metadata line and not a section header, we consider it consumed.
        continue;
    }

    // Section header detection
    const bracketMatch = trimmed.match(/^\[(.*?)\]$/);
    if (bracketMatch) {
      const rawHeader = bracketMatch[1];
      const candidates = rawHeader.split('/').map(h => h.trim());

      const mapped = candidates.find(c => sectionHeaderMap[c]);
      const key = mapped ? sectionHeaderMap[mapped] : candidates[0];
      currentSection = key;
      if (!segments[currentSection]) segments[currentSection] = '';
      continue; // This continue is correct for section headers
    }

    // Standard fallback continuation
    segments[currentSection] += (segments[currentSection] ? '\n' : '') + trimmed;
  }

  // Final cleanup
  for (const key in segments) {
    segments[key] = segments[key].trim();
  }

  // Explicitly set default "blank" values for any remaining undefined metadata fields
  // This ensures all keys are present in the output JSON, even if not found in text.
  metadata.jobTitle = metadata.jobTitle ?? "";
  metadata.company = metadata.company ?? "";
  metadata.employmentType = metadata.employmentType ?? "";
  metadata.experienceLevel = metadata.experienceLevel ?? "";  
  metadata.country = metadata.country ?? "";
  metadata.location = metadata.location ?? "";
  metadata.department = metadata.department ?? "";
  metadata.salaryCurrency = metadata.salaryCurrency ?? "";
  metadata.salaryFrom = metadata.salaryFrom ?? 0;
  metadata.salaryTo = metadata.salaryTo ?? 0;
  metadata.interviewType = metadata.interviewType ?? "";
  metadata.localRemoteWork = metadata.localRemoteWork ?? false;
  metadata.overseasRemoteWork = metadata.overseasRemoteWork ?? false;

  return {
    structured: segments,
    metadata,
    unknownHeaders: unknownHeaders.filter(Boolean),
  };
}
