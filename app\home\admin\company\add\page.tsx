/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { z } from "zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CompanySchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countryList } from "@/data/location/countryList";
import { Textarea } from "@/components/ui/textarea";
import { UploadButton } from "@/utils/uploadthing";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Image from "next/image";
import { XIcon } from "lucide-react";
import { toast } from "sonner";
import { CreateCompany } from "@/actions/company/createCompany";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { TextEditor } from "@/components/general/TextEditor";
import { cn } from "@/lib/utils";

export default function AdminAddCompanyPage() {
  const [pending, setPending] = useState(false);
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [open, setOpen] = useState(false);

  const newCompanyForm = useForm<z.infer<typeof CompanySchema>>({
    resolver: zodResolver(CompanySchema),
    defaultValues: {
      id: "",
      name: "",
      location: "",
      address: "",
      about: "",
      description: "",
      email: "",
      phone: "",
      logo: "",
      website: "",
      xAccount: "",
      linkedIn: "",
      tin: "",
      benefits: [],
      foreignerRatio: 0,
      englishUsageRatio: 0,
    },
  });

  async function onSubmit(data: z.infer<typeof CompanySchema>) {
    try {
      setPending(true);
      // New company mode – create the company.
      const response = await CreateCompany(data);
      if (response?.success) {
        toast.success(response.success);
        setSuccess(response!.success);
        setOpen(false);
        window.location.href = "/admin/company";
      } else if (response?.error) {
        toast.error(response.error);
        setError(response!.error);
      }
    } catch (error) {
      console.error("Error in company form", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setPending(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">New Company</Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay>
          <DialogContent
            className={cn(
              "max-w-[750px] w-full", // Your desired width
              "bg-background",
              "data-[state=open]:animate-in data-[state=closed]:animate-out",
              "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
              "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
              "fixed top-[50%] left-[50%] z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200",
              "max-h-[calc(100vh-2rem)] overflow-y-auto"
            )}
          >
            <DialogHeader>
              <DialogTitle>New Company</DialogTitle>
            </DialogHeader>
            <Form {...newCompanyForm}>
              <form
                className="space-y-6"
                onSubmit={newCompanyForm.handleSubmit(onSubmit)}
              >
                <div className="flex text-muted-foreground text-sm justify-between">
                  <div>Adding a new company.</div>
                  <div>
                    <span className="text-red-500 text-sm">*</span> Required
                    fields{" "}
                  </div>
                </div>
                {/* COMPANY INFO */}
                <div
                  className="grid grid-cols-2 gap-4 overflow-y-auto pr-2"
                  style={{ maxHeight: "calc(100vh - 250px)" }}
                >
                  {/* Company Name */}
                  <div>
                    <FormField
                      control={newCompanyForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Name<span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter company name"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company Logo */}
                  <div className="row-span-2">
                    <FormField
                      control={newCompanyForm.control}
                      name="logo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Logo<span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <div className="w-full h-full">
                              {field.value ? (
                                <div className="flex justify-center items-center">
                                  <div className="relative w-fit">
                                    <Image
                                      src={field.value}
                                      alt="Company Logo"
                                      width={100}
                                      height={100}
                                      className="rounded-lg"
                                    />
                                    <Button
                                      type="button"
                                      variant="destructive"
                                      size="icon"
                                      className="absolute -top-2 -right-2"
                                      onClick={() => field.onChange("")}
                                    >
                                      <XIcon className="size-4" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="w-full h-full flex justify-center items-center">
                                  <UploadButton
                                    endpoint="imageUploader"
                                    onClientUploadComplete={(res) => {
                                      toast.success(
                                        "Logo uploaded successfully."
                                      );
                                      field.onChange(res[0].ufsUrl);
                                    }}
                                    onUploadError={(error: Error) => {
                                      toast.error(`Error! ${error.message}`);
                                    }}
                                    onUploadBegin={(name) => {
                                      toast.info(`Uploading: ${name}`);
                                    }}
                                    appearance={{
                                      button:
                                        "ut-ready:bg-gray-500 ut-uploading:cursor-not-allowed rounded-md bg-gray-600 w-full",
                                      container:
                                        "w-full p-2 flex flex-col items-center gap-2 rounded-md border-cyan-300 bg-slate-200",
                                      allowedContent: "text-xs text-gray-500",
                                    }}
                                    content={{
                                      button({ ready }) {
                                        if (ready)
                                          return <div>Upload File</div>;
                                        return "Getting ready...";
                                      },
                                      allowedContent({ ready, isUploading }) {
                                        if (!ready)
                                          return "Checking allowed file types";
                                        if (isUploading)
                                          return "Uploading file...";
                                        return (
                                          <p>
                                            `File you can upload: images (max
                                            size of 4MB)`
                                          </p>
                                        );
                                      },
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company Location */}
                  <div>
                    <FormField
                      control={newCompanyForm.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Location
                            <span className="text-red-500">*</span>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="w-full cursor-pointer">
                                <SelectValue placeholder="Select Country" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="p-4">
                              <SelectGroup className="cursor-pointer">
                                {countryList.map((country) => (
                                  <SelectItem
                                    key={country.code}
                                    value={country.code}
                                    className="cursor-pointer"
                                  >
                                    <span className="emoji">
                                      {country.flagEmoji}
                                    </span>
                                    <span className="p-2">{country.name}</span>
                                  </SelectItem>
                                ))}
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company Website */}
                  <div className="row-start-3">
                    <FormField
                      control={newCompanyForm.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://www.yourcompany.com"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company X Account */}
                  <div className="row-start-3">
                    <FormField
                      control={newCompanyForm.control}
                      name="xAccount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>X (twitter) Account</FormLabel>
                          <FormControl>
                            <Input placeholder="@yourcompany" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company LinkedIn */}
                  <div>
                    <FormField
                      control={newCompanyForm.control}
                      name="linkedIn"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>LinkedIn</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://www.linkedin.com/company/..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company TIN */}
                  <div className="row-start-4">
                    <FormField
                      control={newCompanyForm.control}
                      name="tin"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tax Information Number (TIN)</FormLabel>
                          <FormControl>
                            <Input placeholder="TIN" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company About */}
                  <div className="col-span-2">
                    <FormField
                      control={newCompanyForm.control}
                      name="about"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            About (one liner or update later)
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Short description about your company or a tagline. Example: Web design and development company."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Company Description */}
                  <div className="col-span-2 row-start-6">
                    <FormField
                      control={newCompanyForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Description (longer or update later)
                          </FormLabel>
                          <FormControl>
                            {/* Ensure the 'field' from react-hook-form is compatible or adapt it */}
                            <TextEditor field={{
                              value: field.value || "",
                              onChange: field.onChange,
                              onBlur: field.onBlur,
                              ref: field.ref
                            }} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex flex-col">
                  <ErrorMessage message={error} />
                  <SuccessMessage message={success} />
                </div>
                <div className="flex flex-col">
                  <Button
                    type="submit"
                    className="w-full cursor-pointer"
                    disabled={pending}
                    size="lg"
                  >
                    {pending ? "Submitting..." : "Create Company"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </DialogOverlay>
      </DialogPortal>
    </Dialog>
  );
}
