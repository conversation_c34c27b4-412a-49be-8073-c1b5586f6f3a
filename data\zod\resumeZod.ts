import { z } from "zod";
import { PublishStatus } from '@prisma/client';

export const HeaderSectionSchema = z.object({
  name: z.string(),
  email: z.string().optional(),
  phone: z.array(z.string()).optional(),
  website: z.string().optional(),
  xaccount: z.string().optional(),
  linkedin: z.string().optional(),
  github: z.string().optional(),
  address: z.string().optional(),
  shortAbout: z.string().optional(),
});

export const SummarySectionSchema = z.object({
  content: z.string().optional(),
});

export const ExperienceSectionSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  title: z.string().optional(),
  company: z.string().optional(),
  employmentType: z.string().optional(),
  location: z.string().optional(),
  description: z.string().optional(), // Changed from z.array(z.string()) for Tiptap
  highlights: z.array(z.string()).optional(),
  technologies: z.array(z.string()).optional(),
  _tempId: z.string().optional(), // Add _tempId as an optional field
});

export const EducationSectionSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  degree: z.string().optional(),
  institution: z.string().optional(),
  location: z.string().optional(),
  details: z.string().optional(), // Changed from z.array(z.string())
  gpa: z.string().optional(),
  honors: z.array(z.string()).optional(),
  _tempId: z.string().optional(), // Add _tempId as an optional field
});

export const AdditionalSectionSchema = z.object({
  header: z.string().optional(),
  details: z.string().optional(), // Changed from z.array(z.string()) for Tiptap
  type: z.string().optional(),
  _tempId: z.string().optional(), // Add _tempId as an optional field
});

export const StandardFieldsSchema = z.object({
  header: HeaderSectionSchema,
  summary: SummarySectionSchema,
  experience: z.array(ExperienceSectionSchema),
  education: z.array(EducationSectionSchema),
  skills: z.array(z.string()),
});

export const ResumeDataSchema = z.object({
  standardFields: StandardFieldsSchema,
  additionalSections: z.array(AdditionalSectionSchema),
});


// Define the complete resume schema
export const ResumeSchema = z.object({
    id: z.string().uuid(),
    fileId: z.string().nullish(),
    picture: z.string().nullish(),
    status: z.nativeEnum(PublishStatus).default(PublishStatus.DRAFT),
    fileContent: z.string().nullish(),
    resumeData: ResumeDataSchema.nullish(),
});

export type ResumeDataSchemaType = z.infer<typeof ResumeDataSchema>;

export const EvaluationScoreSchema = z.object({
    overall: z.number().optional(),
    experience_quality: z.number().optional(),
    education: z.number().optional(),
    skills: z.number().optional(),
    completeness: z.number().optional(),
});

export const EvaluationAssessmentSchema = z.object({
    strengths: z.array(z.string()).optional(),
    improvements: z.array(z.string()).optional(),
    industry_recommendations: z.array(z.string()).optional(),
});

export const EvaluationDetailsSchema = z.object({
    experience: z.string().optional(),
    education: z.string().optional(),
    skills: z.string().optional(),
});

export const ResumeEvaluationSchema = z.object({    
    scores: EvaluationScoreSchema.nullish(),
    assessment: EvaluationAssessmentSchema.nullish(),
    evaluation_details: EvaluationDetailsSchema.nullish(),
});
  
// Types for the resumeData JSON structure
export type ResumeData = {
    standardFields:    StandardFields;
    additionalSections: AdditionalSection[];
};

export type StandardFields = {
    header:     HeaderSection;
    summary:    SummarySection;
    experience: ExperienceSection[];
    education:  EducationSection[];
    skills:     string[];
};

export type HeaderSection = {
    name:      string;
    email?:     string;
    phone:     string[];
    website?:   string;
    xaccount?:   string;
    linkedin?:  string;
    github?:    string;
    address?:   string;
    shortAbout?: string;
};

export type SummarySection = {
    content?:   string;
};

export type ExperienceSection = {
    startDate:    string;      // YYYY-MM or YYYY
    endDate:      string;      // YYYY-MM or YYYY or "Present"
    title:        string;
    company:      string;
    employmentType: string;
    location?:     string;
    description?:  string;     // Changed from string[] - stores JSON string from TextEditor
    highlights?:   string[];   // Optional key achievements
    technologies?: string[];   // Optional technologies used
    _tempId?:      string;     // Add _tempId here as well
};

export type EducationSection = {
    startDate:    string;      // YYYY
    endDate:      string;      // YYYY
    degree:       string;
    institution:  string;
    location?:     string;
    details?:      string;     // Changed from string[] - stores JSON string from TextEditor
    gpa?:          string;     
    honors?:       string[];
    _tempId?:      string;     // Add _tempId here as well
};

export type AdditionalSection = {
  header: string;
  details?: string; // Changed from string[] - stores JSON string from TextEditor
  type?: string;
  _tempId?:      string;     // Add _tempId here as well
};

export const JobAdditionalSectionSchema = z.object({
    section_name: z.string(),
    details: z.array(z.string()).optional(),
});
  
export const JobSalaryRangeSchema = z.object({
    min: z.string().optional(),
    max: z.string().optional(),
    currency: z.string().optional(),
});
  
export const JobStandardFieldsSchema = z.object({
    title: z.string(),
    company: z.string(),
    location: z.string(),
    employment_type: z.string(),
    experience_level: z.string(),
    education_required: z.string(),
    salary_range: JobSalaryRangeSchema.or(z.array(JobSalaryRangeSchema)),
    requirements: z.array(z.string()),
    responsibilities: z.array(z.string()),
    skills_required: z.array(z.string()),
});

export const JobDataSchema = z.object({
    standardFields: JobStandardFieldsSchema,
    additionalSections: z.array(JobAdditionalSectionSchema),
});

export type JobData = {
    standardFields:    JobStandardFields;
    additionalSections: JobAdditionalSection[];
};

export type JobStandardFields = {
    title: string;
    company: string;
    location: string;
    employment_type: string;
    experience_level: string;
    education_required: string;
    salary_range: SalaryRange[];
    requirements: string[];
    responsibilities: string[];
    skills_required: string[];
};

export type SalaryRange = {
    min?: string;
    max?: string;
    currency?: string;
};

export type JobAdditionalSection = {    
    section_name: string;
    details?: string[];
};
