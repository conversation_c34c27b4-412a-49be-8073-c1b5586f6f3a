import { Badge } from "@/components/ui/badge";
import { GlobeIcon, MapPinIcon, PenBoxIcon, Users2Icon } from "lucide-react";
import { prisma } from "@/lib/prisma/prismaClient";
import { notFound } from "next/navigation";
import { getFlagEmoji } from "@/data/location/countryList";
import { JsonToHTML } from "@/components/general/JsonToHTML";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import arcjet, { detectBot } from "@/lib/arcjet/arcjet";
import { request, tokenBucket } from "@arcjet/next";
import { auth } from "@/lib/auth/auth";
import { FaLinkedin } from "react-icons/fa";
import { QuestionTooltip } from "@/components/info/InfoTooltip";
import { JobApplications } from "@/components/company/JobApplications";
import { Suspense } from "react";
import { JobResumeMatches } from "@/components/company/JobResumeMatches";
import { snakeCaseToTitleCase } from "@/utils/stringHelpters";
import { benefits } from "@/components/job/ListOfBenefits";

const aj = arcjet.withRule(
  detectBot({
    mode: "DRY_RUN",
    allow: ["CATEGORY:SEARCH_ENGINE", "CATEGORY:PREVIEW"],
  })
);

function getClient(session: boolean) {
  if (session) {
    return aj.withRule(
      tokenBucket({
        mode: "DRY_RUN",
        capacity: 100,
        interval: 60,
        refillRate: 30,
      })
    );
  } else {
    return aj.withRule(
      tokenBucket({
        mode: "DRY_RUN",
        capacity: 100,
        interval: 60,
        refillRate: 10,
      })
    );
  }
}

async function getJob(id: string) {
  const [jobData] = await Promise.all([
    await prisma.jobPost.findUnique({
      where: {
        id: id,
        status: "ACTIVE",
      },
      select: {
        id: true,
        jobTitle: true,
        employmentType: true,
        experienceLevel: true,
        location: true,
        country: true,
        salaryCurrency: true,
        salaryFrom: true,
        salaryTo: true,
        jobDescription: true,
        listingDuration: true,
        interviewType: true,
        localRemoteWork: true,
        overseasRemoteWork: true,
        skills: true,
        languageRequirements: true,
        tags: true,
        status: true,
        createdAt: true,
        company: {
          select: {
            name: true,
            about: true,
            logo: true,
            location: true,
            website: true,
            benefits: true,
            xAccount: true,
            linkedIn: true,
            englishUsageRatio: true,
            foreignerRatio: true,
          },
        },
      },
    }),
  ]);

  if (!jobData) {
    return notFound();
  }
  return { jobData };
}

type Params = Promise<{ id: string }>;

export default async function JobDetailsPage({ params }: { params: Params }) {
  const { id } = await params;
  const session = await auth();

  const req = await request();
  const decision = await getClient(!!session).protect(req, { requested: 10 });

  if (decision.isDenied()) {
    throw new Error("Access Denied");
  }

  const { jobData } = await getJob(id);
  const locationFlag = getFlagEmoji(jobData.country);

  return (
    <div className="md:grid lg:grid-cols-3 gap-8 pl-10 pr-10">
      <div className="flex w-full col-span-3">
        <div className="grid grid-cols-2 gap-4 w-full">
          <div className="flex flex-col p-2 border-2 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="flex font-semibold items-center gap-2">
                <Users2Icon className="w-4 h-4" /> Job Applicants
              </h3>
              <QuestionTooltip content="Candidates that applied for this job." />
            </div>
            <div className="flex">
              <Suspense>
                <JobApplications jobId={id} />
              </Suspense>
            </div>
          </div>
          <div className="flex flex-col p-2 border-2 rounded-lg">
            <div className="flex items-center justify-between">
              <h3 className="flex font-semibold items-center gap-2">
                <PenBoxIcon className="w-4 h-4" /> Resume Matches
              </h3>
              <QuestionTooltip content="Candidates that match your job requirements based on our AI matching algorithm with scores of 70% or higher." />
            </div>
            <div className="flex ">
              <Suspense>
                <JobResumeMatches jobId={id} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-8 col-span-2">
        {/* header */}
        <div className="flex flex-col">
          <div>
            <h1 className="text-3xl font-bold">{jobData.jobTitle}</h1>
            <div className="flex gap-2 mt-2">
              <p className="font-medium">{jobData.company.name}</p>
              {jobData.employmentType ? (
                <>
                  <span className="hidden md:inline text-sm text-muted-foreground">*</span>
                  <Badge className="rounded-full" variant="secondary">
                    {snakeCaseToTitleCase(jobData.employmentType)}
                  </Badge>
                </>
              ) : null}
              {jobData.experienceLevel ? (
                <>
                  <span className="hidden md:inline text-sm text-muted-foreground">*</span>
                  <p className="text-sm text-muted-foreground">
                    {jobData.experienceLevel}
                  </p>
                </>
              ) : null}
            </div>
          </div>
          <div>
              <Badge className="rounded-full gap-x-2">
                {locationFlag && <span>{locationFlag}</span>} {jobData.location}{" "}
                {jobData.country}
              </Badge>
          </div>
        </div>

        <section>
          <h3 className="font-semibold mb-4">Required Skills</h3>

          {(() => {
            // Process skills to determine if we should group them
            const parsedSkills = jobData.skills.map((skill) => {
              if (
                typeof skill === "string" &&
                (skill.startsWith("{") || skill.includes('":"'))
              ) {
                try {
                  return JSON.parse(skill);
                } catch (e) {
                  return { name: skill, category: null };
                }
              }
              return { name: skill, category: null };
            });

            // Check if we have any categorized skills
            const hasCategorizedSkills = parsedSkills.some(
              (skill) => skill.category
            );

            if (hasCategorizedSkills) {
              // Group skills by category
              const groupedSkills = parsedSkills.reduce(
                (acc, skill) => {
                  const category = skill.category || "Other";
                  if (!acc[category]) {
                    acc[category] = [];
                  }
                  acc[category].push(skill.name);
                  return acc;
                },
                {} as Record<string, string[]>
              );

              // Sort categories alphabetically
              const sortedCategories = Object.keys(groupedSkills).sort();

              return (
                <div className="space-y-6">
                  {sortedCategories.map((category) => (
                    <div key={category} className="space-y-2">
                      <h4 className="text-sm font-medium text-muted-foreground">
                        {category}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {groupedSkills[category].map(
                          (skillName: any, index: any) => (
                            <Badge
                              key={`${category}-${index}-${skillName}`}
                              variant="default"
                              className="cursor-pointer transition-all hover:scale-105 active:scale-95 text-sm px-4 py-1.5 rounded-full"
                            >
                              {skillName}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              );
            } else {
              // Display skills without grouping for AI-processed posts
              return (
                <div className="flex flex-wrap gap-3">
                  {parsedSkills.map((skill, index) => (
                    <Badge
                      key={`skill-${index}-${skill.name}`}
                      variant="default"
                      className="cursor-pointer transition-all hover:scale-105 active:scale-95 text-sm px-4 py-1.5 rounded-full"
                    >
                      {skill.name}
                    </Badge>
                  ))}
                </div>
              );
            }
          })()}
        </section>

        <section className="mt-6">
          {(() => {
            // Process language requirements
            const parsedLanguages = jobData.languageRequirements.map((lang) => {
              if (
                typeof lang === "string" &&
                (lang.startsWith("{") || lang.includes('":"'))
              ) {
                try {
                  return JSON.parse(lang);
                } catch (e) {
                  return {
                    language: lang,
                    level: null,
                    type: "REQUIRED",
                    certification: "",
                  };
                }
              }
              return {
                language: lang,
                level: null,
                type: "REQUIRED",
                certification: "",
              };
            });

            // Map language codes to readable names
            const languageMap = {
              TAGALOG: "Tagalog",
              CEBUANO: "Cebuano",
              JAPANESE: "Japanese",
              ENGLISH: "English",
              MANDARIN: "Mandarin",
              KOREAN: "Korean",
              VIETNAMESE: "Vietnamese",
            };

            // Map level codes to readable names
            const levelMap = {
              NATIVE: "Native",
              FLUENT: "Fluent",
              BUSINESS: "Business",
              CONVERSATIONAL: "Conversational",
            };

            // Group languages by type (Required vs Preferred)
            const requiredLanguages = parsedLanguages.filter(
              (lang) => lang.type === "REQUIRED" || lang.type === "REQUIRED"
            );

            const preferredLanguages = parsedLanguages.filter(
              (lang) => lang.type === "PREFERRED" || lang.type === "PREFERED"
            );

            return (
                <div className="flex flex-col w-full">
                    <div className="w-full">
                        <h3 className="font-semibold mb-4">Language Requirements</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 w-full gap-2">
                        {/* Required Languages */}
                        <div className="border rounded-lg overflow-hidden">
                            <div className="bg-muted p-3 font-medium text-center border-b">
                                Required
                            </div>
                            <div className="p-4">
                                {requiredLanguages.length > 0 ? (
                                <div className="space-y-2">
                                    {requiredLanguages.map((lang, index) => {
                                    const languageName =
                                        languageMap[
                                        lang.language as keyof typeof languageMap
                                        ] || lang.language;
                                    const levelName =
                                        levelMap[lang.level as keyof typeof levelMap] ||
                                        lang.level;

                                    return (
                                        <div key={`required-${index}`}>
                                        <span className="font-medium">
                                            {languageName}:
                                        </span>{" "}
                                        {levelName}
                                        </div>
                                    );
                                    })}

                                    {/* Show certifications at the bottom if any */}
                                    {requiredLanguages.some(
                                    (lang) => lang.certification
                                    ) && (
                                    <div className="mt-3 pt-3 border-t">
                                        {requiredLanguages
                                        .filter((lang) => lang.certification)
                                        .map((lang, index) => (
                                            <div key={`cert-${index}`} className="text-sm">
                                            {lang.certification}
                                            </div>
                                        ))}
                                    </div>
                                    )}
                                </div>
                                ) : (
                                <div className="text-muted-foreground text-center py-2">
                                    No Data
                                </div>
                                )}
                            </div>
                        </div>

                        {/* Preferred Languages */}
                        <div className="border rounded-lg overflow-hidden">
                            <div className="bg-muted p-3 font-medium text-center border-b">
                                Preferred
                            </div>
                            <div className="p-4">
                                {preferredLanguages.length > 0 ? (
                                <div className="space-y-2">
                                    {preferredLanguages.map((lang, index) => {
                                    const languageName =
                                        languageMap[
                                        lang.language as keyof typeof languageMap
                                        ] || lang.language;
                                    const levelName =
                                        levelMap[lang.level as keyof typeof levelMap] ||
                                        lang.level;

                                    return (
                                        <div key={`preferred-${index}`}>
                                        <span className="font-medium">
                                            {languageName}:
                                        </span>{" "}
                                        {levelName}
                                        </div>
                                    );
                                    })}

                                    {/* Show certifications at the bottom if any */}
                                    {preferredLanguages.some(
                                    (lang) => lang.certification
                                    ) && (
                                    <div className="mt-3 pt-3 border-t">
                                        {preferredLanguages
                                        .filter((lang) => lang.certification)
                                        .map((lang, index) => (
                                            <div key={`cert-${index}`} className="text-sm">
                                            {lang.certification}
                                            </div>
                                        ))}
                                    </div>
                                    )}
                                </div>
                                ) : (
                                <div className="text-muted-foreground text-center py-2">
                                    No Data
                                </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
          })()}
        </section>

        <section>
          <JsonToHTML
            json={((): object | null => {
              if (typeof jobData.jobDescription === "string") {
                if (jobData.jobDescription.trim() === "") {
                  return { type: "doc", content: [] }; // Default empty JSON
                }
                try {
                  return JSON.parse(jobData.jobDescription);
                } catch (e) {
                  return { type: "doc", content: [] }; // Default empty JSON on error
                }
              }
              return jobData.jobDescription; // Pass through if already an object or null/undefined
            })()}
            hideSkills={true}
          />
        </section>
      </div>

      <div className="space-y-8">
        {/* Job details card */}
        <Card className="p-6">
          <h3 className="font-semibold">About the Job</h3>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Apply before
              </span>
              <span className="text-sm">
                {new Date(
                  jobData.createdAt.getTime() +
                    jobData.listingDuration * 24 * 60 * 60 * 1000
                ).toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Posted on</span>
              <span className="text-sm">
                {jobData.createdAt.toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </span>
            </div>
            <br />

            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Employment Type
              </span>
              <span className="text-sm">
                {(() => {
                  // Map employment type codes to readable names
                  const employmentTypeMap = {
                    FULL_TIME: "Full Time",
                    PART_TIME: "Part Time",
                    CONTRACT: "Contract",
                    INTERNSHIP: "Internship",
                  };

                  return (
                    employmentTypeMap[
                      jobData.employmentType as keyof typeof employmentTypeMap
                    ] || jobData.employmentType
                  );
                })()}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Interview Type
              </span>
              <span className="text-sm">
                {(() => {
                  // Map interview type codes to readable names
                  const interviewTypeMap = {
                    ONLINE: "Online",
                    ONSITE: "On-site",
                    HYBRID: "Hybrid",
                  };

                  return (
                    interviewTypeMap[
                      jobData.interviewType as keyof typeof interviewTypeMap
                    ] || jobData.interviewType
                  );
                })()}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Remote (Local)
              </span>
              <span className="text-sm">
                {jobData.localRemoteWork ? "Yes" : "No"}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Remote (Overseas)
              </span>
              <span className="text-sm">
                {jobData.overseasRemoteWork ? "Yes" : "No"}
              </span>
            </div>
          </div>
        </Card>

        {/* Company card */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex gap-3">
              <Image
                src={jobData.company.logo as string}
                alt={"Company logo"}
                width={48}
                height={48}
                className="rounded-full size-12"
              />

              <div className="flex flex-col">
                <h3 className="font-semibold">{jobData.company.name}</h3>
                <p className="text-sm text-muted-foreground">
                  <a
                    href={jobData.company.website || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                  >
                    <MapPinIcon className="w-4 h-4" />
                    <span>{jobData.company.location}</span>
                  </a>
                </p>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  <a
                    href={jobData.company.website || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                  >
                    <GlobeIcon className="w-4 h-4" />
                    <span>Website</span>
                  </a>
                </p>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  <a
                    href={jobData.company.linkedIn || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                  >
                    <FaLinkedin className="w-4 h-4" />
                    <span>LinkedIn</span>
                  </a>
                </p>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {jobData.company.xAccount}
                </p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-3">
              {jobData.company.about}
            </p>
            {/* display ratio */}
            <div className="flex flex-col items-center ml-auto border-2 p-2 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                {/* Foreigner Ratio */}
                <div className="flex flex-col items-center">
                  <p className="text-xs text-muted-foreground mb-1 text-center">
                    Foreigners
                  </p>
                  <div className="relative w-16 h-16">
                    {/* Background circle */}
                    <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>

                    {/* Progress circle - adjust the stroke-dasharray and stroke-dashoffset for the percentage */}
                    <svg
                      className="absolute inset-0 w-full h-full rotate-[-90deg]"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="46"
                        fill="none"
                        stroke="#0e7490"
                        strokeWidth="8"
                        strokeDasharray="289.02652413026095"
                        strokeDashoffset={
                          289.02652413026095 *
                          (1 - jobData.company.foreignerRatio / 100)
                        }
                        strokeLinecap="round"
                      />
                    </svg>

                    {/* Percentage text */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold">
                        {jobData.company.foreignerRatio}%
                      </span>
                    </div>
                  </div>
                  <p className="text-xs font-medium mt-1">
                    {jobData.company.foreignerRatio < 1
                      ? "Not set"
                      : jobData.company.foreignerRatio < 25
                        ? "Minimal"
                        : jobData.company.foreignerRatio < 50
                          ? "Moderate"
                          : jobData.company.foreignerRatio < 75
                            ? "Significant"
                            : "Predominant"}
                  </p>
                </div>

                {/* English Usage Ratio */}
                <div className="flex flex-col items-center">
                  <p className="text-xs text-muted-foreground mb-1 text-center">
                    English Usage
                  </p>
                  <div className="relative w-16 h-16">
                    {/* Background circle */}
                    <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>

                    {/* Progress circle - adjust the stroke-dasharray and stroke-dashoffset for the percentage */}
                    <svg
                      className="absolute inset-0 w-full h-full rotate-[-90deg]"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="46"
                        fill="none"
                        stroke="#0e7490"
                        strokeWidth="8"
                        strokeDasharray="289.02652413026095"
                        strokeDashoffset={
                          289.02652413026095 *
                          (1 - jobData.company.englishUsageRatio / 100)
                        }
                        strokeLinecap="round"
                      />
                    </svg>

                    {/* Percentage text */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold">
                        {jobData.company.englishUsageRatio}%
                      </span>
                    </div>
                  </div>
                  <p className="text-xs font-medium mt-1">
                    {jobData.company.englishUsageRatio < 1
                      ? "Not set"
                      : jobData.company.englishUsageRatio < 25
                        ? "Occasional"
                        : jobData.company.englishUsageRatio < 50
                          ? "Regular"
                          : jobData.company.englishUsageRatio < 75
                            ? "Frequent"
                            : "Primary"}
                  </p>
                </div>
              </div>
            </div>
            <div>
              <section>
                <h3 className="font-semibold mb-4">Benefits</h3>
                <div className="flex flex-wrap gap-3">
                  {jobData.company.benefits.map((benefitId: string) => {
                    const benefit = benefits.find((b) => b.id === benefitId);
                    return benefit ? (
                      <Badge key={benefit.id}>
                        {benefit.icon}
                        {benefit.label}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </section>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
