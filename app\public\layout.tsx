import { Navbar } from "@/components/header/Navbar";
import { Footer } from "@/components/footer/Footer";

export default async function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col bg-athena-gradient w-full overflow-hidden">
        <div className="flex flex-col">
          <div className="w-full px-10">
            <Navbar />
          </div>
          <div className="w-full">
            {children}
          </div>
        </div>
      <Footer />
    </div>
  );
}
