import { getUserJobApplications } from "@/actions/job/getJob";
import { EmptyState } from "@/components/general/EmptyState";
import { MainPagination } from "@/components/general/MainPagination";
import { UserJobApplications } from "@/components/user/UserJobApplications";
import { auth } from "@/lib/auth/auth";

const APPLICATIONS_PAGE_SIZE = 10; // Define page size

export default async function ApplicationsPage(props: {
  // If this page also used route params, they would be typed similarly:
  // params: Promise<{ [key: string]: string | string[] | undefined }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const session = await auth();
  const resolvedSearchParams = await props.searchParams;
  const currentPage = Number(resolvedSearchParams?.page) || 1;

  if (!session?.user?.id) {
    // Should ideally be caught by middleware or auth checks earlier
    return <div>User not authenticated.</div>;
  }

  const result = await getUserJobApplications(session.user.id, currentPage, APPLICATIONS_PAGE_SIZE);

  if (result?.error || !result?.applications || result.applications.length === 0) {
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <h1 className="text-2xl font-semibold">Job Applications</h1>
        <EmptyState
          title="No Job Applications Yet"
          description={result?.error || "You haven't applied to any jobs yet."}
          buttonText="Find a Job"
          href="/public/job/list"
        />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 mt-5 gap-4">
        <UserJobApplications data={result.applications} />
        {result.totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <MainPagination totalPages={result.totalPages} currentPage={currentPage} />
          </div>
        )}
    </div>
  );
}
