import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(req: Request) {
  try {
    const { category, skillName } = await req.json();
    
    // Path to skillsData.ts file
    const filePath = path.join(process.cwd(), 'data', 'job', 'skillsData.ts');
    
    // Read the current file content
    let content = await fs.readFile(filePath, 'utf-8');
    
    // Parse the existing skillsData object
    const skillsDataMatch = content.match(/export const defaultSkillsData: SkillsData = ({[\s\S]*?});/);
    if (!skillsDataMatch) {
      return NextResponse.json({ error: 'Could not parse skills data' }, { status: 400 });
    }
    
    const skillsDataObj = eval(`(${skillsDataMatch[1]})`);
    
    // Add the new category if it doesn't exist
    if (!skillsDataObj[category]) {
      skillsDataObj[category] = [];
    }
    
    // Add the new skill if it doesn't exist
    const skillExists = skillsDataObj[category].includes(skillName);
    
    if (!skillExists) {
      skillsDataObj[category].push(skillName);
      
      // Sort skills alphabetically
      skillsDataObj[category].sort((a: string, b: string) => 
        a.localeCompare(b)
      );
      
      // Convert the object back to a formatted string
      const newSkillsDataString = JSON.stringify(skillsDataObj, null, 2)
        .replace(/"/g, "'")  // Use single quotes for consistency
        .replace(/\n\s\s/g, '\n    '); // Fix indentation
      
      // Replace the old skillsData object with the new one
      const newContent = content.replace(
        /export const defaultSkillsData: SkillsData = ({[\s\S]*?});/,
        `export const defaultSkillsData: SkillsData = ${newSkillsDataString};`
      );
      
      // Write back to file
      await fs.writeFile(filePath, newContent, 'utf-8');
      
      return NextResponse.json({ success: true, message: 'Skill added successfully' });
    } else {
      return NextResponse.json({ message: 'Skill already exists' }, { status: 409 });
    }
    
  } catch (error) {
    console.error('Error adding skill:', error);
    return NextResponse.json({ error: 'Failed to add skill' }, { status: 500 });
  }
}