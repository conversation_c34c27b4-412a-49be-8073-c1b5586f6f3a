'use client'

import { ThemeToggle } from "../theme/ThemeToggle"
import { UserMenu } from "./UserMenu"
import { useSession } from "next-auth/react";
import { buttonVariants } from "../ui/button";
import Link from "next/link";
import { UserRole, UserType } from "@prisma/client";
import { usePathname } from 'next/navigation';

export const UserTopbar = ({ collapsed }: { collapsed: boolean }) => {
        const { data: session, status } = useSession();
        const pathname = usePathname();
          
  return (
    <header
      className={`fixed top-0 left-0 right-0 h-14 px-6 bg-background border-b flex items-center justify-between z-40 transition-all duration-300 ease-in-out ${collapsed ? 'ml-16' : 'ml-56'}`}
    >
      <h1 className="flex text-xs text-muted-foreground items-center">
        Docs | FAQ
      </h1>
      <div className="flex items-center gap-5">
          {/* <ThemeToggle /> */}
          {session?.user?.userType === UserType.COMPANY && (
            pathname !== "/home/<USER>/job/post" && (
              <Link 
                className={buttonVariants({ size: "lg" })} 
                href="/public/job/list"
              >
                View Job Posts
              </Link>
            )
          )}
          {session?.user?.userType === UserType.JOB_SEEKER && session?.user?.onboarded && (
            pathname !== "/public/job/list" && pathname !== "/home/<USER>/applications" && pathname !== "/home/<USER>/favorites" && pathname !== "/home/<USER>/matches" && (
              <Link 
                className={buttonVariants({ size: "lg" })} 
                href="/public/job/list"
              >
                Find a Job
              </Link>
            )
          )}
          {session?.user ? (
            <UserMenu
              email={session.user.email ?? ""}
              name={session.user.name ?? ""}
              image={session.user.image ?? ""}
              role={session.user.role ?? UserRole.USER}
              type={session.user.userType ?? UserType.JOB_SEEKER}
              onboarded={session.user.onboarded ?? false}
            />
          ) : (
            <Link 
              className={buttonVariants({ size: "lg" })} 
              href="/auth/signin"
            >
              Sign In
            </Link>
          )}
        </div>
    </header>
  )
}
