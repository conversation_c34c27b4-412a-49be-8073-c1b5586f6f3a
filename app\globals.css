@import url('https://fonts.googleapis.com/css2?family=Noto+Color+Emoji&display=swap');
@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    background: linear-gradient(135deg, #020612 0%, #0a0e19 50%, #06123a 100%) !important;
    color: var(--foreground);
  }
  select, .emoji, [data-emoji] {
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI Symbol", "Noto Color Emoji Compat", sans-serif;
    font-variation-settings: "FILL" 1;
  }
}

.bg-athena-gradient {
  background: linear-gradient(135deg, #020612 0%, #0a0e19 50%, #06123a 100%);
}

.sticky-sidebar {
  position: sticky;
  top: 65px; /* Height of your navbar */
  height: calc(100vh - 65px); /* Full height minus navbar */
  overflow-y: auto; /* Add scroll if content exceeds height */
}

/* Ensure proper height inheritance */
html,
body,
#__next {
  height: 100%;
  scroll-behavior: smooth;
}

/* Grid height control */
main {
  min-height: calc(100vh - [navbar-height] - [footer-height]);
}

/* Add these styles to ensure dialogs are properly centered */
[data-slot="dialog-portal"] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;
}

[data-slot="dialog-content"] {
  position: fixed !important;
  z-index: 10000;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

[data-slot="dialog-overlay"] {
  position: fixed !important;
  z-index: 9999;
  inset: 0;
}

.DialogOverlay {
  background: rgba(0 0 0 / 0.5);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  place-items: center;
  overflow-y: auto;
}

.DialogContent {
    width: 700px;
  min-width: 300px;
  background: white;
  padding: 30px;
  border-radius: 4px;
  max-width: screen;
}

/* Ensure Radix portals render high enough */
[data-radix-popper-content-wrapper] {
  z-index: 12000 !important;
}

.tiptap {
  :first-child {
    margin-top: 0;
  }
}

.prose ul li,
.prose ol li {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  line-height: 1.0 !important; 
}

/* Target list markers within the prose container */
.prose ul li::marker,
.prose ol li::marker {
  color: black; /* Default color for light mode */
}

/* Target list markers within the prose container in dark mode */
.dark .prose ul li::marker,
.dark .prose ol li::marker {
  color: white; /* Color for dark mode */
}

* {
  scroll-behavior: smooth;
}

.text-gradient {
  background: radial-gradient(
    64.18% 64.18% at 71.16% 35.69%,
    #def9fa 0%,
    #bef3f5 20%,
    #9dedf0 40%,
    #7de7eb 60%,
    #5ce1e6 80%,
    #33bbcf 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


/* Smooth, Aqua Transition */
.bg-blue-gradient {
  background: linear-gradient(158deg,
    #def9fa 0%,
    #bef3f5 20%,
    #9dedf0 40%,
    #7de7eb 60%,
    #5ce1e6 80%,
    #33bbcf 100%);
}

.bg-blue-gradient-reverse {
  background: linear-gradient(158deg,
    #33bbcf 0%,   /* Darkest color at the start (0%) */
    #5ce1e6 20%,
    #7de7eb 40%,
    #9dedf0 60%,
    #bef3f5 80%,
    #def9fa 100%  /* Lightest color at the end (100%) */
  );
}

/* Smooth, Purple Transition */
.bg-purple-gradient {
  background: linear-gradient(158deg,
    #efdefa 0%,
    #dfbef5 20%,
    #cb9df0 40%,
    #ba7deb 60%,
    #b35ce6 80%,
    #8433cf 100%);
}

/* Smooth Light to Dark */
.bg-black-gradient {
  background: linear-gradient(145deg,
    #ffffff 0%,
    #6d6d6d 40%,
    #11101d 100%);
}

/* Inverted version */
.bg-black-gradient-2 {
  background: linear-gradient(190deg,
    #ffffff 0%,
    #6d6d6d 40%,
    #11101d 100%);
}

/* Subtle Fade to Dark */
.bg-gray-gradient {
  background: linear-gradient(153deg,
    rgba(255, 255, 255, 0) 0%,
    #14101d 100%);
}


.bg-discount-gradient {
  background: linear-gradient(125.17deg, #272727 0%, #11101d 100%);
}

.box-shadow {
  box-shadow: 0px 20px 100px -10px rgba(66, 71, 91, 0.1);
}

.sidebar {
  -webkit-animation: slide-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-top {
  0% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes slide-top {
  0% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.feature-card:hover {
  background: var(--black-gradient);
  box-shadow: var(--card-shadow);
}

.feedback-container .feedback-card:last-child {
  margin-right: 0px;
}

.feedback-card {
  background: transparent;
}

.feedback-card:hover {
  background: var(--black-gradient);
}

.blue__gradient {
  background: linear-gradient(180deg, rgba(188, 165, 255, 0) 0%, #214d76 100%);
  filter: blur(123px);
}

.pink__gradient {
  background: linear-gradient(90deg, #f4c4f3 0%, #fc67fa 100%);
  filter: blur(900px);
}

.white__gradient {
  background: rgba(255, 255, 255, 0.6);
  filter: blur(750px);
}