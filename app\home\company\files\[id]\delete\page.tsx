"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect } from "react";
import { useParams } from 'next/navigation'
import { useGetFile } from "@/hooks/useGetFile";

export default function CandidateFileDeletePage() {
    const params = useParams<{ id: string }>()
    const id = params.id;
    const router = useRouter();
    const { fileQuery, isLoading, isError, file, deleteMutation, isDeleting, deleteError, deleteResult } = useGetFile(id);

    useEffect(() => {
        if (deleteResult && deleteResult.success) {
            toast.success("Resume deleted successfully");
            router.push("/home/<USER>/resume/list");
        }
    }, [deleteResult, router]);

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (isError || !file) {
        return (
            <div>
                <h1>File Not Found</h1>
                <p>This file could not be found.</p>
            </div>
        );
    }

    const handleDeleteAction = () => {
        deleteMutation.mutate(id);
    };

    return (
        <>
            <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between p-6">
                <h1 className="text-2xl font-bold pt-10">Delete Resume</h1>
                <p className="text-sm text-muted-foreground pt-2">Are you sure you want to delete file <b>{file?.fileName}</b>?</p>
                <p className="text-sm text-muted-foreground">This file cannot be deleted if there is an associated AI-processed resume. You must delete the resume first.</p>
                <p className="text-sm text-muted-foreground pt-2">This action cannot be undone.</p>
                <Button
                    variant="destructive"
                    size="sm"
                    className="mt-4 cursor-pointer"
                    onClick={handleDeleteAction}
                    disabled={isDeleting}
                >
                    {isDeleting ? "Deleting..." : "Delete"}
                </Button>
                {deleteError && (
                    <p className="text-red-500 mt-2">Error deleting file. Please try again.</p>
                )}
            </div>
        </>
    );
}
