"use server";

import { z } from "zod";
import { CompanySchema } from "@/data/zod/zodSchema";
import { prisma } from "@/lib/prisma/prismaClient";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function UpdateCompany(data: z.infer<typeof CompanySchema>) {
  const session = await auth();
  // Middleware should handle redirection if not authenticated.
  // This check is for robustness; if session or user is missing here on a protected route,
  // it indicates an unexpected server state.
  if (!session?.user?.id) {
    throw new Error("User authentication failed or user ID is missing.");
  }
  
  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const validateData = CompanySchema.parse(data);

    if (validateData) {
      // Create a new company record
      const company = await prisma.company.update({
        where: {
          id: validateData.id,
        },
        data: {
          name: validateData.name,
          location: validateData.location,
          address: validateData.address,
          about: validateData.about,
          description: validateData.description,
          email: validateData.email,
          phone: validateData.phone,
          logo: validateData.logo,
          website: validateData.website,
          xAccount: validateData.xAccount,
          linkedIn: validateData.linkedIn,
          tin: validateData.tin,
          benefits: validateData.benefits,
          foreignerRatio: validateData.foreignerRatio,
          englishUsageRatio: validateData.englishUsageRatio,
          userId: session?.user?.id as string,
        },
      });
    }

    return {
      success: "Success! Company information was updated.",
    };
  } catch (error) {
    return { error: `Error was encountered, please try again. ${error}` };
  }
}
