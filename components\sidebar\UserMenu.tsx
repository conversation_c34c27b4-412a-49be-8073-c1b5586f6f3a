"use client";

import { CogIcon, LogOut, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { useState } from "react";
import clsx from "clsx";
import { signOut as nextAuthClientSignOut } from "next-auth/react";
import Link from "next/link";

interface UserMenuProps {
  collapsed: boolean;
}

export const UserMenu = ({ collapsed }: UserMenuProps) => {
    const [isActive, setIsActive] = useState(false);

    const onSignOut = async () => {
        await nextAuthClientSignOut({ callbackUrl: "/" });    
        setIsActive(false);
    };

  return (
    <Popover>
      <PopoverTrigger asChild onMouseLeave={() => setIsActive(false)}>
        <Button
          onClick={() => setIsActive(!isActive)}
          variant="ghost"
          className={clsx(
            "flex items-center justify-start gap-x-2 text-slate-500 text-sm font-[500] transition-all hover:text-slate-600 hover:bg-yellow-300/20 cursor-pointer w-full",
            isActive &&
              "text-sky-700 justify-start bg-sky-200/20 hover:bg-yellow-200/20 hover:text-sky-200 cursor-pointer w-full"
          )}
        >
          <div className="flex items-center text-start gap-x-2 py-4 text-sky-200  text-sm font-[400]">
            <User className="mr-0" size={22} />
            {!collapsed && "Account"}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        side="top"
        className="w-48 bg-white dark:bg-zinc-800 text-black dark:text-white border dark:border-zinc-700"
      >
        <div className="flex flex-col justify-start space-y-2">
          <Link href="/home/<USER>/settings" passHref legacyBehavior>
            <Button
              variant="ghost"
              className="w-full justify-start cursor-pointer"
            >
              <CogIcon size={16} strokeWidth={2} className="opacity-60 mr-2" />
              Settings
            </Button>
          </Link>
          <Button
            variant="ghost"
            className="justify-start cursor-pointer"
            onClick={onSignOut}
          >
            <LogOut size={16} strokeWidth={2} className="opacity-60 mr-2" />
            <span className="text-sm">Sign Out</span>
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};
