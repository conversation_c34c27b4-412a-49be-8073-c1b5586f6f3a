"use client";

import { DocumentViewerWord } from "@/components/general/DocumentViewerWord";
import { DocumentViewerPdf } from "@/components/general/DocumentViewerPdf";

export function JobDetailsDoc({ url, fileType }: { url: string, fileType: string }) {

    if (!url) {
        return (
            <>
                <p>No document found for this job.</p>
            </>
        )
    }

    return (
        <>
          <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between px-4 bg-white">
            {fileType === "PDF" ? (
                <DocumentViewerPdf 
                    documentUrl={url}
                    documentType={"application/pdf"} 
                    title="Job Description" 
                />
    
            ):(
                <DocumentViewerWord 
                    documentUrl={url}
                    title="Job Description"
                />
            )}
          </div>
        </>
      );
}