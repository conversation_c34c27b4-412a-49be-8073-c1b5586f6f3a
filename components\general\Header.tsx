import { Poppins } from "next/font/google";
import clsx from "clsx";

const font = Poppins({
    subsets: ["latin"],
    weight: ["600"],
});

interface HeaderProps {
    caption: string;
    label: string;
}

export const Header = ({ caption, label }: HeaderProps) => {
    return (
        <div className="w-full flex flex-col gap-y-4 items-center justify-center">
            <h1 className={clsx(font.className, "text-3xl font-semibold")}>
                {caption}
            </h1>
            <p className="text-muted-foreground text-sm">
                {label}
            </p>
        </div>
    );
}