import { NextResponse } from "next/server";
import { auth } from "./lib/auth/auth";
import {
  DEFAULT_LOGIN_REDIRECT,
  apiAuthPrefix,
  authRoutes,
  publicRoutes,
  apiRoutes,
  PRIVATE_ROUTES, // Import PRIVATE_ROUTES
  ONBOARDING_PATH, // Import ONBOARDING_PATH
  userRoute,
  companyRoute,
  adminRoute
} from "@/routes";
import { UserRole, UserType } from "@prisma/client";

const PUBLIC_JOB_POSTS_ROOT = "/public"; // Main landing for unauthenticated users

export default auth((req) => { // Removed async as it's not strictly needed here
  const { nextUrl, auth: session } = req; 
  const { pathname: path } = nextUrl;
  const isLoggedIn = !!session?.user;
  const user = session?.user; 

//   console.log("Middleware: path=", path, { isLoggedIn, userId: user?.id, onboarded: user?.onboarded, role: user?.role, userType: user?.userType });

  // 1. Allow NextAuth.js specific API routes and other custom API routes
  if (path.startsWith(apiAuthPrefix) || apiRoutes.includes(path)) {
    return NextResponse.next();
  }

  // Logic to identify dynamic public profiles like /[username]
  const pathSegments = path.split('/').filter(Boolean);
  let isDynamicPublicProfile = false;
  // Check if it's a single segment path (e.g., /username) and not the root path itself.
  if (path !== "/" && pathSegments.length === 1) {
    const segment = pathSegments[0];
    // A segment is a dynamic public profile if it's NOT a predefined private/reserved segment
    // AND the path itself is NOT an explicitly listed public route (e.g. /public, /auth/signin).
    // This prevents /public, /auth, etc. from being misidentified as dynamic profiles.
    if (!PRIVATE_ROUTES.includes(segment) && !publicRoutes.includes(path)) {
      isDynamicPublicProfile = true;
    }
  }

  // Determine if the route is public.
  // A route is public if: it's in publicRoutes, OR starts with /public/ (excluding onboarding), OR is a dynamic public profile.
  const isPublicRoute = publicRoutes.includes(path) || 
                        (path.startsWith(PUBLIC_JOB_POSTS_ROOT + "/") && path !== ONBOARDING_PATH) || 
                        isDynamicPublicProfile;
  const isAuthRoute = authRoutes.includes(path);
  const isOnboardingRoute = path === ONBOARDING_PATH;

  if (isLoggedIn && user) {
    // Logged-in user logic:

    // Priority 1: If on an auth route (like /signin), redirect away.
    if (isAuthRoute) {
      // If a logged-in user hits an auth route, redirect them to their
      // appropriate page (onboarding or dashboard) instead of a generic public page.
      let determinedRedirect = DEFAULT_LOGIN_REDIRECT; // Fallback
      if (!user.onboarded) {
        determinedRedirect = ONBOARDING_PATH;
      } else {
        // User is onboarded, determine dashboard
        if (user.role === UserRole.SUPERADMIN && user.userType === UserType.OWNER) {
          determinedRedirect = `${adminRoute}/dashboard`;
        } else if (user.userType === UserType.COMPANY) {
          determinedRedirect = `${companyRoute}/dashboard`;
        } else if (user.userType === UserType.JOB_SEEKER) {
          determinedRedirect = `${userRoute}/dashboard`;
        } else if (user.userType === UserType.RECRUITER) {
          determinedRedirect = `${userRoute}/recruiter`;
        } else if (user.userType === UserType.TENANT) {
          determinedRedirect = `${userRoute}/tenant`;
        }
      }
      return NextResponse.redirect(new URL(determinedRedirect, req.url));
    }

    // Priority 2: Onboarding.
    // If user is not onboarded, not on the onboarding page itself, and the current path is NOT a public route,
    // redirect to onboarding.
    if (!user.onboarded && !isOnboardingRoute && !isPublicRoute) {
    //   console.log(`Middleware: User ${user.id} not onboarded. Redirecting to ${ONBOARDING_PATH} from ${path}`);
      return NextResponse.redirect(new URL(ONBOARDING_PATH, req.url));
    }

    // Priority 3: Role/Type-based dashboard redirection for onboarded users.
    // This applies if the user is onboarded, not on an onboarding/public/auth route,
    // and not already within their designated area.
    if (user.onboarded && !isOnboardingRoute && !isPublicRoute && !isAuthRoute) {
      const pathIsUserArea = path.startsWith(userRoute);
      const pathIsCompanyArea = path.startsWith(companyRoute);
      const pathIsAdminArea = path.startsWith(adminRoute);

      // SUPERADMIN: Redirect to admin dashboard if not already in an admin area.
      if ((user.role === UserRole.SUPERADMIN) && !pathIsAdminArea) {
        return NextResponse.redirect(new URL(`${adminRoute}/dashboard`, req.url));
      }
      // COMPANY: Redirect to company dashboard if not already in a company area.
      else if (user.userType === UserType.COMPANY && !pathIsCompanyArea) {
        return NextResponse.redirect(new URL(`${companyRoute}/dashboard`, req.url));
      }
      // JOB_SEEKER: Redirect to user dashboard if not already in a user area.
      else if (user.userType === UserType.JOB_SEEKER && !pathIsUserArea) {
        return NextResponse.redirect(new URL(`${userRoute}/dashboard`, req.url));
      }
    }

    // If none of the above redirect conditions for logged-in users are met, allow access.
    // This means the user is:
    // - On a public route
    // - On the onboarding route
    // - On an auth route (already handled by redirecting away, but this is a fallback)
    // - Onboarded and already in their correct role-specific area or another permitted protected page.
    // console.log(`Middleware: Allowing access for logged-in user ${user.id} to path: ${path}`);
    return NextResponse.next();

  } else {
    // Logged-out user logic:

    // Allow access to authentication routes (e.g., /auth/signin, /auth/signup)
    if (isAuthRoute) {
    //   console.log(`Middleware: Logged-out user. Allowing access to auth route: ${path}`);
      return NextResponse.next();
    }

    // If the route is generally public (this now includes dynamic public profiles), allow access.
    if (isPublicRoute) {
      return NextResponse.next();
    }

    // For all other paths, redirect unauthenticated users to the main public job posts page.
    // console.log(`Middleware: Logged-out user. Path ${path} is not public or auth. Redirecting to ${PUBLIC_JOB_POSTS_ROOT}.`);
    return NextResponse.redirect(new URL(PUBLIC_JOB_POSTS_ROOT, req.url));
  }
});

export const config = {
  matcher: [
    // Match all routes except for Next.js internals, static files, and common image/font types.
    '/((?!_next|favicon.ico|.*\\.(?:css|js|mjs|png|jpg|jpeg|gif|svg|webp|json|xml|txt|woff|woff2|ttf|eot|otf)).*)',
  ],
};
