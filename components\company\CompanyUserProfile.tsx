"use client";

import { useSession } from "next-auth/react";
import { GetUserById } from "@/actions/user/getUser";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import {
  CalendarIcon,
  GlobeIcon,
  FileTextIcon,
  PhoneIcon,
  MailIcon,
  MapPinIcon,
} from "lucide-react";
import { formatDate } from "@/utils/formatDate";
import { useEffect, useState } from "react";
import { FaGithub, FaLinkedin } from "react-icons/fa";
import { UploadPicture } from "../general/UploadPicture";
import { EditUserProfile } from "../user/EditUserProfile";

export function CompanyUserProfile() {
  const { data: session } = useSession();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      if (!session?.user?.id) return;

      setLoading(true);
      try {
        const response = await GetUserById(session.user.id as string);
        if (response?.success) {
          setData(response.data); 
        }
      } catch (err) {
        console.error("Error:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [session?.user?.id as string]);

  if (!data && loading) return <div>Loading profile...</div>;

  return (
    <Card className="p-6">
      <div className="grid grid-cols-1 sm:grid-cols-[auto_1fr] gap-8">
        {/* Left Column - Profile Image */}
        <div className="w-48 flex-shrink-0 justify-self-center sm:justify-self-start">
                <Avatar className="w-48 h-48 border-2">
                    <AvatarImage
                    src={data?.image || ""}
                    alt={`${data.firstName} ${data.lastName}`}
                    />
                    <AvatarFallback className="text-4xl">
                    {data.firstName?.[0]}
                    {data.lastName?.[0]}
                    </AvatarFallback>
                </Avatar>
            <div className="flex w-full items-center justify-center text-sm">
                <UploadPicture type={1}/>
            </div>
        </div>

        {/* Right Column - Profile Details */}
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="flex justify-between items-start w-full">
            <div>
              <h1 className="text-2xl font-bold">
                {data.firstName} {data.lastName}
              </h1>
              <p className="text-md text-muted-foreground">
                Role: {data.role}
              </p>
            </div>

            <div className="flex gap-2">
                <EditUserProfile />
            </div>
          </div>

          {/* Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-[180px_180px_1fr] gap-x-6 gap-y-4 items-start text-sm">
            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-primary">
                <MailIcon className="w-4 h-4" />
                <span>{data.email}</span>
              </div>
              <div className="flex items-center gap-2 text-primary">
                <PhoneIcon className="w-4 h-4" />
                <span>{data.mobilePhone}</span>
              </div>
              <div className="flex items-center gap-2 text-primary">
                <CalendarIcon className="w-4 h-4" />
                <span>{data.birthDate ? formatDate(data.birthDate) : ""}</span>
              </div>
              <div className="flex items-center gap-2 text-primary">
                <MapPinIcon className="w-4 h-4" />
                <span></span>
              </div>
            </div>

            {/* Links */}
            <div className="space-y-2">
              <a
                href={data.portfolio || "#"}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-primary hover:underline"
              >
                <GlobeIcon className="w-4 h-4" />
                <span>Portfolio</span>
              </a>
              <a
                href={data.linkedin || "#"}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-primary hover:underline"
              >
                <FaLinkedin className="w-4 h-4" />
                <span>LinkedIn</span>
              </a>
              <a
                href={data.github || "#"}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-primary hover:underline"
              >
                <FaGithub className="w-4 h-4" />
                <span>GitHub</span>
              </a>
              <a
                href={data.writing || "#"}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-primary hover:underline"
              >
                <FileTextIcon className="w-4 h-4" />
                <span>Writing</span>
              </a>
            </div>

            {/* About Section */}
            <div className="md:row-span-2 bg-muted/20 rounded-lg max-h-30 overflow-y-auto overflow-x-hidden scroll-smooth scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent hover:scrollbar-thumb-muted-foreground/50 border">
              <h2 className="text-md font-semibold mb-2 pt-2 pl-2">
                About me:
              </h2>
              <p className="text-muted-foreground whitespace-pre-wrap leading-relaxed text-sm p-2">
                {data.about}
              </p>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
