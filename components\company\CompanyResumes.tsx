"use client";

import {
  <PERSON>,
  <PERSON>Title,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@radix-ui/react-dropdown-menu";
import { MainPagination } from "@/components/general/MainPagination";
import { Button } from "@/components/ui/button";
import {
  EyeIcon,
  MoreHorizontal,
} from "lucide-react";
import Link from "next/link";
import { ResumeEvaluationDialog } from "@/components/user/resume/ResumeEvaluationBank";
import { QuestionTooltip } from "@/components/info/InfoTooltip";
import { AppliedJobsDialog } from "@/components/company/AppliedJobsDialog";
import { ResumeMatchesDialog } from "@/components/company/ResumeMatchesDialog";
import Image from "next/image";
import { UploadCompanyResume } from "./UploadCompanyResume";
import PdfImage from "@/public/icons/pdf.png";
import DocxImage from "@/public/icons/docx.png";
import { AlertDialogDelete } from "../general/AlertDialogDelete";
import { toast } from "sonner";
import { DeleteResume } from "@/actions/resume/userResumeActions";

interface CompanyResumeListProps {
  paginatedResumes: any[];
  currentPage: number;
  totalPages: number;
}

export function CompanyResumeList({
  paginatedResumes,
  currentPage,
  totalPages,
}: CompanyResumeListProps) {

    const onDelete = async (id: string) => {
        try {            
            const result = await DeleteResume(id);
            if (result?.success) {
                toast.success(result.success);
                window.location.reload();
            } else {
                toast.error(`{Failed to delete resume: ${result?.error}`);
            }
        } catch (error) {
            toast.error("An unexpected error occurred.");            
        } finally {
        }
    }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Company Resume Bank</CardTitle>
          <CardDescription>
            <div className="flex items-center justify-between gap-2 pb-4">
              <span className="flex">These are the resumes that have been uploaded by you or your team.</span>
                <UploadCompanyResume companyId={paginatedResumes[0]?.companyId} />
            </div>
            <div className="flex w-full border-1 rounded-lg p-2 bg-green-100/10">
                AI automatically processes, evaluates, and matches uploaded resume to your existing job posts. These resumes will appear in the list soon. If not found here, please check your uploads folder for file status.
            </div>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead style={{ width: "5%" }}>Date</TableHead>
                <TableHead style={{ width: "7.5%" }}>Name</TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Contact</TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Applied{" "}
                <QuestionTooltip content="Number of your company job posts that this candidate has applied." /></TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Matches{" "}
                <QuestionTooltip content="Number of your company job posts that matches this candidate resume based on our AI matching algorithm with scores of 70% or higher." /></TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Resume</TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Evaluation</TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">File</TableHead>
                <TableHead style={{ width: "7.5%" }} className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedResumes.map((resume, index) => (
                <TableRow
                  key={resume.id}
                  className={index % 2 === 1 ? "bg-muted/30" : ""}
                >
                  <TableCell>{resume.createdAt.toLocaleDateString()}</TableCell>
                  <TableCell className="flex items-center gap-2">                        
                        <Image
                          src={resume.picture || "/icons/profile.png"}
                          alt={`${resume.name || ""}`}
                          width={28}
                          height={28}
                          className="rounded-full"
                        />
                    {resume.name || ""}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col items-center text-xs">
                      <span>{resume.email || ""}</span>
                      <span>{resume.phone || ""}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <AppliedJobsDialog
                      count={resume.user?.appliedJobPosts?.length || 0}
                      userId={resume.user?.id}
                      companyId={resume.companyId}
                      userName={`${resume.name || ""}`}
                    />
                  </TableCell>
                  <TableCell className="text-center">
                    <ResumeMatchesDialog
                      count={resume.resumeMatches?.length || 0}
                      resumeId={resume.id}
                      companyId={resume.companyId}
                      userName={`${resume.name || ""}`}
                    />
                  </TableCell>
                  <TableCell className="text-center">
                    <Link
                      href={`/home/<USER>/resumedoc/${resume.id}`}
                      passHref
                      target="_blank"
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        className="cursor-pointer"
                      >
                        <EyeIcon className="h-4 w-4 mr-1" />
                      </Button>
                    </Link>
                  </TableCell>
                  <TableCell className="text-center">
                    <ResumeEvaluationDialog
                      resumeEval={resume.resumeEvaluation}
                      applicantName={`${resume.name || ""}`}
                    />
                  </TableCell>
                  <TableCell className="text-center justify-center">
                    <div className="flex justify-center">
                        <a
                        href={resume.file?.url}
                        target="_blank"
                        className="text-primary hover:underline cursor-pointer"
                        title="Uploaded Resume"
                        >
                            <Image
                                src={
                                    resume.file?.fileType === "PDF" ? PdfImage : DocxImage
                                }
                                alt="Resume"
                                width={30}
                                height={30}
                                className="rounded-lg"
                                title={resume.file?.fileName}
                            />
                        </a>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="cursor-pointer"
                        >
                          <MoreHorizontal />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild className="flex cursor-pointer w-full justify-center">
                          <Link
                            href={`/home/<USER>/resume/${resume.id}`}
                          >
                            <EyeIcon className="mr-2" /> Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild className="flex w-full cursor-pointer justify-center">
                          <AlertDialogDelete id={resume.id} title="Delete Resume" description="This will delete the resume." onDelete={onDelete} label="Delete" />
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <MainPagination
                totalPages={totalPages}
                currentPage={currentPage}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
