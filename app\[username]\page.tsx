import { redirect } from "next/navigation";
import Link from "next/link";
import { Metadata } from "next";
import { getUserData } from "./utils";
import { FullResume } from "@/components/user/resume/FullResume";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ username: string }>;
}): Promise<Metadata> {
  const { username } = await params;
  const { user_id, resume } = await getUserData(username);

  if (!user_id) {
    return {
      title: "User Not Found",
      description: "This user profile could not be found.",
      robots: "noindex, nofollow",
    };
  }

  if (!resume?.resumeData || resume.status !== "LIVE") {
    return {
      title: "Resume Not Found",
      description: "This resume could not be found.",
      robots: "noindex, nofollow",
    };
  }

  return {
    title: `${resume.resumeData.standardFields.header.name}'s Resume`,
    description: resume.resumeData.standardFields.summary.content,
    robots: "noindex, nofollow",
    openGraph: {
      title: `${resume.resumeData.standardFields.header.name}'s Resume`,
      description: resume.resumeData.standardFields.summary.content,
      images: [
        {
          url: `${APP_URL}/${username}/og`,
          width: 1200,
          height: 630,
          alt: "Profile",
        },
      ],
    },
  };
}

export default async function PublicResumePage({
  params,
}: {
  params: Promise<{ username: string }>;
}) {
  const { username } = await params;

  const { user_id, resume } = await getUserData(username);

  if (!user_id) redirect(`/?usernameNotFound=${username}`);
  if (!resume?.resumeData || resume.status !== "LIVE")
    redirect(`/?idNotFound=${user_id}`);

  const profilePicture = resume.picture as string;

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Person",
    name: resume.resumeData.standardFields.header.name,
    image: profilePicture,
    jobTitle: resume.resumeData.standardFields.header.shortAbout,
    description: resume.resumeData.standardFields.summary.content,
    email:
      resume.resumeData.standardFields.header.email &&
      `mailto:${resume.resumeData.standardFields.header.email}`,
    url: `${APP_URL}/${username}/og`,
    skills: resume.resumeData.standardFields.skills,
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="flex flex-col max-w-4xl mx-auto mt-10 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between px-4 bg-white shadow-lg">
       <h1 className="text-2xl font-bold pt-10">Résumé</h1>
        <FullResume
          username={username}
          resume={resume?.resumeData}
          profilePicture={profilePicture}
        />
      </div>

      <div className="text-center mt-8 mb-4">
        <Link
          href={`/?ref=${username}`}
          className="text-design-gray font-mono text-sm"
        >
          Made by{" "}
          <span className="text-design-black underline underline-offset-2">
            EdisonAIX.com
          </span>
        </Link>
        Inspired by Self.so
      </div>
    </>
  );
}
