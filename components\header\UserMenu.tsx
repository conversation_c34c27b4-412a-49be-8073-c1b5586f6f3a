"use client";

import {
  ChevronDown,
  LogOut,
  UserIcon,
  LayoutDashboard,
  Building2,
  Users,
  Briefcase,
  UserCircle,
  FilePenLine,
  PenBoxIcon,
  ListCheck,
  FileUser,
  FolderArchive,
  Building,
  UserCog,
  GraduationCap,
  Heart,
  FileInputIcon,
  FileTextIcon,
  SquareUser,
  Warehouse,
  Banknote,
  TicketCheck,
  MessageSquareText,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link"; 
import { UserRole, UserType } from "@prisma/client";
import { signOut as nextAuthClientSignOut } from "next-auth/react";

interface UserDropdownProps {
  email: string;
  name: string;
  image: string;
  type: UserType;
  role: UserRole;
  onboarded: boolean;
}

const adminRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: Briefcase,
        label: "Job List",
        href: "/home/<USER>/company/job/list",
        color: "text-amber-500"
    },
    {
        icon: Building,
        label: "Companies",
        href: "/home/<USER>/company/list",
        color: "text-indigo-500"
    },
    {
        icon: UserCog,
        label: "HR Managers",
        href: "/home/<USER>/company/manager/list",
        color: "text-fuchsia-500"
    },
    {
        icon: GraduationCap,
        label: "Job Seekers",
        href: "/home/<USER>/jobseeker/list",
        color: "text-blue-500"
    },
    {
        icon: SquareUser,
        label: "Recruiters",
        href: "/home/<USER>/recruiter/list",
        color: "text-red-300"
    },
    {
        icon: Warehouse,
        label: "Tenants",
        href: "/home/<USER>/tenant/list",
        color: "text-slate-500"
    },
    {
        icon: TicketCheck,
        label: "Subscriptions",
        href: "/home/<USER>/subscription/list",
        color: "text-violet-500"
    },
    {
        icon: Banknote,
        label: "Payments",
        href: "/home/<USER>/payment/list",
        color: "text-green-500"
    },
    {
        icon: UserCircle,
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-blue-700"
    },
    {
        icon: MessageSquareText,
        label: "Feedback",
        href: "/home/<USER>/feedback/list",
        color: "text-gray-700"
    },
];

const companyRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: PenBoxIcon,         
        label: "Job List",
        href: "/home/<USER>/job/list",
        color: "text-amber-500"
    },
    {
        icon: FileInputIcon,        
        label: "Job Matches",
        href: "/home/<USER>/matches",
        color: "text-emerald-500"
    },
    {
        icon: FileUser,
        label: "Applications",
        href: "/home/<USER>/applications",
        color: "text-rose-500"
    },
    {
        icon: ListCheck,
        label: "Shortlist",
        href: "/home/<USER>/shortlist",
        color: "text-teal-500"
    },
    {
        icon: Users,
        label: "Resumes",
        href: "/home/<USER>/resume/list",
        color: "text-green-500"
    },
    {
        icon: FolderArchive,
        label: "Uploads",
        href: "/home/<USER>/files",
        color: "text-yellow-400"
    },
    {
        icon: Building2,         
        label: "Company",
        href: "/home/<USER>/account",
        color: "text-indigo-500"
    },
    {
        icon: UserCircle,
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-purple-500"
    },
];

const userRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: FileInputIcon,        
        label: "Job Matches",
        href: "/home/<USER>/matches",
        color: "text-emerald-500"
    },
    {
        icon: FilePenLine,        
        label: "Applications",
        href: "/home/<USER>/applications",
        color: "text-amber-500"
    },    
    {
        icon: Heart,
        label: "Favorites",
        href: "/home/<USER>/favorites",
        color: "text-rose-500"  
    },
    {
        icon: FileTextIcon,        
        label: "Resume",
        href: "/home/<USER>/resume",
        color: "text-teal-500"
    },
    {
        icon: UserCircle,        
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-purple-500"
    }
];


const onBoardRoute = [
  {
    icon: UserIcon,
    label: "On Boarding",
    href: "/home/<USER>",
    color: "text-emerald-500"
  },
];

export function UserMenu({
  name,
  image,
  email,
  type,
  role,
  onboarded,
}: UserDropdownProps) {


    const onSignOut = async () => {
        // Use the signOut function from next-auth/react for a more robust client-side sign out
        await nextAuthClientSignOut({ callbackUrl: "/" });
    };


  const getRoutes = () => {
    const routes = [];
    
    if (role === "SUPERADMIN") {
        routes.push(...adminRoutes);
    } else if (onboarded) {
        if (type === "COMPANY") routes.push(...companyRoutes);
        if (type === "JOB_SEEKER") routes.push(...userRoutes);
    } else {
        routes.push(...onBoardRoute);
    }
    
    return routes;
};

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="cursor-pointer">
        <Button variant="ghost" className="h-auto p-0 hover:bg-transparent">
          <Avatar>
            <AvatarImage src={image || ""} />
            <AvatarFallback  className="bg-gray-200 border">{name?.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          <ChevronDown size={16} strokeWidth={2} className="ml-2 opacity-60" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48 p-2 mt-2" align="end">
        <DropdownMenuLabel className="flex flex-col gap-1 items-center justify-center">
          <span className="text-sm font-medium text-foreground">Welcome!</span>
          <span className="text-sm font-medium text-foreground">{name}</span>
          <span className="text-xs text-muted-foreground">{email}</span>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {getRoutes().map((route) => (
            <DropdownMenuItem
              key={route.href}
              asChild
              className="cursor-pointer"
            >
              <Link href={route.href}>
                <route.icon
                  size={16}
                  strokeWidth={2}
                  className={`mr-2 ${route.color}`}
                />
                <span className="text-sm">{route.label}</span>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <button
            type="button"
            onClick={onSignOut}
            className="w-full flex items-center gap-2 px-2 py-1.5 cursor-pointer"
          >
            <LogOut size={16} strokeWidth={2} className="text-shadow-yellow-800 mr-2" />
            <span className="text-sm">Sign Out</span>
          </button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
