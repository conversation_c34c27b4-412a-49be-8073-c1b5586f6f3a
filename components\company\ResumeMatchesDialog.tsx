"use client";

import {
  <PERSON>alog,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ResumeMatchDialog } from "@/components/user/resume/ResumeMatchEvaluation";
import { useUserJobMatches } from "@/hooks/useUserJobMatches";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { EyeIcon } from "lucide-react";
import Image from "next/image";
import { useUserJobShortlist } from "@/hooks/useJobShortlist";

export function ResumeMatchesDialog({
  count, //filled when coming from job list
  resumeId, //filled when coming from resume list
  companyId, //filled when coming from job list
  jobId, //filled when coming from job list
  userName, //filled when coming from resume list
}: {
  count: number;
  resumeId?: string;
  companyId?: string;
  jobId?: string;
  userName?: string;
}) {

    const { matchesQuery } = useUserJobMatches(resumeId, companyId as string);
    const { jobMatchesQuery } = jobId ? useUserJobMatches(undefined, undefined, jobId) : { jobMatchesQuery: { isLoading: false, data: null } };
    const { userShortlistQuery } = jobId ? useUserJobShortlist(matchesQuery.data?.[0]?.jobId, resumeId) : { userShortlistQuery: { isLoading: false, data: null } };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="link"
          className="text-primary hover:underline cursor-pointer"
        >
          {count}
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[650px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between pt-6 pl-6 pr-6">
              <DialogTitle className="text-lg font-semibold">
                Resume Matches
              </DialogTitle>
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 cursor-pointer"
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
            <div className="flex flex-col items-center px-6">                
                {userName}
                <em className="text-sm text-muted-foreground">
                Job match evaluation with score of 70% or higher.</em>
            </div>
            <div className="overflow-y-auto px-6 pb-6">
                {jobId ? (
                    // Show job applicants if jobId is provided
                    jobMatchesQuery.isLoading || !jobMatchesQuery.data ? (
                        <div className="text-center py-4">Loading... {JSON.stringify({isLoading: jobMatchesQuery.isLoading, hasData: !!jobMatchesQuery.data})}</div>
                    ) : jobMatchesQuery.data.length === 0 ? (
                        <div className="text-center py-4 text-muted-foreground">
                        No job applicants found.
                        </div>
                    ) : (
                        <ScrollArea className="w-full max-h-[400px]">
                        <Table>
                            <TableHeader>
                            <TableRow>
                                <TableHead style={{ width: "45%" }}>Name</TableHead>
                                <TableHead style={{ width: "15%" }}>Match</TableHead>
                                <TableHead style={{ width: "15%" }}>Evaluation</TableHead>
                                <TableHead style={{ width: "15%" }}>Resume</TableHead>
                                <TableHead style={{ width: "10%" }}>Shortlist</TableHead>
                            </TableRow>
                            </TableHeader>
                            <TableBody>
                                {jobMatchesQuery.data.map((job) => (
                                    <TableRow key={job.id}>
                                    <TableCell
                                        style={{ width: "45%" }}
                                        className="flex items-center gap-2"
                                    >
                                        <Image
                                        src={job.resume?.picture || "/icons/profile.png"}
                                        alt={`${job.resume?.name || ""}`}
                                        width={28}
                                        height={28}
                                        className="rounded-full"
                                        />
                                        {job.resume?.name || ""}
                                    </TableCell>
                                    <TableCell style={{ width: "15%" }}>
                                        <MatchScoreCircle score={job.overall_score} />
                                    </TableCell>
                                    <TableCell style={{ width: "15%" }} className="pl-4">
                                        <ResumeMatchDialog
                                        resumeMatch={job}
                                        applicantName={`${job.resume?.name || ""}`}
                                        />
                                    </TableCell>
                                    <TableCell style={{ width: "15%" }} className="pl-4">
                                        <a
                                        href={`/home/<USER>/resumedoc/${job.resumeId}`}
                                        target="_blank"
                                        className="text-primary hover:underline cursor-pointer"
                                        title="View Resume"
                                        >
                                        <Button
                                            variant="ghost"
                                            className="text-primary hover:underline cursor-pointer"
                                            title="View Resume"
                                        >
                                            <EyeIcon className="w-6 h-6" />
                                        </Button>
                                        </a>
                                    </TableCell>
                                    <TableCell
                                        style={{ width: "10%" }}
                                        className="pl-4 cursor-pointer"
                                    >
                                        <ShortlistCheckbox
                                            jobId={jobId as string}
                                            resumeId={job.resumeId}
                                            initialShortlisted={job.user?.isShortlisted || false}
                                        />
                                    </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                        </ScrollArea>
                    )
                    ) : (
                    matchesQuery.isLoading || !matchesQuery.data ? (
                        <div className="text-center py-4">Loading...</div>
                    ) : matchesQuery.data.length === 0 ? (
                        <div className="text-center py-4 text-muted-foreground">
                        No job matches found
                        </div>
                    ) : (
                    <ScrollArea className="w-full max-h-[400px]">
                    <Table>
                        <TableHeader>
                        <TableRow>
                            <TableHead>Job Title</TableHead>
                            <TableHead>Match Score</TableHead>
                            <TableHead>Evaluation</TableHead>
                            <TableHead>Shortlist</TableHead>
                        </TableRow>
                        </TableHeader>
                        <TableBody>
                        {matchesQuery.data.map((match) => (
                            <TableRow key={match.id}>
                            <TableCell>{match.jobPost.jobTitle}</TableCell>
                            <TableCell className="text-center">
                                <MatchScoreCircle score={match.overall_score} />
                            </TableCell>
                            <TableCell>
                                <ResumeMatchDialog
                                resumeMatch={match}
                                applicantName={userName as string}
                                />
                            </TableCell>
                            <TableCell style={{ width: "10%" }} className="pl-4 cursor-pointer">
                                <ShortlistCheckbox
                                    jobId={matchesQuery.data?.[0]?.jobId as string}
                                    resumeId={resumeId as string}
                                    initialShortlisted={userShortlistQuery.data ? true : false}
                                />
                            </TableCell>
                            </TableRow>
                        ))}
                        </TableBody>
                    </Table>
                    </ScrollArea>
                )
                )}
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
