import { AlertTriangleIcon } from "lucide-react";
import { CardWrapper } from "./CardWrapper";


export const ErrorCard = () => {
  return (
    <CardWrapper
      captionLabel="Error"
      headerLabel="Oops! Something went wrong!"
      backButtonLabel="Back to login"
      backButtonHref="/auth/signin"
      customClassName=""
    >
      <div className="w-full flex justify-center items-center">
        <AlertTriangleIcon className="text-destructive" />
      </div>
    </CardWrapper>
  );
};
