"use client";

import {
  GlobeIcon,
  MailIcon,
  PhoneIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ResumeDataSchemaType } from '@/data/zod/resumeZod';
import { useMemo } from 'react';
import { FaGithub, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { UploadPicture } from '@/components/general/UploadPicture';
import { usePathname } from "next/navigation";
import { normalizeHeaders } from '@/utils/stringHelpters';

interface SocialButtonProps {
  href: string;
  icon: React.ElementType;
  label: string;
}

function SocialButton({ href, icon: Icon, label }: SocialButtonProps) {
  return (
    <Button className="size-8" variant="outline" size="icon" asChild>
      <a
        href={
          href.startsWith('mailto:') || href.startsWith('tel:')
            ? href
            : `${href}${href.includes('?') ? '&' : '?'}ref=selfso`
        }
        aria-label={label}
        target="_blank"
        rel="noopener noreferrer"
      >
        <Icon className="size-4" aria-hidden="true" />
      </a>
    </Button>
  );
}

/**
 * Header component displaying personal information and contact details
 */
export function Header({
  username,
  resumeId,
  header,
  picture,
}: {
  username: string;
  resumeId?: string;
  header: ResumeDataSchemaType['standardFields']['header'];
  picture?: string;
}) {

  const pathname = usePathname();
  const shouldHideUpload = pathname === `/${username}`;

  const prefixUrl = (stringToFix?: string) => {
    if (!stringToFix) return undefined;
    const url = stringToFix.trim();
    return url.startsWith('http') ? url : `https://${url}`;
  };

  const website = useMemo(() => {
    return prefixUrl(header?.website ?? "");
  }, [header?.website]);
  const github = useMemo(() => {
    return prefixUrl(header?.github ?? "");
  }, [header?.github]);
  const xaccount = useMemo(() => {
    return prefixUrl(header?.xaccount ?? "");
  }, [header?.xaccount]);
  const linkedin = useMemo(() => {
    return prefixUrl(header?.linkedin ?? "");
  }, [header?.linkedin]);

  return (
    <header className="flex items-start md:items-center justify-between gap-4 ">
      <div className="flex-1 space-y-1.5">
        <h1 className="text-2xl font-bold" id="resume-name">
          {normalizeHeaders(header?.name)}
        </h1>

        <p className="max-w-md items-center text-pretty font-mono text-xs text-foreground">
          <a
            className="inline-flex gap-x-1.5 align-baseline leading-none hover:underline text-[#9CA0A8]"
            href={`https://www.google.com/maps/search/${encodeURIComponent(
              header?.address ?? "",
            )}`}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={`Location: ${header?.address}`}
          >
            {normalizeHeaders(header?.address || "")}
          </a>
        </p>

        <div
          className="flex gap-x-1 pt-1 font-mono text-sm text-design-resume print:hidden"
          role="list"
          aria-label="Contact links"
        >
          {website && (
            <SocialButton
              href={website}
              icon={GlobeIcon}
              label="Personal website"
            />
          )}
          {header?.email && (
            <SocialButton
              href={`mailto:${header?.email}`}
              icon={MailIcon}
              label="Email"
            />
          )}
          {header?.phone && (
            <SocialButton
              href={`tel:${header?.phone}`}
              icon={PhoneIcon}
              label="Phone"
            />
          )}
          {github && (
            <SocialButton href={`${github}`} icon={FaGithub} label="GitHub" />
          )}
          {xaccount && (
            <SocialButton href={`${xaccount}`} icon={FaTwitter} label="xaccount" />
          )}
          {linkedin && (
            <SocialButton
              href={`${linkedin}`}
              icon={FaLinkedin}
              label="LinkedIn"
            />
          )}
        </div>

        <div
          className="hidden gap-x-2 font-mono text-sm text-design-resume print:flex print:text-[12px]"
          aria-label="Print contact information"
        >
          {website && (
            <>
              <a className="underline hover:text-foreground/70" href={website}>
                {new URL(website).hostname}
              </a>
              <span aria-hidden="true">/</span>
            </>
          )}
          {header?.email && (
            <>
              <a
                className="underline hover:text-foreground/70"
                href={`mailto:${header?.email}`}
              >
                {header?.email}
              </a>
              <span aria-hidden="true">/</span>
            </>
          )}
          {header?.phone && (
            <a
              className="underline hover:text-foreground/70"
              href={`tel:${header?.phone}`}
            >
              {header?.phone}
            </a>
          )}
        </div>
      </div>
      
        <div className="items-center justify-center text-sm">
            <Avatar className="size-20 md:size-28 border-2" aria-hidden="true">
                <AvatarImage src={picture} alt={`${header?.name}'s profile picture`} />
                <AvatarFallback>
                {header?.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
            </Avatar>
            {!shouldHideUpload && (
                <UploadPicture type={2} resumeId={resumeId} />
            )}
        </div>
    </header>
  );
}
