import { serve } from "inngest/next";
import { inngest } from "@/lib/inngest/client";
import {
  helloWorld,
  sendResumeToAI,
  sendJobToAI,
  jobTempPeriodicParse,
  jobToResumePeriodicMatching,
  resumePeriodicEvaluation,
  resumeFilePeriodicParsing,
  resumeToJobPeriodicMatching,
//   consolidatedCronJobs,
  dailyCurrencyUpdate,
  extractResumeData,
  extractResumeContactInfo,
  evaluateExtractedResume,
  extractJobData,
  extractJobInfo,
} from "./functions";


export const { GET, POST, PUT } = serve({
  client: inngest,
  functions: [
    helloWorld,
    sendResumeToAI,
    sendJobToAI,
    jobTempPeriodicParse,
    jobToResumePeriodicMatching,
    resumePeriodicEvaluation,
    resumeFilePeriodicParsing,
    resumeToJobPeriodicMatching,
    // consolidatedCronJobs,
    dailyCurrencyUpdate,
    extractResumeData,
    extractResumeContactInfo,
    evaluateExtractedResume,
    extractJobData,
    extractJobInfo,
  ],
});
