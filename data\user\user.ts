"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { GetVerificationTokenByEmail } from "../site/verificationToken";
import { hashEmail } from "@/utils/hashingHelpers";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { safeParseDecryptedResume } from "@/utils/parseResumeSchema";
import { UserJobSeekerSchema } from "@/data/zod/zodSchema";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { UserPlan } from "@/types/customTypes";
import { v4 as uuidv4 } from "uuid";
import { UserRole, UserStatus } from "@prisma/client";

export const GetUserByEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        emailHash: await hashEmail(email),
      },
      include: {
        company: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const GetUserByHashedEmail = async (email: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        emailHash: email,
      },
      include: {
        company: true,
        accounts: true,
        jobSeeker: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const GetUser = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: id },
      include: {
        company: {
          select: {
            id: true,
            name: true,
          }
        },
        accounts: true,
      },
    });

    if (user) {
      const decryptedUser = {
        ...user,
        firstName: safeDecrypt(user.firstName),
        lastName: safeDecrypt(user.lastName),
        email: safeDecrypt(user.email),
        mobilePhone: safeDecrypt(user.mobilePhone),
        name: safeDecrypt(user.name),
        birthDate: safeDecrypt(user.birthDate),
        image: safeDecrypt(user.image),
        accounts: user.accounts,
        company: user.company,
      };
      console.log("Decrypted User:", decryptedUser);
      return decryptedUser;
    }
    return null;
  } catch (error) {
    console.error("Error in GetUser:", error);
    return null;
  }
};

export const GetUserNameById = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: id },
      select: {
        firstName: true,
        lastName: true,
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const DeleteVerificationToken = async (hashedEmail: string) => {
  try {
    const verificationToken = await GetVerificationTokenByEmail(hashedEmail);
    if (verificationToken) {
      await prisma.userVerificationToken.delete({
        where: { id: verificationToken.id },
      });
    }
  } catch {
    return null;
  }
};

export const GetJobSeekerData = async (id: string) => {
  try {
    const jobSeeker = await prisma.jobSeeker.findUnique({
      where: { id: id },
      include: {
        resumeFile: {
          select: {
            id: true,
            fileUse: true,
            fileName: true,
            fileType: true,
            fileDescription: true,
            fileSize: true,
            key: true,
            url: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            firstName: true,
            lastName: true,
            username: true,
            birthDate: true,
            mobilePhone: true,
            image: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        resume: {
          select: {
            id: true,
            fileContent: true,
            resumeData: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    if (jobSeeker) {
      const jobSeekerFile = {
        ...jobSeeker.resumeFile,
        key: safeDecrypt(jobSeeker.resumeFile?.key),
        url: safeDecrypt(jobSeeker.resumeFile?.url),
      };

      const jobSeekerResume = {
        ...jobSeeker.resume,
        fileContent: safeDecrypt(jobSeeker.resume?.fileContent),
        resumeData: safeDecrypt(jobSeeker.resume?.resumeData),
      };

      const userProfile = {
        ...jobSeeker.user,
        email: safeDecrypt(jobSeeker.user?.email),
        name: safeDecrypt(jobSeeker.user?.name),
        firstName: safeDecrypt(jobSeeker.user?.firstName),
        lastName: safeDecrypt(jobSeeker.user?.lastName),
        birthDate: safeDecrypt(jobSeeker.user?.birthDate),
        mobilePhone: safeDecrypt(jobSeeker.user?.mobilePhone),
        image: safeDecrypt(jobSeeker.user?.image),
      };

      const decryptedUser = {
        ...jobSeeker,
        portfolio: safeDecrypt(jobSeeker.portfolio),
        linkedin: safeDecrypt(jobSeeker.linkedin),
        github: safeDecrypt(jobSeeker.github),
        writing: safeDecrypt(jobSeeker.writing),
        resume: jobSeekerResume,
        user: userProfile,
        resumeFile: jobSeekerFile,
      };

      if (decryptedUser) {
        return {
          success: "Success! File information was saved.",
          jobSeeker: decryptedUser,
        };
      } else {
        return {
          error: "Error! Failed to save file.",
          jobSeeker: null,
        };
      }
    }
  } catch (error) {
    return {
      error: `Error! Failed to save file: ${error}`,
      jobSeeker: null,
    };
  }
};

export const GetUserJobSeekerData = async (userId: string) => {
  try {
    const jobSeeker = await prisma.jobSeeker.findUnique({
      where: { userId: userId },
      include: {
        resumeFile: {
          select: {
            id: true,
            fileUse: true,
            fileName: true,
            fileType: true,
            fileDescription: true,
            fileSize: true,
            key: true,
            url: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            firstName: true,
            lastName: true,
            username: true,
            birthDate: true,
            mobilePhone: true,
            image: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        resume: {
          select: {
            id: true,
            fileContent: true,
            resumeData: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    // console.log({jobSeekerUpdate: jobSeeker});

    if (jobSeeker) {
      const jobSeekerFile = {
        ...jobSeeker.resumeFile,
        key: safeDecrypt(jobSeeker.resumeFile?.key),
        url: safeDecrypt(jobSeeker.resumeFile?.url),
      };

      const jobSeekerResume = {
        ...jobSeeker.resume,
        fileContent: safeDecrypt(jobSeeker.resume?.fileContent),
        resumeData: safeParseDecryptedResume(
          jobSeeker.resume?.resumeData
            ? Buffer.from(jobSeeker.resume.resumeData)
            : null
        ),
      };

      const userProfile = {
        ...jobSeeker.user,
        email: safeDecrypt(jobSeeker.user?.email),
        name: safeDecrypt(jobSeeker.user?.name),
        firstName: safeDecrypt(jobSeeker.user?.firstName),
        lastName: safeDecrypt(jobSeeker.user?.lastName),
        birthDate: safeDecrypt(jobSeeker.user?.birthDate),
        mobilePhone: safeDecrypt(jobSeeker.user?.mobilePhone),
        image: safeDecrypt(jobSeeker.user?.image),
      };

      const decryptedUser = {
        ...jobSeeker,
        portfolio: safeDecrypt(jobSeeker.portfolio),
        linkedin: safeDecrypt(jobSeeker.linkedin),
        github: safeDecrypt(jobSeeker.github),
        writing: safeDecrypt(jobSeeker.writing),
        resume: jobSeekerResume,
        user: userProfile,
        resumeFile: jobSeekerFile,
      };

      if (decryptedUser) {
        return {
          success: "Success! User information was retrieved.",
          jobSeeker: decryptedUser,
        };
      } else {
        return {
          error: "Error! Failed to retrieve user information.",
          jobSeeker: null,
        };
      }
    } else {
        //get from user table
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                email: true,
                name: true,
                firstName: true,
                lastName: true,
                username: true,
                birthDate: true,
                mobilePhone: true,
                image: true,
                createdAt: true,
                updatedAt: true,
            },
        });

        if (user) {
            const decryptedUser = {
                ...user,
                email: safeDecrypt(user.email),
                name: safeDecrypt(user.name),
                firstName: safeDecrypt(user?.firstName),
                lastName: safeDecrypt(user?.lastName),
                birthDate: safeDecrypt(user?.birthDate),
                mobilePhone: safeDecrypt(user?.mobilePhone),
                image: safeDecrypt(user?.image)               
            }

            
            // console.log({decryptedUserUpdate: decryptedUser});

            if (decryptedUser) {
                return {
                success: "Success! User information was retrieved.",
                jobSeeker: decryptedUser,
                };
            } else {
                return {
                error: "Error! Failed to retrieve user information.",
                jobSeeker: null,
                };
            }
        }
    }
  } catch (error) {
    return {
      error: `Error! Failed to retrieve user information: ${error}`,
      jobSeeker: null,
    };
  }
};

export const UpdateJobSeekerProfileData = async (
  data: z.infer<typeof UserJobSeekerSchema>
) => {
  if (!data) {
    return { error: "Missing user info!" };
  }

  try {

    console.log({UserDATAUpdate: data})

    const user = await prisma.user.update({
      where: {
        id: data.userId,
      },
      data: {
        firstName: data.firstname ? encryptToBuffer(data.firstname) : null,
        lastName: data.lastname ? encryptToBuffer(data.lastname) : null,
        birthDate: data.birthDate ? encryptToBuffer(data.birthDate) : null,
        mobilePhone: data.mobilePhone ? encryptToBuffer(data.mobilePhone) : null,
        name: data.firstname && data.lastname ? encryptToBuffer(data.firstname + " " + data.lastname) : null,
      },
    });

    if (user) {

        //check if user has jobseeker data, if none, add it.
        const existJobSeeker = await prisma.jobSeeker.findUnique({
            where: {
                userId: data.userId
            }
        });

        if (!existJobSeeker) {
            const jobSeeker = await prisma.jobSeeker.create({
                data: {
                    id: uuidv4(),
                    userId: data.userId as string,
                    title: data.title,
                    countryCode: data.location,
                    about: data.about,
                    portfolio: data.portfolio ? encryptToBuffer(data.portfolio) : null,
                    linkedin: data.linkedin ? encryptToBuffer(data.linkedin) : null,
                    github: data.github ? encryptToBuffer(data.github) : null,
                    writing: data.writing ? encryptToBuffer(data.writing) : null,
                }
            });
        } else {
            const jobSeeker = await prisma.jobSeeker.update({
                where: {
                    id: data.id,
                    userId: data.userId,
                },
                data: {
                    title: data.title,
                    countryCode: data.location,
                    about: data.about,
                    portfolio: data.portfolio ? encryptToBuffer(data.portfolio) : null,
                    linkedin: data.linkedin ? encryptToBuffer(data.linkedin) : null,
                    github: data.github ? encryptToBuffer(data.github) : null,
                    writing: data.writing ? encryptToBuffer(data.writing) : null,
                },
            });

            if (jobSeeker) {
                const user = await prisma.user.update({
                    where: {
                    id: data.userId,
                    },
                    data: {
                        firstName: encryptToBuffer(data.firstname as string),
                        lastName: encryptToBuffer(data.lastname as string),
                        onboarded: true,
                    },
                });

                return {
                    success:
                    "Success! Your information was saved.",
                };
                } else {
                return {
                    error: "Error! Failed to save user information. Try again.",
                };
            }
        }
    } else {
        return {
            error: "Error! Failed to save user information. Try again.",
        };
    }
  } catch (error) {
    console.log(`Error was encountered updating user, please try again. ${error}`);
    return {
      error: `Error! Failed to save user information: ${error}`,
    };
  }
};

export const getUserIdByUsernameData = async (username: string) => {
  const data = await prisma.userResume.findFirst({
    where: {
      username: username,
    },
    select: {
      userId: true,
    },
  });

  if (data) {
    return data;
  }

  return null;
};

export const updateUserProfilePictureData = async (userId: string, url: string) => {
  const data = await prisma.user.update({
    where: {
      id: userId,
    },
    data: {
      image: encryptToBuffer(url)
    },
  });

  if (data) {
    return data;
  }

  return null;
};

export const GetUserLocationData = async (userId: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        jobSeeker: {
          select: {
            countryCode: true,
          },
        },
        company: {
          select: {
            location: true,
          },
        },
      },
    });

    return user;
  } catch {
    return null;
  }
};

export const setUserPlanData = async (plan: UserPlan) => {

    console.log({USERPLAN: plan})

    try {
        if (plan.companyId){
            // This is a company. Check for existing company plan first
            const compExists = await prisma.userPlan.findFirst({
                where: {
                    companyId: plan.companyId
                },
                select: {
                    id: true
                }
            });

            if (compExists) {

                // Set subscription
                if (plan.subscriptionId) {
                    await prisma.subscription.create({
                        data: {
                            id: uuidv4(),
                            userPlanId: compExists.id,
                            processor: plan.processor || "",
                            userId: plan.userId,
                            subscriptionId: plan.subscriptionId,
                            pricingPlanId: plan.planId,
                            chargedAmount: plan.amount + (plan.amount * 0.20),
                            currency: plan.baseCurrencyCode,
                            billingCycle: plan.paymentFrequency,
                            status: "active"
                        }
                    });
                }

                // Update plan
                const updated = await prisma.userPlan.update({
                    where: {
                        companyId: plan.companyId
                    },
                    data: {
                        userId: plan.userId,
                        pricingPlanId: plan.planId,
                        subscriptionId: plan.subscriptionId || "",
                        amount: plan.amount,
                        paymentFrequency: plan.paymentFrequency,
                        exchangeRate: plan.exchangeRate,
                        exchangeCurrencyCode: plan.exchangeCurrencyCode,
                        exchangeCurrencySymbol: plan.exchangeCurrencySymbol,
                        userLocale: plan.userLocale,
                        baseCurrencyCode: plan.baseCurrencyCode,
                        baseCurrencySymbol: plan.baseCurrencySymbol,
                        updatedAt: new Date()
                    }
                });

                
                if (updated) {
                    // Update user
                    await prisma.user.update({
                        where: {
                            id: plan.userId
                        },
                        data: {
                            planSet: true
                        }
                    });


                    return updated;
                }
            } else {

                //Create plan        
                const newPlan = await prisma.userPlan.create({
                    data: {
                        id: uuidv4(),
                        userId: plan.userId,
                        companyId: plan.companyId,
                        subscriptionId: plan.subscriptionId || "",
                        pricingPlanId: plan.planId,
                        amount: plan.amount,
                        paymentFrequency: plan.paymentFrequency,
                        exchangeRate: plan.exchangeRate,
                        exchangeCurrencyCode: plan.exchangeCurrencyCode,
                        exchangeCurrencySymbol: plan.exchangeCurrencySymbol,
                        userLocale: plan.userLocale,
                        baseCurrencyCode: plan.baseCurrencyCode,
                        baseCurrencySymbol: plan.baseCurrencySymbol
                    }
                });

                
                if (newPlan) {               

                    // Set subscription
                    if (plan.subscriptionId) {
                        await prisma.subscription.create({
                            data: {
                                id: uuidv4(),
                                userPlanId: newPlan.id,
                                processor: plan.processor || "",
                                userId: plan.userId,
                                subscriptionId: plan.subscriptionId,
                                pricingPlanId: plan.planId,
                                chargedAmount: plan.amount + (plan.amount * 0.20),
                                currency: plan.baseCurrencyCode,
                                billingCycle: plan.paymentFrequency,
                                status: "active"
                            }
                        });
                    }

                    // Update user
                    await prisma.user.update({
                        where: {
                            id: plan.userId
                        },
                        data: {
                            planSet: true
                        }
                    });

                    return newPlan;
                }
            }
        } else {
            // This is not a company. Check for existing user plan first
            const userExists = await prisma.userPlan.findFirst({
                where: {
                    userId: plan.userId
                },
                select: {
                    id: true
                }
            });

            if (userExists) {               

                // Set subscription
                if (plan.subscriptionId) {
                    await prisma.subscription.create({
                        data: {
                            id: uuidv4(),
                            userPlanId: userExists.id,
                            processor: plan.processor || "",
                            userId: plan.userId,
                            subscriptionId: plan.subscriptionId,
                            pricingPlanId: plan.planId,
                            chargedAmount: plan.amount + (plan.amount * 0.20),
                            currency: plan.baseCurrencyCode,
                            billingCycle: plan.paymentFrequency,
                            status: "active"
                        }
                    });
                }

                // Update plan
                const updated = await prisma.userPlan.update({
                    where: {
                        userId: plan.userId
                    },
                    data: {
                        pricingPlanId: plan.planId,
                        subscriptionId: plan.subscriptionId || "",
                        amount: plan.amount,
                        paymentFrequency: plan.paymentFrequency,
                        exchangeRate: plan.exchangeRate,
                        exchangeCurrencyCode: plan.exchangeCurrencyCode,
                        exchangeCurrencySymbol: plan.exchangeCurrencySymbol,
                        userLocale: plan.userLocale,
                        baseCurrencyCode: plan.baseCurrencyCode,
                        baseCurrencySymbol: plan.baseCurrencySymbol,
                        updatedAt: new Date()
                    }
                });

                
                if (updated) {
                    // Update user
                    await prisma.user.update({
                        where: {
                            id: plan.userId
                        },
                        data: {
                            planSet: true
                        }
                    });

                    return updated;
                }
            } else {
                //Create plan        
                const newPlan = await prisma.userPlan.create({
                    data: {
                        id: uuidv4(),
                        userId: plan.userId,
                        companyId: plan.companyId,
                        pricingPlanId: plan.planId,
                        subscriptionId: plan.subscriptionId || "",
                        amount: plan.amount,
                        paymentFrequency: plan.paymentFrequency,
                        exchangeRate: plan.exchangeRate,
                        exchangeCurrencyCode: plan.exchangeCurrencyCode,
                        exchangeCurrencySymbol: plan.exchangeCurrencySymbol,
                        userLocale: plan.userLocale,
                        baseCurrencyCode: plan.baseCurrencyCode,
                        baseCurrencySymbol: plan.baseCurrencySymbol
                    }
                });
                
                if (newPlan) {                                    

                    // Set subscription
                    if (plan.subscriptionId) {
                        await prisma.subscription.create({
                            data: {
                                id: uuidv4(),
                                userPlanId: newPlan.id,
                                processor: plan.processor || "",
                                userId: plan.userId,
                                subscriptionId: plan.subscriptionId,
                                pricingPlanId: plan.planId,
                                chargedAmount: plan.amount + (plan.amount * 0.20),
                                currency: plan.baseCurrencyCode,
                                billingCycle: plan.paymentFrequency,
                                status: "active"
                            }
                        });
                    }
                        
                    // Update user
                    await prisma.user.update({
                        where: {
                            id: plan.userId
                        },
                        data: {
                            planSet: true
                        }
                    });

                    return newPlan;
                }
            }
        }

        return null;
    } catch (error) {
        console.log(`Error setting user plan ${error}`)
        return null;
    }
};

export const getUserPlanData = async (userId: string, companyId?: string) => {
    try {
        const userPlan = await prisma.userPlan.findFirst({
            where: {
                userId: userId, // userId is always required
                ...(companyId ? { companyId: companyId } : {}), // Conditionally add companyId
            },
            include: {
                plan: true
            }
        });

        if (userPlan) {
            return userPlan;
        } else {
            return null;
        }
    } catch (error) {
        console.log(`Error fetching user plan: ${error}`)
        return null;        
    }
};

export const setUserStatusData = async (id: string, status: UserStatus) => {
  try {
    const user = await prisma.user.update({
      where: { id: id },
      data: {
        status: status,
      },
    });

    if (user) {
      return user;
    }
    return null;
  } catch (error) {
    console.error("Error in GetUser:", error);
    return null;
  }
};

export const saveUserFeedbackData = async (message: string[], userId?: string) => {
  try {
    const feedback = await prisma.userFeedback.create({
      data: {
        message: message,
        userId: userId,
      },
    });

    if (feedback) {
      return feedback;
    }
    return null;
  } catch {
    return null;
  }
};

export const getUserFeedbackData = async (page: number = 1, pageSize: number = 10) => {
  try {
    const skip = (page - 1) * pageSize;

    const [feedback, totalItems] = await prisma.$transaction([
            prisma.userFeedback.findMany({
                include: {
                    user: true,
                },
                orderBy: {
                    createdAt: 'desc'
                },
                skip: skip,
                take: pageSize,
            }),
            prisma.userFeedback.count(),
        ]);

        if (feedback) {
            console.log("User Feedback:", feedback);
            
            const decryptedFeedbackData = feedback.map(fb => ({
                ...fb,
                user: fb.user ? {
                ...fb.user,
                firstName: safeDecrypt(fb.user.firstName),
                lastName: safeDecrypt(fb.user.lastName),
                email: safeDecrypt(fb.user.email),
                name: safeDecrypt(fb.user.name),
                image: safeDecrypt(fb.user.image),
                } : null,
            }));

            console.log("Decrypted Feedback:", decryptedFeedbackData);


            return { data: decryptedFeedbackData, totalItems };
        }
    return null;
  } catch (error) {
    console.log("Error in GetUserFeedbackData:", error)
    return null;
  }
};


export const GetAdminUsersData = async (page: number = 1, pageSize: number = 10) => {
  try {
    const whereClause = { 
      role: UserRole.ADMIN,
      companyId: {
        not: null
      } 
    };
    const skip = (page - 1) * pageSize;

    const [users, totalItems] = await prisma.$transaction([
      prisma.user.findMany({ 
        where: whereClause,
        include: {
          company: {
            select: {
              id: true,
              name: true,
              logo: true,
              location: true,
              about: true,
              address: true,
            }
          },
          accounts: true,
        },
        skip: skip,
        take: pageSize,
        orderBy: { // Optional: Add default ordering
          createdAt: 'desc'
        }
      }),
      prisma.user.count({ where: whereClause })
    ]);

    if (users && users.length > 0) {
      const decryptedUsers = users.map(user => ({
        ...user,
        firstName: safeDecrypt(user.firstName),
        lastName: safeDecrypt(user.lastName),
        email: safeDecrypt(user.email),
        mobilePhone: safeDecrypt(user.mobilePhone),
        name: safeDecrypt(user.name),
        birthDate: safeDecrypt(user.birthDate),
        image: safeDecrypt(user.image),
      }));
      
    //   console.log("Decrypted Admin Users:", decryptedUsers);

      return { data: decryptedUsers, totalItems };
    }
    return null;
  } catch (error) {
    console.error("Error in GetUser:", error);
    return null;
  }
};

export const getUserSubscriptions = async () => {
    try {
        const subscriptions = await prisma.subscription.findMany({
            include: {
                userPlan: true,
                plan: true,
            }
        });

        return subscriptions;
    } catch (error) {
        console.log(error);
        return null;
    }
};

export const deleteUserFeedbackData = async (id: string) => {
    try {
        const result = await prisma.userFeedback.delete({
            where: {
                id: id
            }
        });

        return result;
    } catch (error) {
        console.log(error);
        return null;
    }
};

