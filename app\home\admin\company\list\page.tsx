import { GetCompanies } from "@/actions/company/getCompany";
import { EmptyState } from "@/components/general/EmptyState";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { FilePlusIcon, PenBoxIcon, XCircleIcon } from "lucide-react";
import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { CopyLinkMenuItem } from "@/components/general/CopyLink";
import AdminAddCompanyPage from "../add/page";
import { MainPagination } from "@/components/general/MainPagination";

export default async function AdminCompanyPage({
  searchParams,
}: {
    searchParams: Promise<{ page?: string }>;
}) {
  const currentPage = Number((await searchParams)?.page) || 1;
  const pageSize = 30;
  const data = await GetCompanies();

  // Pagination logic
  const totalPages = Math.ceil((data?.length || 0) / pageSize);
  const paginatedData = data?.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <div className="flex flex-col w-full justify-start items-start">
      {data?.length === 0 ? (
        <EmptyState
          title="No job posts found"
          description="You don't have job posts yet."
          buttonText="Post a job"
          href="/job"
        />
      ) : (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>
              <div className="flex items-center w-full justify-between gap-2">
                <h1 className="text-2xl font-semibold">Companies</h1>
                <AdminAddCompanyPage />
              </div>
            </CardTitle>
            <CardDescription>Manage your companies here.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Jobs</TableHead>
                    <TableHead>About</TableHead>
                    <TableHead>Website</TableHead>
                    <TableHead>SignedUp</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData?.map((comp) => (
                    <TableRow key={comp.id}>
                      <TableCell className="flex items-center whitespace-nowrap gap-4">
                            <Image
                                src={comp.logo || ""}
                                alt={comp.name || ""}
                                width={32}
                                height={32}
                            /><span>{comp.name || ""}</span>
                        </TableCell>
                      <TableCell>{comp.location}</TableCell>                      
                      <TableCell>
                        <div className="flex flex-col text-xs">
                          <span>{comp.plan?.plan.planName}</span>
                          <span>{comp.plan?.paymentFrequency}</span>
                          <span>{comp.plan?.createdAt.toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>{comp._count.jobPosts}</TableCell>
                      <TableCell>
                        <div className="max-w-[350px] whitespace-normal line-clamp-2 leading-tight overflow-hidden">
                          {comp.about}
                        </div>
                      </TableCell>
                      <TableCell>{comp.website}</TableCell>
                      <TableCell>
                        {comp.createdAt.toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="cursor-pointer">
                              <MoreHorizontal />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-30">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              asChild
                              className="cursor-pointer"
                            >
                              <Link
                                href={`/home/<USER>/company/job/add?id=${comp.id}`}
                              >
                                <FilePlusIcon className="mr-2" />
                                Add Job
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              asChild
                              className="cursor-pointer"
                            >
                              <Link href={`/home/<USER>/company/${comp.id}/edit`}>
                                <PenBoxIcon className="mr-2" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              asChild
                              className="cursor-pointer"
                            >
                              <CopyLinkMenuItem
                                jobUrl={`${process.env.NEXT_PUBLIC_APP_URL}/job/${comp.id}`}
                              />
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              asChild
                              className="cursor-pointer"
                            >
                              <Link href={`/home/<USER>/company/${comp.id}/delete`}>
                                <XCircleIcon className="mr-2" />
                                Delete
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="flex justify-center mt-4">
                <MainPagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
