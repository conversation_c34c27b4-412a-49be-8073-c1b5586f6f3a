import {
  GetOriginalResume,
  GetResume,
} from "@/actions/resume/userResumeActions";
import { ResumeEvaluationCompany } from "@/components/user/resume/ResumeEvaluationCompany";
import { EmptyState } from "@/components/general/EmptyState";
import { DocumentViewerWord } from "@/components/general/DocumentViewerWord";
import { DocumentViewerPdf } from "@/components/general/DocumentViewerPdf";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export default async function CandidateResumePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const resume = await GetResume(id as string);

//   if (!resume?.resumeData) {
//     return (
//       <div className="grid grid-cols-1 mt-5 gap-4">
//         <EmptyState
//           title="Resume Not Found"
//           description="This resume could not be found."
//           buttonText="Back to Resume Bank"
//           href="/home/<USER>/resume/list"
//         />
//       </div>
//     );
//   }
  const profilePicture = resume.picture as string;
  const origResume = await GetOriginalResume(resume?.fileId as string);  

  return (
    <>
      <div className="max-w-4xl mx-auto w-full md:rounded-lg border-[0.5px] border-neutral-300 bg-white">
        <ResumeEvaluationCompany userId="" resumeId={resume.id as string} />
      </div>
      <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between px-4 bg-white">
        <h1 className="text-2xl font-bold pt-10">Résumé</h1>
        {origResume?.fileType === "PDF" ? (
            <DocumentViewerPdf 
                documentUrl={origResume?.url ? origResume.url : ""} 
                documentType={"application/pdf"} 
                title="User Resume" 
            />

        ):(
            <DocumentViewerWord 
                documentUrl={origResume?.url ? origResume.url : ""} 
                title="User Resume" 
            />
        )}
      </div>
    </>
  );
}
