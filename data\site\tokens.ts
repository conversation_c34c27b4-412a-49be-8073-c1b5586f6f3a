"use server";

import crypto from "crypto";
import { v4 as uuidv4 } from "uuid";
import { prisma } from "@/lib/prisma/prismaClient";
import { GetVerificationTokenByEmail } from "@/data/site/verificationToken";
import { GetPasswordResetTokenByEmail } from "@/data/site/passwordResetToken";
import { GetTwoFactorTokenByEmail } from "@/data/site/twoFactorToken";
import { hashEmail } from "@/utils/hashingHelpers";

export const GenerateVerificationToken = async (email: string) => {
  const token = uuidv4();
  const emailHash = await hashEmail(email);

  const expires = new Date(new Date().getTime() + 3600 * 1000); //1hr
  const existingToken = await GetVerificationTokenByEmail(emailHash);

  if (existingToken) {
    await prisma.userVerificationToken.delete({
      where: { id: existingToken.id },
    });
  }

  //Update if email already exists, else create a new one
  const verificationToken = await prisma.userVerificationToken.upsert({
    where: { emailHash }, // Use emailHash to locate an existing token
    update: { token: token, expires: expires },
    create: {
      id: uuidv4(),
      emailHash: emailHash,
      token: token,
      expires: expires,
    },
  });

  return verificationToken;
};

export const GeneratePasswordResetToken = async (hashedEmail: string) => {
  const token = uuidv4();

  const expires = new Date(new Date().getTime() + 3600 * 1000); //1hr
  const existingToken = await GetPasswordResetTokenByEmail(hashedEmail);

  if (existingToken) {
    await prisma.userPasswordResetToken.delete({
      where: { id: existingToken.id },
    });
  }

  const passwordResetTokenToken = await prisma.userPasswordResetToken.upsert({
    where: {
      emailHash: hashedEmail,
    }, // Use emailHash to locate an existing token
    update: {
      token: token,
      expires: expires,
    },
    create: {
      id: uuidv4(),
      emailHash: hashedEmail,
      token: token,
      expires: expires,
    },
  });

  return passwordResetTokenToken;
};

export const GenerateTwoFactorToken = async (hashedEmail: string) => {
  const token = crypto.randomInt(100000, 999999).toString();
  const expires = new Date(new Date().getTime() + 15 * 60 * 1000); //15mins

  const existingToken = await GetTwoFactorTokenByEmail(hashedEmail);

  if (existingToken) {
    await prisma.userTwoFactorToken.delete({ where: { id: existingToken.id } });
  }

  const twoFactorToken = await prisma.userTwoFactorToken.upsert({
    where: {
      emailHash: hashedEmail,
    }, // Use emailHash to locate an existing token
    update: {
      token: token,
      expires: expires,
    },
    create: {
      id: uuidv4(),
      emailHash: hashedEmail,
      token: token,
      expires: expires,
    },
  });

  return twoFactorToken;
};
