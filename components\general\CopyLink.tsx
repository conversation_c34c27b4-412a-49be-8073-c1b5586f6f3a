"use client";

import { Link2 } from "lucide-react";
import { DropdownMenuItem } from "../ui/dropdown-menu";
import { toast } from "sonner";

export function CopyLinkMenuItem({ jobUrl }: { jobUrl: string }) {

    async function handleCopy() {
        try {
            await navigator.clipboard.writeText(jobUrl);
            toast.success("Job URL copied to clipboard");
        } catch (error) {
            console.error("Failed to copy: ", error);
            toast.error("Failed to copy job URL");
        }
    }

  return (
    <DropdownMenuItem onSelect={handleCopy} className="justify-start cursor-pointer">
      <Link2 className="size-4 mr-2" />
      <span>Copy URL</span>
    </DropdownMenuItem>
  );
}
