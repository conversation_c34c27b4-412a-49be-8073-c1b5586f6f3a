import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(req: Request) {
  try {
    const { countryName, cityName } = await req.json();
    
    // Path to cityList.ts file
    const filePath = path.join(process.cwd(), 'data', 'location', 'cityList.ts');
    
    // Read the current file content
    let content = await fs.readFile(filePath, 'utf-8');
    
    // Parse the existing cityList object
    const cityListMatch = content.match(/export const cityList: \{ \[key: string\]: City\[\] \} = ({[\s\S]*?});/);
    if (!cityListMatch) {
      return NextResponse.json({ error: 'Could not parse city list' }, { status: 400 });
    }
    
    const cityListObj = eval(`(${cityListMatch[1]})`);
    
    // Add the new city if it doesn't exist
    if (!cityListObj[countryName]) {
      cityListObj[countryName] = [];
    }
    
    const cityExists = cityListObj[countryName].some(
      (city: any) => city.name.toLowerCase() === cityName.toLowerCase()
    );
    
    if (!cityExists) {
      cityListObj[countryName].push({
        name: cityName,
        country: countryName
      });
      
      // Sort cities
      cityListObj[countryName].sort((a: any, b: any) => 
        a.name.localeCompare(b.name)
      );
      
      // Convert the object back to a formatted string with double quotes
      const newCityListString = JSON.stringify(cityListObj, null, 2)
        .replace(/'/g, '"')  // Ensure all quotes are double quotes
        .replace(/\n\s\s/g, '\n    '); // Fix indentation to match original format
      
      // Replace the old cityList object with the new one
      const newContent = content.replace(
        /export const cityList: \{ \[key: string\]: City\[\] \} = ({[\s\S]*?});/,
        `export const cityList: { [key: string]: City[] } = ${newCityListString};`
      );
      
      // Write back to file
      await fs.writeFile(filePath, newContent, 'utf-8');
      
      return NextResponse.json({ success: true, message: 'City added successfully' });
    } else {
      return NextResponse.json({ message: 'City already exists' }, { status: 409 });
    }
    
  } catch (error) {
    console.error('Error adding city:', error);
    return NextResponse.json({ error: 'Failed to add city' }, { status: 500 });
  }
}
