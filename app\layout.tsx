import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import { Toaster } from "@/components/ui/sonner";
import { cn } from "@/lib/utils";
import "./globals.css";
import NextTopLoader from "nextjs-toploader";
import { NextSSRPlugin } from "@uploadthing/react/next-ssr-plugin";
import { extractRouterConfig } from "uploadthing/server";
import { ourFileRouter } from "./api/uploadthing/core";
import { SessionProvider } from "next-auth/react";
import { auth } from "@/lib/auth/auth";
import { ReactQueryClientProvider } from '@/components/providers/ReactQueryClientProvider';
import { FloatingChatButton } from "@/components/general/FloatingChatButton";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Athena: AI powered talent management",
  description: "AI Powered Talent Management",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();

//   console.log({ROOT: session?.user?.id})

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, "antialiased min-h-screen")}>
        <SessionProvider session={session}>
            <ReactQueryClientProvider>
                <ThemeProvider
                    attribute="class"
                    defaultTheme="light"
                    enableSystem
                    disableTransitionOnChange
                >
                    <NextSSRPlugin routerConfig={extractRouterConfig(ourFileRouter)} />
                    {/* Changed container div */}
                    <div className="flex flex-col min-h-screen">
                    <main className="flex-1">
                        <NextTopLoader
                        color="#F36295"
                        initialPosition={0.08}
                        crawlSpeed={200}
                        height={6}
                        crawl={true}
                        showSpinner={true}
                        easing="ease"
                        speed={300}
                        shadow="0 0 10px #2299DD,0 0 5px #2299DD"
                        />
                        {children}
                    </main>
                    </div>
                    <Toaster closeButton richColors />
                </ThemeProvider>
                <FloatingChatButton />
            </ReactQueryClientProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
