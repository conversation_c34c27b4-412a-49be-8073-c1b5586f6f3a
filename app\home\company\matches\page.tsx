import { EmptyState } from "@/components/general/EmptyState";
import { auth } from "@/lib/auth/auth";
import { getJobResumeMatches } from "@/actions/job/getJob";
import { getCompanyJobList } from "@/actions/company/getCompany";
import { CompanyJobMatches } from "@/components/company/CompanyJobMatches";
import { ScoreFilterDropdownServer } from "@/components/company/ScoreFilterDropdownServer";

export default async function CompanyMatchesPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string; score?: string }>;
}) {
  // Create a plain JavaScript object copy of searchParams at the beginning
  // to avoid issues with Next.js dynamic API usage detection.
  const plainSearchParams = await searchParams;

  const session = await auth();
  const currentPage = Number(plainSearchParams?.page) || 1;
  const scoreQueryParam = plainSearchParams?.score; // Use the plain copy
  const pageSize = 50;

  const companyId = session?.user?.companyId as string;
  if (!companyId) {
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <h1 className="text-2xl font-semibold">Job Matches</h1>
        <EmptyState
          title="Error"
          description="Company information not found."
          buttonText="Go to Dashboard"
          href="/home/<USER>"
        />
      </div>
    );
  }

  let minScoreForFiltering: number | undefined;
  if (scoreQueryParam === "all") minScoreForFiltering = undefined;
  else if (scoreQueryParam) minScoreForFiltering = parseInt(scoreQueryParam);
  else minScoreForFiltering = 70; // Default to 70 for filtering jobs

  const allCompanyJobs = await getCompanyJobList(companyId);

  const jobsWithMatchesDetails = await Promise.all(
    allCompanyJobs.map(async (job) => {
      const matchesForJob = await getJobResumeMatches(job.id, "", minScoreForFiltering);
      return {
        ...job,
        matchesCountWithFilter: matchesForJob.length,
      };
    })
  );

  const jobsToShow = jobsWithMatchesDetails.filter(job => job.matchesCountWithFilter > 0);

  // Pagination logic
  const totalPages = Math.ceil(jobsToShow.length / pageSize);
  const paginatedJobs = jobsToShow.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <>
      {jobsToShow.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
          <h1 className="text-2xl font-semibold">Job Matches</h1>
          <div className="flex items-center justify-between gap-2 pb-0">
            <span className="text-sm text-muted-foreground">Jobs that matches resumes with a score set by you or the default 70 and above.</span>
            <ScoreFilterDropdownServer
                currentScoreFromUrl={scoreQueryParam}
                basePath="/home/<USER>/matches"
                allSearchParams={{ ...plainSearchParams, page: "1" }} // Use plainSearchParams
            />
          </div>
          <EmptyState
            title="No job matches found"
            description="No jobs have applicants matching the current score filter. Try adjusting the filter or check back later."
            buttonText="Go to Job List"
            href="/home/<USER>/job/list"
          />
        </div>
      ) : (
        <div className="space-y-6">
          <CompanyJobMatches 
            paginatedJobs={paginatedJobs}
            currentPage={currentPage}
            totalPages={totalPages}
            currentScore={scoreQueryParam}
            allSearchParams={plainSearchParams} // Pass the plain copy
          />      
        </div>
      )}
    </>
  );
}
