// app/api/user/cancel-subscription/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { useSession } from "next-auth/react";
import { hashEmail } from '@/utils/hashingHelpers';
import { prisma } from '@/lib/prisma/prismaClient';

export async function POST(req: NextRequest) {
  const { data: session } = useSession();
  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.user.findUnique({
    where: { emailHash: await hashEmail(session.user.email) },
    include: { subscriptions: { orderBy: { createdAt: 'desc' }, take: 1 } },
  })

  const subscription = user?.subscriptions[0]
  if (!subscription || subscription.status !== 'active') {
    return NextResponse.json({ error: 'No active subscription found' }, { status: 400 })
  }

  if (!process.env.PAYPAL_ACCESS_TOKEN) {
    return NextResponse.json({ error: 'PayPal access token not found' }, { status: 500 })
  }

  const paypalId = subscription.subscriptionId

  // Cancel via PayPal API
  const result = await fetch(`https://api-m.paypal.com/v1/billing/subscriptions/${paypalId}/cancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.PAYPAL_ACCESS_TOKEN}`, // You should generate this dynamically
    },
    body: JSON.stringify({ reason: 'User requested cancellation' }),
  })

  if (!result.ok) {
    const error = await result.text()
    return NextResponse.json({ error: 'PayPal cancel failed', details: error }, { status: 500 })
  }

  // Update local status
  await prisma.subscription.update({
    where: { id: subscription.id }, // Use the subscription's primary key 'id'
    data: { status: 'cancelled' },
  })

  return NextResponse.json({ success: true })
}
