"use server";

import { auth } from "@/lib/auth/auth";
import { z } from "zod";
import { JobSeekerSchema } from "@/data/zod/zodSchema";
import { prisma } from "@/lib/prisma/prismaClient";
import { v4 as uuidv4 } from "uuid";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { UserFile } from "@/types/customTypes";
import { SaveUserFile } from "../file/saveFile";
import { safeField } from "@/utils/stringHelpters";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function createJobSeeker(
  data: z.infer<typeof JobSeekerSchema>,
  savedFile: string
) {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const validateData = JobSeekerSchema.parse(data);

    if (validateData) {
      // Create a new jobSeeker record
      const jobSeeker = await prisma.jobSeeker.create({
        data: {
          id: uuidv4(),
          title: validateData.title ? safeField(validateData.title) : null,
          countryCode: validateData.title
            ? safeField(validateData.location)
            : null,
          about: validateData.title ? safeField(validateData.about) : null,
          portfolio: validateData.portfolio
            ? encryptToBuffer(validateData.portfolio)
            : null,
          linkedin: validateData.linkedin
            ? encryptToBuffer(validateData.linkedin)
            : null,
          github: validateData.github
            ? encryptToBuffer(validateData.github)
            : null,
          writing: validateData.writing
            ? encryptToBuffer(validateData.writing)
            : null,
          userId: session?.user?.id as string,
        },
      });

      if (jobSeeker) {
        const user = await prisma.user.update({
          where: {
            id: session?.user?.id as string,
          },
          data: {
            firstName: encryptToBuffer(validateData.firstname as string),
            lastName: encryptToBuffer(validateData.lastname as string),
            onboarded: true,
            userType: "JOB_SEEKER",
          },
        });

        if (savedFile) {
          const data = JSON.parse(savedFile);

          const resumeFile: UserFile = {
            id: "",
            userId: session?.user?.id as string,
            fileUse: "RESUME",
            fileType: data[0].name.split(".")[1].toUpperCase(),
            fileName: data[0].name,
            description: "User Resume",
            key: data[0].key,
            url: data[0].ufsUrl,
            fileSize: data[0].size,
          };

          const resultFileSave = await SaveUserFile(resumeFile);

          if (resultFileSave) {
            if (resultFileSave.success) {
              //Update jobSeeker with resumeFileId
              await prisma.jobSeeker.update({
                where: {
                  id: jobSeeker.id,
                },
                data: {
                  resumeFileId: resultFileSave.fileResult?.id as string,
                },
              });
            }
          }
        }

        return {
          success:
            "Success! Your information was saved. You can now start applying for jobs.",
        };
      } else {
        return {
          error: "Error! Failed to save file.",
        };
      }
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw error;
    }
    return {
      error: `Error was encountered, please try again. ${error}`,
    };
  }
}
