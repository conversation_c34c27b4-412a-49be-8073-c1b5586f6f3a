"use client";

import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Table, TableBody, TableCell, TableRow } from "../ui/table";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox"; 
import { ChevronLeftCircle, ChevronRightCircle, EyeIcon, PenBoxIcon } from "lucide-react";
import Image from "next/image";
import { Pagination } from "../ui/pagination";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { normalizeHeaders } from "@/utils/stringHelpters";
import { JobResumeNotesDialog } from "./JobResumeNotesDialog";

interface JobMatchesInlineProps {
  jobId: string;
  jobTitle: string;
  jobMatches: any[];
  isOddRow?: boolean;
}

export function JobMatchesInline({
  jobId,
  jobTitle,
  jobMatches,
  isOddRow = false,
}: JobMatchesInlineProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  if (!jobMatches || jobMatches.length === 0) {
    return (
      <div
        className={`text-center py-4 text-muted-foreground ${isOddRow ? "bg-muted/30" : ""}`}
      >
        No job matches found yet. Upload resumes.
      </div>
    );
  }

  // Pagination logic
  const totalPages = Math.ceil(jobMatches.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMatches = jobMatches.slice(startIndex, endIndex);

  return (
    <>
      <Table className="w-full table-fixed">
        <TableBody>
          {currentMatches.map((match) => (
            <TableRow
              key={match.id}
              className={`border-b-0 ${isOddRow ? "bg-muted/30" : ""}`}
            >
              <TableCell style={{ width: "34.78%" }} className="py-0">
                <div className="flex items-center gap-2">
                  <Image
                    src={
                      (match.resume?.picture && String(match.resume.picture).trim()) ||
                      (match.resume?.user?.image && String(match.resume.user.image).trim()) ||
                      "/icons/profile.png"
                    }
                    alt={`${match.resume?.name || ""}`}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="text-sm block overflow-hidden text-ellipsis whitespace-nowrap min-w-0 flex-1">
                    {match.resume?.name ? normalizeHeaders(match.resume?.name) : `${match.resume?.user?.firstName} ${match.resume?.lastName}` || ""}
                  </span>
                </div>
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <MatchScoreCircle
                  score={match?.overall_score}
                />
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <ResumeMatchDialog
                  resumeMatch={match}
                  applicantName={match.resume?.name ? match.resume?.name : `${match.resume?.user?.firstName} ${match.resume?.lastName}` || ""}
                />
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <a
                  href={`/home/<USER>/resumedoc/${match.resume?.id}`}
                  target="_blank"
                  className="text-primary hover:underline cursor-pointer inline-flex justify-center pl-4"
                  title="View Resume"
                >
                  <Button
                    variant="ghost"
                    className="text-primary hover:underline cursor-pointer"
                    title="View Resume"
                  >
                    <EyeIcon className="w-5 h-5" />
                  </Button>
                </a>
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <div className="flex justify-center">
                  <ShortlistCheckbox
                    jobId={jobId}
                    resumeId={match?.resumeId || ""}
                    initialShortlisted={match?.resume?.isShortlisted || false}
                  />
                </div>
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <div className="flex justify-center">
                    <JobResumeNotesDialog
                        jobId={match?.jobId || ""}
                        resumeId={match?.resumeId || ""}
                        jobTitle={jobTitle}
                        resumeName={match.resume?.name ? match.resume?.name : `${match.resume?.user?.firstName} ${match.resume?.lastName}` || ""}
                    />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {totalPages > 1 && (
        <div className="flex w-full justify-center">
          <Pagination className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              title="Previous Page"
              className="cursor-pointer"
            >
              <ChevronLeftCircle className="h-5 w-5" />
            </Button>
            <span className="mx-4 text-xs">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              title="Next Page"
              className="cursor-pointer"
            >
              <ChevronRightCircle className="h-5 w-5" />
            </Button>
          </Pagination>
        </div>
      )}
    </>
  );
}
