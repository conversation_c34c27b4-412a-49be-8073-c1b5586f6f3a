"use server";

import * as z from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { RegisterSchema } from "@/data/zod/zodSchema";
import { GetUserByEmail } from "@/data/user/user";
import { GenerateVerificationToken } from "@/data/site/tokens";
import { v4 as uuidv4 } from "uuid";
import { UserRole } from "@prisma/client";
import { hashEmail, hashString } from "@/utils/hashingHelpers";
import { SendEmail } from "@/lib/sendEmail";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const signup = async (values: z.infer<typeof RegisterSchema>) => {
  
    // Arcjet protection
    await ensureServerActionProtection();

  const validatedFields = RegisterSchema.safeParse(values);
  const newId = uuidv4();

  if (!validatedFields.success) {
    return { error: "Invalid fields." };
  }

  const { email, password, firstname, lastname } = validatedFields.data;

    try {
        const existingUser = await GetUserByEmail(email);

        if (existingUser) {
            return { error: "Email already in use!" };
        }

        const newUser = await prisma.user.create({
            data: {
            id: newId,
            firstName: encryptToBuffer(firstname),
            lastName: encryptToBuffer(lastname),
            email: encryptToBuffer(email),
            emailHash: await hashEmail(email),
            password: await hashString(password),
            role: UserRole.USER,
            name: encryptToBuffer(firstname + " " + lastname),
            onboarded: false,
            },
        });

        if (newUser) {
            const userEmail = safeDecrypt(newUser.email);

            const verificationToken = await GenerateVerificationToken(userEmail!);

            if (verificationToken) {
            const name = firstname + " " + lastname;
            const subject = "Your verification token";
            const html = `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                            <div>
                            <h2>Welcome, ${name}!</h2>
                            <p>Please verify your email by clicking the link below:</p>
                        </div>
                        <div>
                            <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/verification?token=${verificationToken.token}" 
                            style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                                Verify Email
                            </a>
                        </div>
                        <div>
                            <p>If you did not sign up for an account, please ignore this email. The token will expire and your information will be deleted.</p>
                        </div>
                        <div>
                            <p>Regards, <br /> Edison AIX Team</p>
                        </div>
                        </div>
                    `;

            await SendEmail({
                sendTo: userEmail!,
                subject: subject,
                html: html,
            });

            return {
                success:
                "Success! A confirmation email was sent. Redirecting to sign in page.",
            };
            }
        } else {
            return { error: "Error! An error occured while registering." };
        }
    } catch (error) {
      return { error: `An error occured while registering. ${error}`};
    }
};
