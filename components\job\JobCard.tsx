"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import Image from "next/image";
import { Badge } from "../ui/badge";
import { formatCurrency } from "@/utils/formatCurrency";
import { CircleCheckIcon, CircleXIcon, MapPin } from "lucide-react";
import { X } from "lucide-react";
import { formatRelativeTime } from "@/utils/formatRelativeTime";
import { snakeCaseToTitleCase } from "@/utils/stringHelpters";
import {
  Dialog,
  DialogTrigger,
  DialogPortal,
  DialogOverlay,
  DialogClose,
} from "@/components/ui/dialog";
import { JobDetails } from "./JobDetails";
import { useState } from "react";
import { Button } from "@/components/ui/button";


interface JobCardProps {
  job: {
    id: string;
    jobTitle: string;
    salaryFrom: number | null;
    salaryTo: number | null;
    salaryCurrency: string | null;
    employmentType: string;
    experienceLevel: string | null;
    country: string | null;
    location: string | null;
    listingDuration: number | null;
    interviewType: string | null;
    localRemoteWork: boolean;
    overseasRemoteWork: boolean;
    createdAt: Date;
    company: {
      name: string;
      about: string | null;
      logo: string | null;
      location: string;
      website: string | null;
      xAccount: string | null;
      linkedIn: string | null;
      tin: string | null;
    };
  };
}

export function JobCard({ job }: JobCardProps) {
  const [open, setOpen] = useState(false);

  // Calculate if the job is new (posted within the last 7 days)
  const createdAtDate = new Date(job.createdAt);
  const now = new Date();
  const oneWeekInMilliseconds = 7 * 24 * 60 * 60 * 1000;
  const isNew = (now.getTime() - createdAtDate.getTime()) <= oneWeekInMilliseconds;

  return (
    <Dialog open={open} onOpenChange={setOpen} modal={true}>
      <DialogTrigger asChild>
        {/* Wrapper div to establish the 'group' for hover effects */}
        <div className="group cursor-pointer h-full">
          <Card className="group-hover:shadow-lg transition-all duration-300 group-hover:border-2 group-hover:border-[#5ce1e6] dark:group-hover:border-gray-300 h-full flex flex-col">
            <CardHeader className="flex flex-col h-[70px] border-b">
                <div className="flex flex-col md:flex-row gap-2"> 
                    <Image
                    src={job.company.logo || "/images/company-logo-placeholder.svg"}
                    alt={job.company.name}
                    width={48}
                    height={48}
                    className="size-12 rounded-lg object-cover flex-shrink-0"
                    />
                    {/* This div ensures title and company stack naturally next to the logo */}
                    {/* Added relative positioning to this div for the "NEW" badge */}
                    <div className="flex-1 relative"> 
                        {isNew && (
                            <span
                                className="absolute top-0 right-0 bg-red-600 text-white text-[9px] font-bold px-1.5 py-0.5 rounded-full leading-none"
                                title="New job posting"
                            >
                                NEW
                            </span>
                        )}
                        {/* Added padding-right to h1 to make space for the badge if title is long */}
                        <h1 className="text-md font-bold line-clamp-2 pr-10">{job.jobTitle}</h1>
                        {/* Added a small margin-top for better spacing if title is short */}
                        <p className="text-sm text-muted-foreground mt-0.5">{job.company.name}</p>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="flex flex-col">
                {/* Block 1: Details (Employment Type, Experience, Country*/}
                <div className="grid grid-cols-3 items-center w-full text-sm pb-2">      
                    <div className="flex flex-col items-start text-sm">  
                        {/* Employment Type can remain as is or be styled similarly if desired */}
                        <Badge variant="outline" className="rounded-md px-2 py-0.5 text-xs">
                            {snakeCaseToTitleCase(job?.employmentType)}
                        </Badge>
                    </div>
                    <div className="flex flex-col items-center text-sm">
                        <span className="text-xs text-muted-foreground/80">Experience</span>
                        {job.experienceLevel ? (
                            <p className="text-muted-foreground text-xs text-right">{job.experienceLevel}</p>
                        ) : (
                            <p className="text-muted-foreground text-xs text-right">-</p>
                        )}
                    </div>
                    <div className="w-full text-xs flex flex-col items-end">
                        <div className="flex flex-col items-center text-sm">
                            <span className="text-xs text-muted-foreground/80">Interview</span>
                            {job.interviewType ? (
                                <p className="text-muted-foreground text-xs text-right">{snakeCaseToTitleCase(job.interviewType)}</p>
                            ) : (
                                <p className="text-muted-foreground text-xs text-right">-</p>
                            )}
                        </div>
                    </div>
                </div>
    
                {/* Location Info Block */}
                <div className="text-xs pb-2 w-full border rounded-lg overflow-clip p-2">
                    <div className="flex items-start">
                        <MapPin className="size-3.5 mr-1.5 flex-shrink-0 mt-[1px]" />
                        <div className="flex-grow min-w-0"> 
                            <Badge variant="default" className="rounded-md px-2 py-0.2 text-xs mr-1 whitespace-nowrap align-baseline">
                                {job.country}
                            </Badge>
                            {job.location && job.location.toLowerCase() !== 'worldwide' && (
                                <span className="text-muted-foreground align-baseline">{job.location}</span>
                            )}
                        </div>
                    </div>
                </div>
                
                
                    <div className="flex flex-col w-full text-xs  pt-2 pb-2">                        
                        <div className="grid grid-cols-2 grid-rows-1 gap-4">
                            <div className="w-full text-xs">
                                <span className="text-xs text-muted-foreground/80">Salary Range</span>
                                {job.salaryFrom !== null && job.salaryFrom > 0 ? (
                                    <p className="text-muted-foreground text-xs">
                                        {formatCurrency(job.salaryFrom, job.salaryCurrency ?? "USD")}
                                        {job.salaryTo && job.salaryTo > 0 && job.salaryTo > job.salaryFrom ? ` - ${formatCurrency(job.salaryTo, job.salaryCurrency ?? "USD")}` : ''}
                                    </p>
                                ) : (
                                    <p className="text-muted-foreground text-xs">-</p>
                                )}   
                            </div>

                            <div className="w-full text-xs flex flex-col items-end">
                                <div className="flex flex-col">
                                    <span className="text-xs text-muted-foreground/80">Remote Work</span>
                                    <div className="flex justify-start gap-x-2 mt-0.5">
                                        <span className="text-xs text-muted-foreground/80 flex items-center">
                                            Local:
                                            {job.localRemoteWork ? (
                                                <CircleCheckIcon className="size-3.5 ml-1 text-green-500" />
                                            ) : (
                                                <CircleXIcon className="size-3.5 ml-1 text-red-500" />
                                            )}
                                        </span>
                                        <span className="text-xs text-muted-foreground/80 flex items-center">
                                            Global:
                                            {job.overseasRemoteWork ? (
                                                <CircleCheckIcon className="size-3.5 ml-1 text-green-500" />
                                            ) : (
                                                <CircleXIcon className="size-3.5 ml-1 text-red-500" />
                                            )}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                {/* Block 4: Company About (if exists) */}
                <div className="flex flex-col w-full text-sm pt-2">
                    {job.company.about && (
                        <p className="text-xs text-muted-foreground line-clamp-3">
                            {job.company.about}
                        </p>
                    )}
                </div>
            </CardContent> 
            {/* CardFooter will be pushed to the bottom of the Card */}
            <CardFooter className="mt-auto border-t h-[15px]">
                <div className="flex flex-row w-full items-center justify-between">
                    <p className="text-xs text-muted-foreground">
                    {formatRelativeTime(job.createdAt)}
                    </p>
                    <span className="text-xs text-muted-foreground">Apply before: {new Date(new Date(job.createdAt).getTime() + (job.listingDuration || 30) * 24 * 60 * 60 * 1000).toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "numeric" })}</span>
                </div> 
            </CardFooter>
          </Card>
        </div>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 dark:bg-black/80 z-[9999]" /> {/* Increased z-index */}
        <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4"> {/* Increased z-index, removed overflow-auto */}
          {/* Content Box - Styled to match previous DialogContent and allow JobDetails to manage scrolling */}
          <div
            className="relative bg-background border shadow-lg rounded-lg 
                       max-w-4xl w-[90vw] lg:w-[70vw] max-h-[90vh] 
                       p-0 flex flex-col overflow-y-auto
                       data-[state=open]:animate-in data-[state=closed]:animate-out 
                       data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 
                       data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
          >
            {open && <JobDetails jobId={job.id} />}
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-3 top-3 h-7 w-7 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-20 cursor-pointer"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </DialogClose>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
