"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { PublishStatus } from "@prisma/client";
import { safeParseDecryptedResume } from "@/utils/parseResumeSchema";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { Resume } from "@/lib/resume/resumeActions";
import { el } from "date-fns/locale";
import { CandidateResume } from "@/types/customTypes";

export const getResumeData = async (id: string) => {
  const data = await prisma.userResume.findUnique({
    where: {
      id: id,
    },
    include: {
      user: true, 
    }
  });

  if (data) {
    
    // console.log({uparsedjobSeekerResume: data});

    const jobSeekerResume = {
        ...data,
        picture: data.picture ? safeDecrypt(data.picture) : null,
        fileContent: data.fileContent ? safeDecrypt(data.fileContent) : null,
        resumeData: safeParseDecryptedResume(
            data.resumeData
              ? Buffer.from(data.resumeData)
              : null
          ),
    };
    // console.log({jobSeekerResume: jobSeekerResume});
    
    return jobSeekerResume;
  }

  return data;
};

export const getUserResumeData = async (userId: string) => {


    try {
        const data = await prisma.userResume.findFirst({
            where: {
            userId: userId,
            },
            include: {
                user: true, 
            }
        });

        if (data) {

            console.log({jobSeekerResumeORIG: data});

            // Construct an object that strictly matches the ResumeSchema structure
            const resumeObjectForSchema = {
                id: data.id,
                fileId: data.fileId,
                picture: data.picture ? safeDecrypt(data.picture) : null, // Decrypt picture here
                status: data.status,
                fileContent: data.fileContent ? safeDecrypt(data.fileContent) : null, // Decrypt fileContent here
                resumeData: safeParseDecryptedResume(data.resumeData ? Buffer.from(data.resumeData) : null), // This is already transformed and parsed
            };

            console.log({jobSeekerResume: resumeObjectForSchema});

            return resumeObjectForSchema;
        } else {
            return null;
        }
    } catch (error) {
            console.error("Error fetching resume data:", error);
            return null;        
    }
};

export const getUserResumeIdData = async (userId: string) => {
  const data = await prisma.userResume.findFirst({
    where: {
      userId: userId,
    },
    select: {
        id: true
    }
  });

  return data;
};

export const getUserResumeUsernameData = async (userId: string) => {
  const data = await prisma.userResume.findFirst({
    where: {
      userId: userId,
    },
    select: {
      username: true,
    },
  });

   if (data) {
    return data;
  }

  return null;
};

export const getOriginalResumeData = async (fileId: string) => {
    const data = await prisma.userFile.findFirst({
        where: {
          id: fileId,
          fileUse: "RESUME"
        },
        orderBy: {
          createdAt: "desc"
        },
        select: {
          fileName: true,
          url: true,
          fileType: true,
        },
        take: 1,
      });

  if (data) {

    const userResume = {
        ...data,
        url: data.url ? safeDecrypt(data.url) : null
    };

    // console.log({userResume: userResume});

    return userResume;
  }

  return null;
};

export const getUserOriginalResumeData = async (userId: string) => {
    const data = await prisma.userFile.findFirst({
        where: {
          userId: userId,
          fileUse: "RESUME"
        },
        orderBy: {
          createdAt: "desc"
        },
        select: {
          fileName: true,
          url: true,
        },
        take: 1,
      });

  if (data) {

    const userResume = {
        ...data,
        url: data.url ? safeDecrypt(data.url) : null
    };

    return userResume;
  }

  return null;
};

export const updateResumeStatusData = async ( resumeId: string, status: PublishStatus ) => {
  
  try {
    const data = await prisma.userResume.update({
        where: {
          id: resumeId,
        },
        data: {
          status: status,
        },
      });

      return data;

  } catch (error) {
    return error;
  }
};

export const updateResumeUsernameData = async ( resumeId: string, username: string ) => {
    try {
        const data = await prisma.userResume.update({
            where: {
              id: resumeId,
            },
            data: {
              username: username,
            },
          });
    
          if(data){

            return data;
          } else {
            return null; 
          }
    } catch (error) {
      return error;
    }
  };

  export const checkResumeUsernameData = async ( username: string ) => {
  
    try {

        const available = await prisma.userResume.findFirst({
            where: {
              username: username
            }
        });

        if(available){
            return false;
        } else {
            return true; //username is not found and available for use
        }
    } catch (error) {
      return error;
    }
  };

export const getUserResumeEvaluationData = async (userId?: string, resumeId?: string) => {
  try {
    const data = await prisma.userResumeEvaluation.findFirst({
      where: {
        ...(resumeId ? { resumeId: resumeId } : {}),
        ...(userId ? { userId: userId } : {})
      },
      select: {
        evaluationData: true,
      }
    });
    
    if (data && data.evaluationData) {
      // Parse the JSON string from the evaluationData column
      try {
        return JSON.parse(data.evaluationData);
      } catch (parseError) {
        console.error("Error parsing evaluation data JSON:", parseError);
        return null;
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error fetching resume evaluation data:", error);
    return null;
  }
}

export const getUserRawResumeData = async (resumeIds: string[]) => {
    const data = await prisma.userResume.findMany({
      where: {
        id: { notIn: resumeIds },
      },
      select: {
        id: true,
        userId: true,
        fileContent: true,
        resumeData: true,
      },
      take: 5,
    });
  
    if (data && data.length > 0) {
        const userResumes = data.map(resume => ({
            id: resume.id,
            userId: resume.userId,
            fileContent: resume.fileContent ? safeDecrypt(resume.fileContent) : null,
            resumeData: safeParseDecryptedResume(
                resume.resumeData
                  ? Buffer.from(resume.resumeData)
                  : null
              )
        }));
    
        return userResumes;
    }
  
    return null;
};

export const getRawResumesByIds = async (resumeIds: string[]) => {
  if (!resumeIds || resumeIds.length === 0) {
    return []; // Return empty array if no IDs are provided
  }
  const data = await prisma.userResume.findMany({
    where: {
      id: { in: resumeIds }, // Use IN operator
    },
    select: {
      id: true,
      userId: true,
      fileContent: true, 
      resumeData: true, 
    },
    // No 'take' limit here, fetch all specified IDs
  });

  if (data && data.length > 0) {
      const userResumes = data.map(resume => ({
          id: resume.id,
          userId: resume.userId,
          fileContent: resume.fileContent ? safeDecrypt(resume.fileContent) : null,
          resumeData: safeParseDecryptedResume(
              resume.resumeData
                ? Buffer.from(resume.resumeData)
                : null
            )
      }));
      return userResumes;
  }
  return []; // Return empty array if no data found
};

export const toggleShortlistResumeData = async (jobId: string, resumeId: string, isShortlisted: boolean) => { 
    try {
        // Check if the record exists
        const existingShortlist = await prisma.jobShortlist.findFirst({
            where: {
                jobId: jobId,
                resumeId: resumeId
            }
        });
        
        // If shortlisted is true and record doesn't exist, create it
        if (isShortlisted && !existingShortlist) {
            const data = await prisma.jobShortlist.create({
                data: {
                    jobId: jobId,
                    resumeId: resumeId,
                }
            });
            return data;
        } 
        // If shortlisted is false and record exists, delete it
        else if (!isShortlisted && existingShortlist) {
            await prisma.jobShortlist.delete({
                where: {
                    id: existingShortlist.id
                }
            });
            return null;
        }
        
        // If no changes needed (already in desired state), return existing state
        return existingShortlist;
    } catch (error) {
        console.error("Error toggling shortlist:", error);
        return error;
    }
}

export const updateUserResumePictureData = async (resumeId: string, url: string) => {
  const data = await prisma.userResume.update({
    where: {
      id: resumeId,
    },
    data: {
      picture: encryptToBuffer(url)
    },
  });

  if (data) {
    return data;
  }

  return null;
};

export const getUserResumeFileData = async (processedResumeFileIds: string[]) => {
    const data = await prisma.userFile.findMany({
      where: {
        id: { notIn: processedResumeFileIds },
        fileUse: "RESUME",
        parseCount: { lt: 3 }
      },
      take: 5,
    });
  
    if (data && data.length > 0) {
        const userFiles = data.map(file => ({
            ...file,
            url: file.url ? safeDecrypt(file.url) : null
        }));
    
        return userFiles;
    }
  
    return null;
};

export const deleteResumeData = async (id: string) => {
    try {
        // First, get the fileId before deleting the resume
        const resumeData = await prisma.userResume.findUnique({
            where: {
                id: id,
            },
            select: {
                fileId: true
            }
        });
        
        if (!resumeData || !resumeData.fileId) {
            return {
                error: "Resume not found or has no associated file.",
                resume: null,
            };
        }
        
        // Use a transaction to ensure both operations succeed or fail together
        const result = await prisma.$transaction(async (tx) => {
            // Delete the resume first
            const deletedResume = await tx.userResume.delete({
                where: {
                    id: id,
                },
            });
            
            // Then delete the associated file
            if (deletedResume.fileId) {
                await tx.userFile.delete({
                    where: {
                        id: deletedResume.fileId,
                    },
                });
            }
            
            
            return deletedResume;
        });
        
        return {
            success: "Success! Resume and associated file deleted.",
            resume: result,
        };
    } catch (error) {
        console.error("Error deleting resume:", error);
        return {
            error: `Error! Failed to delete resume: ${error}`,
            resume: null,
        };
    }
};

export const GetAllResumeRawTextData = async () => {
  try {
    // Fetch all resumes
    const resumes = await prisma.userResume.findMany({
      select: {
        id: true,
        userId: true,
        fileContent: true
      }
    });

    // Decrypt the file content for each resume
    if (resumes && resumes.length > 0) {
        const resumesWithDecryptedContent = resumes.map(resume => ({
        id: resume.id,
        userId: resume.userId,
        raw_text: safeDecrypt(resume.fileContent) || ""
        })).filter(resume => resume.raw_text); // Filter out resumes with no content

        return resumesWithDecryptedContent;
    }
    return null;
  } catch (error) {
    console.error("Error fetching resume raw text:", error);
    return null;
  }
};

export const searchResumeData = async (searchTerm: string, page: number = 1, pageSize: number = 10) => {
  try {
    if (!searchTerm.trim()) {
      return { resumes: [], totalPages: 0, error: "Search term is empty." };
    }

    const normalizedSearchTerm = searchTerm.toLowerCase();

    // WARNING: Fetching all resumes can be very inefficient for large datasets.
    // Consider alternative strategies like dedicated search fields or a search engine for better performance.
    const allResumesRaw = await prisma.userResume.findMany({
      select: {
        id: true,
        name: true,         // Encrypted
        picture: true,      // UserResume.picture is not directly used by ResumeSearchResult, Resume object has its own
        fileContent: true,  // Encrypted
        resumeData: true,   // Encrypted (Buffer)
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc", 
      },
    });

    const decryptedAndFilteredResumes = allResumesRaw
      .map(resume => {
        const decryptedName = resume.name ? safeDecrypt(resume.name) : "";
        const decryptedPicture = resume.picture ? safeDecrypt(resume.picture) : "";
        const decryptedFileContent = resume.fileContent ? safeDecrypt(resume.fileContent) : "";
        
        const parsedResumeData = safeParseDecryptedResume(
          resume.resumeData ? Buffer.from(resume.resumeData) : null
        );
        
        // Convert parsedResumeData (object) to a searchable string.
        const resumeDataString = parsedResumeData ? JSON.stringify(parsedResumeData).toLowerCase() : "";

        const matches = decryptedName?.toLowerCase().includes(normalizedSearchTerm) ||
                        decryptedFileContent?.toLowerCase().includes(normalizedSearchTerm) ||
                        resumeDataString.includes(normalizedSearchTerm);

        if (matches) {
          return {
            id: resume.id,
            name: decryptedName, // Decrypted name
            picture: decryptedPicture, // Decrypted picture
            // The `Resume` object (parsedResumeData) will contain its own decrypted picture.
            // The top-level UserResume.picture is not directly needed for ResumeSearchResult.
            resumeData: parsedResumeData, // Decrypted and parsed Resume object
            createdAt: resume.createdAt,
          };
        }
        return null;
      })
      .filter(Boolean) as { id: string; name: string; picture: string | null; resumeData: any; createdAt: Date }[]; // Corrected Type assertion after filter

    const totalCount = decryptedAndFilteredResumes.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const skip = (page - 1) * pageSize;

    const paginatedResumes = decryptedAndFilteredResumes.slice(skip, skip + pageSize);
    
    //   console.log({paginatedResumes: JSON.stringify(paginatedResumes, null, 2)});

    return { resumes: paginatedResumes, totalPages };

  } catch (error) {
    console.error("Error in searchResumeData:", error);
    return { resumes: [], totalPages: 0, error: "Failed to retrieve search results." };
  }
};

export const updateResumeData = async (resume: Resume) => {
    try {
        const data = await prisma.userResume.update({
            where: {
              id: resume.id,
            },
            data: {
                fileId: resume.fileId,
                name: encryptToBuffer(resume.resumeData?.standardFields?.header?.name as string),
                about: encryptToBuffer(resume.resumeData?.standardFields?.header?.shortAbout as string),
                email: encryptToBuffer(resume.resumeData?.standardFields?.header?.email as string),
                phone: encryptToBuffer(JSON.stringify(resume.resumeData?.standardFields?.header?.phone || [])),
                website: encryptToBuffer(resume.resumeData?.standardFields?.header?.website as string),
                xaccount: encryptToBuffer(resume.resumeData?.standardFields?.header?.xaccount as string),
                linkedin: encryptToBuffer(resume.resumeData?.standardFields?.header?.linkedin as string),
                github: encryptToBuffer(resume.resumeData?.standardFields?.header?.github as string),
                address: encryptToBuffer(resume.resumeData?.standardFields?.header?.address as string),
                fileContent: (() => {
                  const allTexts: string[] = [];
                  if (resume.resumeData) {
                    extractTextFromObject(resume.resumeData.standardFields, allTexts);
                    extractTextFromObject(resume.resumeData.additionalSections, allTexts);
                  }
                  return encryptToBuffer(allTexts.join(" "));
                })(),
                resumeData: resume.resumeData ? encryptToBuffer(JSON.stringify(resume.resumeData)) : null,
            },
          });
    
          if(data){

            return data;
          } else {
            return null; 
          }
    } catch (error) {
      return error;
    }
  };

  function extractTextFromObject(obj: any, texts: string[] = []): string[] {
  if (obj === null || obj === undefined) {
    return texts;
  }

  if (typeof obj === 'string' && obj.trim() !== '') {
    texts.push(obj.trim());
  } else if (Array.isArray(obj)) {
    obj.forEach(item => extractTextFromObject(item, texts));
  } else if (typeof obj === 'object') {
    Object.values(obj).forEach(value => extractTextFromObject(value, texts));
  }
  return texts;
}

export const createResumeData = async (resume: CandidateResume) => {
    try {
        const data = await prisma.userResume.create({
            data: {
                id: resume.id,
                userId: resume.userId,
                companyId: resume.companyId,
                fileId: resume.fileId,
                name: resume.name ? encryptToBuffer(resume.name as string) : null,
                email: resume.email ? encryptToBuffer(resume.email as string) : null,
                phone: resume.phone ? encryptToBuffer(resume.phone as string) : null,
                fileContent: resume.fileContent ? encryptToBuffer(resume.fileContent as string) : null,
                promptMarkdown: resume.promptMarkdown ? encryptToBuffer(resume.promptMarkdown as string) : null,
                structured: resume.structured ? encryptToBuffer(JSON.stringify(resume.structured)) : null,
                metadata: resume.metadata ? encryptToBuffer(JSON.stringify(resume.metadata)) : null,
                status: PublishStatus.DRAFT
            },
          });
    
          if(data){
            return data;
          } else {
            return null; 
          }
    } catch (error) {
      return error;
    }
  }