import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useMemo } from "react";
import { AdditionalSection } from "@/data/zod/resumeZod";
import { TextEditor } from "@/components/general/TextEditor";

interface AdditionalSectionFieldProps {
  section: Partial<AdditionalSection>; // Use Partial if new sections can be incomplete
  index: number;
  onUpdate: (index: number, updatedSection: Partial<AdditionalSection>) => void;
  onDelete: (index: number) => void;
}

export const AdditionalSectionField: React.FC<AdditionalSectionFieldProps> = ({
  section,
  index,
  onUpdate,
  onDelete,
}) => {
  const handleHeaderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate(index, { ...section, header: e.target.value });
  };

  // Adapt the 'field' prop for Details TextEditor
  const fieldForDetailsEditor = useMemo(() => ({
    value: section.details || "",
    onChange: (newContent: string) => {
      onUpdate(index, {
        ...section,
        details: newContent,
      });
    },
    onBlur: () => {},
    ref: () => {},
  }), [section, index, onUpdate]);
  return (
    <div className="relative p-4 border rounded-md group space-y-3">
      <button
        type="button"
        title="Delete Section"
        className="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors"
        onClick={() => onDelete(index)}
      >
        <X className="w-5 h-5" />
      </button>

      <div>
        <Label
          htmlFor={`additional-section-header-${index}`}
          className="text-sm font-medium"
        >
          Section Title
        </Label>
        <Input
          id={`additional-section-header-${index}`}
          value={section.header || ""}
          onChange={handleHeaderChange}
          placeholder="e.g., Projects, Certifications, Awards"
          className="mt-1"
        />
      </div>

      <div>
        <Label
          htmlFor={`additional-section-details-${index}`}
          className="text-sm font-medium"
        >
          Details (one item per line)
        </Label>        
        <TextEditor
          key={section._tempId || `additional-details-${index}`} // Prefer _tempId
          field={fieldForDetailsEditor}
        />
      </div>
    </div>
  );
};
