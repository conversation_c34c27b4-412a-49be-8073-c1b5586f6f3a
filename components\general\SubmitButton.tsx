"use client";

import { Heart, Loader2 } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useFormStatus } from "react-dom";
import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface GeneralSubmitButtonProps {
  text: string;
  width?: string;
  icon?: ReactNode;
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | null
    | undefined;
}

export function GeneralSubmitButton({
  text,
  width,
  icon,
  variant,
}: GeneralSubmitButtonProps) {
  const { pending } = useFormStatus();

  return (
    <Button className={cn(width, "cursor-pointer")} variant={variant} disabled={pending}>
      {pending ? (
        <>
          <Loader2 className="size-4 animate-spin" />
          <span>Submitting...</span>
        </>
      ) : (
        <>
          {icon && <div>{icon}</div>}
          <span>{text}</span>
        </>
      )}
    </Button>
  );
}

export function SaveJobButton({ savedJob }: { savedJob: boolean }) {
  const { pending } = useFormStatus();

  return (
    <Button className="cursor-pointer" variant="outline" disabled={pending} type="submit">
      {pending ? (
        <>
          <Loader2 className="size-4 animate-spin" />
          <span>Saving...</span>
        </>
      ) : (
        <>
          <Heart
            className={cn(
              "size-4", // Always apply size-4 for consistent icon sizing
              savedJob ? "fill-current text-red-500" : "transition-colors" // Apply fill/color if saved, or default transition
            )}
          />
          {savedJob ? "Saved" : "Save Job"}
        </>
      )}
    </Button>
  );
}
