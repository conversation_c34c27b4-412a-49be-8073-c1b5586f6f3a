"use server";

import { prisma } from "@/lib/prisma/prismaClient";

export const GetTwoFactorTokenByToken = async (token: string) => {
    try {
        const twoFactorToken = await prisma.userTwoFactorToken.findUnique({
            where: { token }
        });
        return twoFactorToken;
    } catch {
        return null;
    }
}

export const GetTwoFactorTokenByEmail = async (hashedEmail: string) => {
    try {
        const twoFactorToken = await prisma.userTwoFactorToken.findFirst({
            where: { 
                emailHash: hashedEmail
            }
        });
        return twoFactorToken;
    } catch {
        return null;
    }
}