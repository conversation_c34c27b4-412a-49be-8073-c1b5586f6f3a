"use client"
import React from 'react';
import { useState, useEffect } from 'react'


import { useRouter } from 'next/navigation'
import { Button } from '../ui/button'
import { CheckBlack, CheckGreen, RightArrowBlue } from '../general/Icons'
import { QuestionTooltip } from '../info/InfoTooltip'
import { getLocalCurrencyData, CurrencyData } from '@/utils/currencyUtils';
import PayPalSubscribeButton from '../payments/PayPalSubscribeButton';

interface PricingTabProps {
    enterprise: boolean
    free: boolean
    essential?: boolean
    yearly: boolean
    price: {
      monthly: number
      yearly: number
    };
    // For localized currency display
    exchangeRate: number;
    currencyCode: string; // e.g., "PHP", "USD"
    locale: string; // e.g., "en-PH", "en-US"
  }

function PriceSelectTab(props: PricingTabProps) {
    const { yearly, price, exchangeRate, currencyCode, locale, essential, free } = props;

    const basePriceUSD = yearly ? price.yearly : price.monthly;
    const textColorClass = essential ? "text-white dark:text-slate-200" : "text-slate-900 dark:text-slate-200";
    const commonSpanClasses = "justify-center font-bold text-3xl";

    const localPrice = basePriceUSD * exchangeRate;
    const formattedUSDPrice = basePriceUSD.toLocaleString('en-US', { // Always format USD price
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    });

    const formattedLocalPrice = localPrice.toLocaleString(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    });

    if (free && basePriceUSD === 0) {
        return (
            <>
                <div className="inline-flex items-baseline mb-2">
                    <span className={`${textColorClass} ${commonSpanClasses}`}>
                        {formattedUSDPrice}
                    </span>
                </div>
                <div className="flex items-center justify-center">
                    <span className="text-slate-500 font-medium text-sm">{yearly ? "Per Year" : "Per Month"}</span>
                </div>

                {/* Placeholder for local currency to maintain alignment if other plans show it */}
                { exchangeRate !== 1 && (
                    <div className="flex items-center justify-center mt-1 invisible" aria-hidden="true"> {/* Make it invisible but take up space */}
                        {/* Content styled to match the height of the actual local price display */}
                        <span className="text-gray-400 dark:text-gray-500 font-normal text-sm">(USD 0)</span>
                    </div>
                )}
            </>
        );
    }

    return (
        <>
            <div className="inline-flex items-baseline mb-2">
                <span className={`${textColorClass} ${commonSpanClasses}`}>
                    {formattedUSDPrice}
                </span><sup className='text-sm text-muted-foreground'>*</sup>
            </div>
            <div className="flex items-center justify-center">
                <span className="text-slate-500 font-medium text-sm">{props.yearly ? "Per Year" : "Per Month"}</span>
            </div>

            {/* Display local currency underneath */}
            { exchangeRate !== 1 && ( // Only show if not USD
                <div className="flex items-center justify-center mt-1">
                    <span className="text-gray-400 dark:text-gray-500 font-normal text-sm">({formattedLocalPrice})</span><sup className='text-sm text-muted-foreground'>**</sup>
                </div>
            )}
        </>
    )
}

interface PriceSelectTableProps {
    onPlanSelect: (planId: string, price: number, isAnnual: boolean, currencyData: CurrencyData, subscriptionID?: string, processor?: string) => void
}

export default function PriceSelectTable({ onPlanSelect }: PriceSelectTableProps) {
    const [isAnnual, setIsAnnual] = useState<boolean>(false);
    const router = useRouter();

    const [currencyData, setCurrencyData] = useState<CurrencyData | null>(null);
    const [isLoadingCurrency, setIsLoadingCurrency] = useState(true);
    const [currencyError, setCurrencyError] = useState<string | null>(null);

    // Helper function to map country code to currency (you'll need a more robust version)
    const mapCountryToCurrency = (countryCode?: string | null): string | null => {
      if (!countryCode) return null;
      const map: { [key: string]: string } = {
        "PH": "PHP", "GB": "GBP", "JP": "JPY", "US": "USD",
        // Add more European countries mapping to EUR
        "DE": "EUR", "FR": "EUR", "ES": "EUR", "IT": "EUR", "IE": "EUR",
        // ... other mappings
      };
      return map[countryCode.toUpperCase()] || null;
    };

    // Helper function to map currency to a preferred locale for formatting
    const mapCurrencyToLocale = (currencyCode?: string | null): string => {
      if (!currencyCode) return "en-US";
      const map: { [key: string]: string } = {
        "PHP": "en-PH", "GBP": "en-GB", "JPY": "ja-JP", "USD": "en-US",
        "EUR": "de-DE", // Or a generic like "en-IE" or based on a primary Eurozone country
        // ... other mappings
      };
      return map[currencyCode.toUpperCase()] || "en-US"; // Fallback
    };


    useEffect(() => {
        async function fetchUserCurrencyData() {
          let determinedLocaleForFormatting = "en-US"; // Initialize with a default
          try {
            setIsLoadingCurrency(true);
            console.log("[PriceTable DEBUG] Starting fetchUserCurrencyData. Initial determinedLocaleForFormatting:", determinedLocaleForFormatting);

            let determinedTargetCurrency = "USD"; // Default to USD

            // Attempt 1: Client-side GeoIP using ipapi.co
            if (typeof window !== "undefined") { // Ensure this runs only on the client
              try {
                console.log("[PriceTable DEBUG] Attempting client-side GeoIP lookup via ipapi.co...");
                const geoResponse = await fetch('https://ipapi.co/json/');
                if (geoResponse.ok) {
                  const geoData = await geoResponse.json();
                  console.log("[PriceTable DEBUG] Client-side GeoIP data received:", geoData);
                  const currencyFromGeo = mapCountryToCurrency(geoData.country_code) || geoData.currency; // Use mapped or direct currency
                  
                  if (currencyFromGeo && currencyFromGeo !== "USD") { // Prioritize if non-USD
                    determinedTargetCurrency = currencyFromGeo.toUpperCase();
                    determinedLocaleForFormatting = mapCurrencyToLocale(determinedTargetCurrency);
                    console.log(`[PriceTable DEBUG] Determined target currency from client-side GeoIP (service: ${geoData.service_used || 'unknown'}): ${determinedTargetCurrency}, Locale: ${determinedLocaleForFormatting}`)
                  } else if (currencyFromGeo === "USD") {
                     console.log(`[PriceTable DEBUG] Client-side GeoIP (service: ${geoData.service_used || 'unknown'}) resolved to USD or no specific non-USD currency found from GeoIP country.`);
                  } else if (!currencyFromGeo && geoData.country) {
                    console.log(`[PriceTable DEBUG] Client-side GeoIP (service: ${geoData.service_used || 'unknown'}) returned country ${geoData.country}, but no currency mapping found.`)
                  }
                } else {
                  console.warn("[PriceTable DEBUG] Client-side GeoIP lookup via ipapi.co failed with status:", geoResponse.status);
                }
              } catch (clientGeoError) {
                console.warn("[PriceTable DEBUG] Error during client-side GeoIP lookup:", clientGeoError);
              }
            }

            // Attempt 2: Fallback to browser locale if client-side GeoIP didn't set a non-USD currency
            if (determinedTargetCurrency === "USD" && typeof window !== "undefined" && navigator.language) {
              console.log("[PriceTable DEBUG] Client-side GeoIP did not yield a non-USD currency. Falling back to browser locale.");
              const rawUserLocale = navigator.language;
              const userLocale = rawUserLocale.toLowerCase();
              console.log("[PriceTable DEBUG] Detected browser locale (raw, lower):", rawUserLocale, userLocale);

              // Your existing locale-based currency determination logic
              if (userLocale.startsWith("en-ph") || userLocale.startsWith("tl-ph")) { determinedTargetCurrency = "PHP"; determinedLocaleForFormatting = "en-PH"; }
              else if (userLocale.startsWith("en-gb")) { determinedTargetCurrency = "GBP"; determinedLocaleForFormatting = "en-GB"; }
              else if (userLocale.startsWith("de")) { determinedTargetCurrency = "EUR"; determinedLocaleForFormatting = "de-DE"; }
              else if (userLocale.startsWith("ja-jp")) { determinedTargetCurrency = "JPY"; determinedLocaleForFormatting = "ja-JP"; }
              // ... add more mappings as needed
              console.log("[PriceTable DEBUG] Determined target currency from browser locale:", determinedTargetCurrency, "determinedLocaleForFormatting:", determinedLocaleForFormatting);
            } else if (determinedTargetCurrency === "USD") {
              // This means client-side GeoIP resulted in USD (or failed silently) and browser locale also didn't change it, or navigator.language wasn't available.
              console.log("[PriceTable DEBUG] Target currency remains USD after GeoIP and browser locale checks.");
            }

            // Directly call the server function.
            // The baseCurrencyCode defaults to "USD" in getLocalCurrencyData if not provided.
            console.log(`[PriceTable DEBUG] Calling getLocalCurrencyData with target: ${determinedTargetCurrency}`);
            const data: CurrencyData = await getLocalCurrencyData(determinedTargetCurrency);
            console.log("[PriceTable DEBUG] Received data from getLocalCurrencyData:", JSON.stringify(data));

            // If the fetched data's locale is different from our determined localeForFormatting
            // (e.g., API returns a generic 'en-US' for a currency we want to format as 'en-PH'),
            // prioritize the locale we determined for formatting if the currency code matches.
            const finalLocale = (data && data.currencyCode === determinedTargetCurrency) ? determinedLocaleForFormatting : (data?.locale || "en-US");
            console.log("[PriceTable DEBUG] Final locale for formatting:", finalLocale);

            if (data && typeof data.exchangeRate === 'number') { // Basic validation
              const dataToSet = { ...data, locale: finalLocale }; // Ensure the finalLocale is used
              console.log("[PriceTable DEBUG] Valid data received. Setting currencyData:", JSON.stringify(dataToSet));
              setCurrencyData(dataToSet);
              setCurrencyError(null);
            } else {
              // This case should ideally be handled by getLocalCurrencyData returning a default
              console.error('[PriceTable DEBUG] Received invalid currency data format from server action or data is null/undefined. Data:', data);
              throw new Error('Received invalid currency data format from server action.');
            }
          } catch (error) {
            console.error("[PriceTable DEBUG] Error in fetchUserCurrencyData:", error);
            setCurrencyError("Failed to load local currency information. Displaying prices in USD.");
            // Fallback to default USD if API or other logic fails
            const fallbackData = {
              exchangeRate: 1,
              currencySymbol: "$",
              currencyCode: "USD",
              locale: "en-US", // Always use en-US for USD fallback display
            };
            console.log("[PriceTable DEBUG] Setting fallback USD currencyData:", JSON.stringify(fallbackData));
            setCurrencyData(fallbackData);
          } finally {
            setIsLoadingCurrency(false);
            console.log("[PriceTable DEBUG] fetchUserCurrencyData finished.");
          }
        }
        fetchUserCurrencyData();
      }, []);

        if (isLoadingCurrency) {
        return (
            <div className="flex justify-center items-center h-screen text-muted-foreground">
                Loading pricing information...
            </div>
        );
    }

    // After loading, currencyData should be set (either to fetched data or USD fallback).
    // If it's still null, it indicates an unexpected issue in data fetching/fallback logic.
    if (!currencyData) {
        return (
            <div className="flex justify-center items-center h-screen text-red-500">
                {currencyError || "A critical error occurred while loading pricing information. Please refresh the page."}
            </div>
        );
    }

    // Define plans data structure
    const plansData = [
      {
        id: 'rosqt1rh3nwx2olngob7oacc',
        name: 'Free',
        price: { yearly: 0, monthly: 0 },
        propsForPricingTab: { free: true, enterprise: false, essential: false },
        headerClassName: "text-blue-600",
        badge: null,
        columnClassName: "border-gray-200", 
        buttonContainerClassName: "",
        buttonClassName: "text-blue-600 hover:text-blue-700 hover:bg-gray-300",
        features: {
          tokens: ['-', <CheckGreen key="cg-free-tokens"/>],
          resume: ['Up to 5', 'Up to 5'],
          jobMatching: ['Up to 5', 'Up to 5'],
          uniqueRole: '1',
          accessRights: '1',
          reports: '-',
          storage: '1 GB',
          apiAccess: '-',
        }
      },
      {
        id: 'sqgwp7nz5pyc1r5b50bhf5do',
        name: 'Basic',
        price: { 
            yearly: 176, 
            monthly: 16,
            paypalPlanId: {
                monthly: 'P-3BW537099B256463LNBHNALY', //'P-7B948564HJ098093GNBG334Y',
                yearly: 'P-1MD87294PF2281140NBHNB5Q', //'P-3CV34763H95821019NBG35RQ'
            } 
        },
        propsForPricingTab: { free: false, enterprise: false, essential: false },
        headerClassName: "text-blue-600",
        badge: null,
        columnClassName: "border-gray-200",
        buttonContainerClassName: "",
        buttonClassName: "text-blue-600 hover:text-blue-700 hover:bg-gray-300",
        features: {
          tokens: [<CheckGreen key="cg-basic-tokens1"/>, <CheckGreen key="cg-basic-tokens2"/>],
          resume: ['Up to 15', 'Up to 15'],
          jobMatching: ['Up to 15', 'Up to 15'],
          uniqueRole: 'Up to 8',
          accessRights: 'Up to 2',
          reports: 'Up to 3',
          storage: '10 GB',
          apiAccess: '-',
        }
      },
      {
        id: 'uscj4toc3lzinzls12nhbzj5',
        name: 'Essential',
        price: { 
            yearly: 1760, 
            monthly: 160,
            paypalPlanId: {
                monthly: 'P-5H538824UK739254PNBHNCMY', //'P-2X4614209X158742FNBG36KI',
                yearly: 'P-371635253S1991641NBHNC5I', //'P-3KS144956E3901021NBG37LQ'
            } 
        },
        propsForPricingTab: { free: false, enterprise: false, essential: true },
        headerClassName: "text-white",
        badge: <span className="px-4 py-2 text-2xl font-medium text-white bg-blue-600 rounded-full"> Essential </span>,
        columnClassName: "bg-gray-900 text-white border-white/20",
        headerContainerClassName: "bg-gray-900 rounded-t-xl",
        buttonContainerClassName: "text-white bg-yellow-500 rounded-b-xl",
        buttonClassName: "text-white hover:text-blue-600",
        features: {
          tokens: [<CheckBlack key="cb-ess-tokens1"/>, <CheckBlack key="cb-ess-tokens2"/>],
          resume: ['Unlimited', 'Unlimited'],
          jobMatching: ['Unlimited', 'Unlimited'],
          uniqueRole: 'Up to 30',
          accessRights: 'Up to 4',
          reports: 'Up to 10',
          storage: '100 GB',
          apiAccess: <CheckBlack  key="cb-ess-api"/>,
        }
      },
      {
        id: 'kynsy6n2va04gcvf50qwiy7f',
        name: 'Enterprise',
        price: { 
            yearly: 7689, 
            monthly: 699,
            paypalPlanId: {
                monthly: 'P-6YA950377T660382FNBHNDNI', //'P-5U131626AW114953MNBG4ACY',
                yearly: 'P-6RK22375NB842632ANBHNEEI', //'P-2P341234467053616NBG4AUA'
            } 
        },
        propsForPricingTab: { free: false, enterprise: true, essential: false },
        headerClassName: "text-blue-600",
        badge: null,
        columnClassName: "border-gray-200",
        buttonContainerClassName: "",
        buttonClassName: "text-blue-600 hover:text-blue-700 hover:bg-gray-300",
        features: {
          tokens: ['-', '-'],
          resume: ['Unlimited', 'Unlimited'],
          jobMatching: ['Unlimited', 'Unlimited'],
          uniqueRole: 'Unlimited',
          accessRights: 'Unlimited',
          reports: 'Unlimited',
          storage: 'Unlimited',
          apiAccess: <CheckBlack key="cb-ent-api" />,
        }
      },
    ];

    const featureDefinitions = [
      { key: 'tokens', label: 'Tokens', tooltip: "You can buy Tokens when you max out your limits to continue using the features.", subLabels: ['Reward tokens', 'Option to buy'], isSubList: true },
      { key: 'resume', label: 'Resume', tooltip: "Number of resume submission per role per month. Upgrade or purchase Tokens for additional resume submission and upload.", subLabels: ['Number of Resumes', 'Resume Evaluation'], isSubList: true },
      { key: 'jobMatching', label: 'Job Matching', tooltip: "Number of Resume job matches per role per month. Upgrade or purchase Tokens for additional resume job match.", subLabels: ['Number of Jobs', 'Match Evaluation'], isSubList: true },
      { key: 'uniqueRole', label: 'Number of Unique Role', tooltip: "Number of job roles posting per month. Upgrade or purchase Tokens for additional job roles." },
      { key: 'accessRights', label: 'Access Rights', tooltip: "Number of accounts that have access to Athena. Upgrade or purchase Tokens for additional accounts." },
      { key: 'reports', label: 'Reports', tooltip: "Reports you can create or receive. Upgrade or purchase Tokens for additional reports." },
      { key: 'storage', label: 'Storage', tooltip: "Total space allocated to your account. Upgrade or purchase Tokens for additional space." },
      { key: 'apiAccess', label: 'API Access', tooltip: "Access rights to our API for automated processing in your own system." },
    ];

    return (
        <>
            <section className="py-10 bg-white sm:py-16 lg:py-10 border rounded-xl">
                <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div className="max-w-xl mx-auto text-center">
                        <h2 className="text-4xl font-bold text-black lg:text-5xl sm:text-3xl">Select a Plan</h2>
                        {currencyError && (
                            <p className="mt-2 text-sm text-red-500">{currencyError}</p>
                        )}
                        <p className="mt-4 text-lg leading-relaxed text-gray-600">Simple pricing that scales with you.</p>
                    </div>
                    <div className="flex w-ful items-center justify-center">
                        <div className="mt-4 pl-2 pr-2 text-sm p-1 text-center bg-gray-200 leading-relaxed text-gray-600 border rounded-lg width-[20%]">
                            <p>Enjoy 1 month free with an annual plan!</p>
                        </div>
                    </div>

                    {/* Pricing toggle */}
                    <div className="flex justify-center max-w-[14rem] m-auto mb-8 lg:mb-16 pt-4">
                        <div className="relative flex w-full p-1 bg-white dark:bg-slate-900 rounded-full">
                        <span className="absolute inset-0 m-1 pointer-events-none" aria-hidden="true">
                            <span className={`absolute inset-0 w-1/2 bg-indigo-500 rounded-full shadow-sm shadow-indigo-950/10 transform transition-transform duration-150 ease-in-out ${isAnnual ? 'translate-x-0' : 'translate-x-full'}`}></span>
                        </span>
                        <button className={`relative flex-1 text-sm font-medium cursor-pointer h-8 rounded-full focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 dark:focus-visible:ring-slate-600 transition-colors duration-150 ease-in-out ${isAnnual ? 'text-white' : 'text-slate-500 dark:text-slate-400'}`} onClick={() => setIsAnnual(true)} aria-pressed={isAnnual}>Yearly <span className={`${isAnnual ? 'text-indigo-200' : 'text-slate-400 dark:text-slate-500'}`}></span></button>
                        <button className={`relative flex-1 text-sm font-medium cursor-pointer h-8 rounded-full focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 dark:focus-visible:ring-slate-600 transition-colors duration-150 ease-in-out ${isAnnual ? 'text-slate-500 dark:text-slate-400' : 'text-white'}`} onClick={() => setIsAnnual(false)} aria-pressed={isAnnual}>Monthly</button>
                        </div>
                    </div>

                    {/* <!-- lg+ --> */}
                    <div className="hidden mt-16 lg:block">
                        <table className="w-full">
                            <thead>
                                <tr>
                                    <th className="py-8 pr-4"></th>

                                    {plansData.map(plan => (
                                        <th key={plan.id} className={`px-4 py-8 text-center ${plan.headerContainerClassName || ''}`}>
                                            {plan.badge ? plan.badge : <span className={`text-2xl font-medium ${plan.headerClassName}`}>{plan.name}</span>}
                                            <br />
                                            <span className="mt-6 text-6xl font-bold">
                                                <PriceSelectTab
                                                    yearly={isAnnual}
                                                    price={plan.price}
                                                    exchangeRate={currencyData!.exchangeRate}
                                                    currencyCode={currencyData!.currencyCode}
                                                    locale={currencyData!.locale}
                                                    {...plan.propsForPricingTab}
                                                />
                                            </span>
                                        </th>
                                    ))}
                                </tr>
                            </thead>

                            <tbody>
                                {featureDefinitions.map((featureDef) => (
                                    <tr key={featureDef.key}>
                                        <td className="py-4 pr-4 font-medium border-b border-gray-200 dark:border-slate-700">
                                            {featureDef.label}
                                            {featureDef.tooltip && <QuestionTooltip content={featureDef.tooltip} />}
                                            {featureDef.isSubList && (
                                                <ul className="text-sm text-neutral-500 dark:text-slate-400 pl-4">
                                                    {featureDef.subLabels?.map((subLabel, i) => <li key={i}>{subLabel}</li>)}
                                                </ul>
                                            )}
                                        </td>
                                        {plansData.map((plan) => {
                                            const value = plan.features[featureDef.key as keyof typeof plan.features];
                                            const isEssentialColumn = plan.id === 'essential';
                                            const cellBaseClassName = `px-4 py-4 text-center border-b text-sm`;
                                            const cellSpecificClassName = isEssentialColumn ? plan.columnClassName : `text-neutral-500 dark:text-slate-400 ${plan.columnClassName}`;
                                            
                                            return (
                                                <td key={`${plan.id}-${featureDef.key}`} className={`${cellBaseClassName} ${cellSpecificClassName}`}>
                                                    {featureDef.isSubList && Array.isArray(value) ? ( // For features that are lists (e.g., Tokens, Resume)
                                                        <div className={`flex text-sm items-center justify-center ${isEssentialColumn ? 'text-white' : 'text-neutral-500 dark:text-slate-400'}`}>
                                                            <ul className={`flex flex-col text-sm items-center pt-6`}>
                                                                {value.map((subVal, i) => <li key={i}>{subVal}</li>)}
                                                            </ul>
                                                        </div>
                                                    ) : (
                                                        <span className='text-sm'>
                                                            {typeof value === 'string' || typeof value === 'number' || React.isValidElement(value) ? value : '-'}
                                                        </span>
                                                    )}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}

                                <tr>
                                    <td className="py-6 pr-4"></td>
                                    {plansData.map(plan => (
                                        <td key={`${plan.id}-button`} className={`px-4 py-6 text-center ${plan.buttonContainerClassName || ''}`}>
                                            {plan.price.paypalPlanId && !currencyData.locale.startsWith('en-PH') ? (
                                                <PayPalSubscribeButton
                                                    planID={isAnnual ? plan.price.paypalPlanId.yearly : plan.price.paypalPlanId.monthly}
                                                    containerId={`paypal-button-${plan.id}-${isAnnual ? 'yearly' : 'monthly'}`}
                                                    onSuccess={(subscriptionID) => {
                                                        console.log('Handling post-subscribe...')
                                                        onPlanSelect(plan.id, isAnnual ? plan.price.yearly : plan.price.monthly, isAnnual, currencyData, subscriptionID, 'PayPal')
                                                    }}
                                                />
                                                ) : (
                                                <Button
                                                    color="primary"
                                                    variant="ghost"
                                                    title={plan.name}
                                                    onClick={() => onPlanSelect(plan.id, isAnnual ? plan.price.yearly : plan.price.monthly, isAnnual, currencyData)}
                                                    className={`inline-flex items-center font-semibold cursor-pointer ${plan.buttonClassName}`}
                                                >
                                                    Select Plan <RightArrowBlue />
                                                </Button>
                                            )}
                                        </td>
                                    ))}
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    {/* <!-- Mobile View --> */}
                    <div className="lg:hidden mt-8 space-y-6 px-4 sm:px-0">
                        {plansData.map((plan) => (
                            <div key={plan.id} className={`rounded-xl shadow-lg overflow-hidden ${plan.id === 'essential' ? 'bg-gray-900 text-white' : 'bg-white dark:bg-slate-800 border dark:border-slate-700'}`}>
                                <div className={`p-6 text-center ${plan.id === 'essential' ? plan.headerContainerClassName?.replace('rounded-t-xl', '') : ''}`}>
                                    <h3 className="text-2xl font-medium mb-4">
                                    {plan.badge ? plan.badge : <span className={`${plan.id === 'essential' ? 'text-white' : 'text-blue-600 dark:text-blue-400'}`}>{plan.name}</span>}
                                    </h3>
                                    <div className="text-4xl font-bold">
                                        <PriceSelectTab
                                            yearly={isAnnual}
                                            price={plan.price}
                                            exchangeRate={currencyData!.exchangeRate}
                                            currencyCode={currencyData!.currencyCode}
                                            locale={currencyData!.locale}
                                            {...plan.propsForPricingTab}
                                        />
                                    </div>
                                </div>

                                <div className={`px-6 py-4 ${plan.id === 'essential' ? 'bg-gray-900' : 'bg-gray-50 dark:bg-slate-800/30'}`}>
                                    <ul className="space-y-3">
                                        {featureDefinitions.map((featureDef) => {
                                            const featureValue = plan.features[featureDef.key as keyof typeof plan.features];
                                            const labelColor = plan.id === 'essential' ? 'text-slate-300' : 'text-gray-600 dark:text-slate-400';
                                            const valueColor = plan.id === 'essential' ? 'text-white font-semibold' : 'text-gray-800 dark:text-slate-200 font-semibold';

                                            return (
                                                <li key={`${plan.id}-${featureDef.key}-mobile`} className={`py-2 ${plan.id !== 'essential' ? 'border-b dark:border-slate-700' : 'border-b border-gray-700'} last:border-b-0`}>
                                                    <div className="flex items-center mb-1 text-sm">
                                                        <span className={labelColor}>
                                                            {featureDef.label}
                                                        </span>
                                                        {featureDef.tooltip && <QuestionTooltip content={featureDef.tooltip} />}
                                                    </div>
                                                    {featureDef.isSubList && Array.isArray(featureValue) ? (
                                                        <ul className="pl-0 mt-1 space-y-0.5 text-sm">
                                                            {featureValue.map((subVal, index) => (
                                                            <li key={index} className="flex justify-between items-center">
                                                                <span className={`${labelColor} opacity-80`}>{featureDef.subLabels?.[index]}:</span>
                                                                <span className={valueColor}>{subVal}</span>
                                                            </li>
                                                            ))}
                                                        </ul>
                                                    ) : (
                                                        <div className={`text-sm text-right ${valueColor}`}>
                                                            {typeof featureValue === 'string' || typeof featureValue === 'number' || React.isValidElement(featureValue)
                                                            ? featureValue
                                                            : '-'}
                                                        </div>
                                                    )}
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </div>

                                <div className={`p-6 text-center ${plan.id === 'essential' ? plan.buttonContainerClassName?.replace('rounded-b-xl', '') : ''}`}>
                                    <Button
                                        color="primary"
                                        variant="default" // Using default variant for better visibility on mobile cards
                                        title={plan.name}
                                        onClick={() => onPlanSelect(plan.id, isAnnual ? plan.price.yearly : plan.price.monthly, isAnnual, currencyData)}
                                        className={`w-full sm:w-auto inline-flex items-center justify-center font-semibold ${plan.id === 'essential' ? plan.buttonClassName + ' bg-yellow-500 hover:bg-yellow-600 text-black' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
                                    >
                                        Select Plan <RightArrowBlue className={`${plan.id === 'essential' ? 'text-black': 'text-white'}`} />
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>

                    <em className='text-sm text-muted-foreground'>* Price does not include the 20% value-added tax (VAT). This will be added during payment.</em> <br />            
                    <em className='text-sm text-muted-foreground'>** Converted to current exchange rate.</em>
                </div>
            </section>
        </>
    )
}