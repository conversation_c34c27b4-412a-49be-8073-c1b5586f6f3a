import { deleteJobPost } from "@/actions/job/deleteJobPost";
import { getJob } from "@/actions/job/getJob";
import { FetchedJobDetails, GetJobActionPayload } from "@/data/zod/zodSchema";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const fetchJobData = async (
  jobId: string
): Promise<GetJobActionPayload> => {
  try {
    const jobDetails: FetchedJobDetails | null = await getJob(jobId);

    // Construct the payload based on the result from GetCompany
    if (jobDetails) {
      return {
        success: true,
        job: jobDetails,
      };
    } else {
      return {
        success: false,
        job: null,
        error: `Job with ID ${jobId} not found.`, 
      };
    }
  } catch (error) {
    console.error(`Error fetching company data for ID ${jobId}:`, error);
    return { 
        success: false,
        job: null,
        error: error instanceof Error ? error.message : "Failed to fetch data",
    };
  }
};

export function useJobActions(jobId: string) {
  const queryClient = useQueryClient();

  const jobQuery = useQuery<GetJobActionPayload, Error>({
    queryKey: ["jobData", jobId],
    queryFn: () => fetchJobData(jobId), 
    enabled: !!jobId, 
    staleTime: 30000, 
  });

  const deleteMutation = useMutation({
    mutationFn: async (jobId: string) => {
      return await deleteJobPost(jobId);
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["job"] });
    },
  });

  return {
    jobQuery,
    isLoading: jobQuery.isLoading,
    isError: jobQuery.isError,
    job: jobQuery.data?.job,
    deleteMutation,
    isDeleting: deleteMutation.isPending,
    deleteError: deleteMutation.isError,
    deleteResult: deleteMutation.data,
  };
}