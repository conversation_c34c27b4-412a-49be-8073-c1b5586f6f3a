import arcjet, { shield, detectBot, tokenBucket } from "./arcjet";
import { request } from "@arcjet/next";
import type { ArcjetMode } from "@arcjet/next"; // Arcjet type import may no longer be needed if not used elsewhere

const getResolvedArcjetMode = (): ArcjetMode => {
  const envMode = process.env.ARCJET_MODE?.toUpperCase();
  if (envMode === "LIVE") {
    return "LIVE";
  }
  // Default to DRY_RUN for undefined, empty, or any other value
  return "DRY_RUN";
};

const resolvedArcjetMode: ArcjetMode = getResolvedArcjetMode();

// By removing the explicit type annotation, TypeScript will infer the most accurate
// type for baseArcjetClient based on the configured rules. This is generally
// preferred for complex types returned by fluent APIs like Arcjet's rule builder.
export const baseArcjetClient = arcjet
  .withRule(
    shield({
      mode: resolvedArcjetMode,
    })
  )
  .withRule(
    detectBot({
      mode: resolvedArcjetMode,
      allow: ["CATEGORY:SEARCH_ENGINE", "CATEGORY:PREVIEW"],
    })
  );

/**
 * Ensures that the current request passes Arcjet protection.
 * Primarily for server actions.
 * Throws an Error with the message "Forbidden" if the request is denied by Arcjet.
 */
export async function ensureServerActionProtection(): Promise<void> {
  const req = await request();
  const decision = await baseArcjetClient.protect(req);
  if (decision.isDenied()) {
    throw new Error("Forbidden");
  }
}

/**
 * Ensures Arcjet protection for pages, with conditional token bucketing
 * and a custom requested amount.
 * Throws an Error "Access Denied" if denied.
 */
export async function ensurePageProtection(
  hasSession: boolean,
  options?: { requested?: number }
): Promise<void> {
  // Define the client specifically for page protection, including all relevant rules.
  // This starts from the base `arcjet` import and chains all rules.
  const pageSpecificClient = arcjet // arcjet from './arcjet'
    .withRule(
      shield({ // Apply shield rule
        mode: resolvedArcjetMode,
      })
    )
    .withRule(
      detectBot({ // Apply detectBot rule
        mode: resolvedArcjetMode,
        allow: ["CATEGORY:SEARCH_ENGINE", "CATEGORY:PREVIEW"], // Ensure these are the desired allow rules for pages
      })
    )
    .withRule( // Apply the conditional tokenBucket rule
      tokenBucket({
        mode: resolvedArcjetMode,
        capacity: 100, // Values from original page component
        interval: 60,
        refillRate: hasSession ? 30 : 10, // Conditional refillRate
      })
    );

  const req = await request();
  let decision;

  // Explicitly prepare the props for the protect call.
  // If options.requested has a value, pass it.
  // Otherwise, call protect with only one argument, allowing Arcjet's
  // tokenBucket to use its default `requested` amount (usually 1).
  if (options?.requested !== undefined) {
    decision = await pageSpecificClient.protect(req, { requested: options.requested });
  } else {
    // If options.requested is not provided, explicitly pass the default
    // requested amount (typically 1 for tokenBucket) as TypeScript
    // requires the 'requested' property in the second argument.
    decision = await pageSpecificClient.protect(req, { requested: 1 });
  }

  if (decision.isDenied()) {
    // Match the error message from the original page.tsx for consistency
    // Consider making this more specific based on decision.reason in the future if needed
    throw new Error("Access Denied");
  }
}
