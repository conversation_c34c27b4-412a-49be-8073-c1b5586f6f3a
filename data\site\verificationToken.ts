"use server";

import { prisma } from "@/lib/prisma/prismaClient";

export const GetVerificationTokenByEmail = async (hashedEmail: string) => {
    try {
        const verificationToken = await prisma.userVerificationToken.findFirst({ 
            where: { 
                emailHash: hashedEmail
            }});
        return verificationToken;
    } catch {
        return null;
    }
}

export const GetVerificationTokenByToken = async (token: string) => {
    try {
        const verificationToken = await prisma.userVerificationToken.findUnique({ 
            where: { 
                token 
            }
        });        
        return verificationToken;
    } catch {
        return null;
    }
}