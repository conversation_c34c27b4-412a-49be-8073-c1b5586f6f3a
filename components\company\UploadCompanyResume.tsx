"use client";

import { SaveUserFile } from "@/actions/file/saveFile";
import { UserFile } from "@/types/customTypes";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";
import { Upload, X } from "lucide-react";
import { useState } from "react";
import "@/styles/uploadthing.css";

export function UploadCompanyResume({companyId }: { companyId: String }) {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="default"
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <Upload className="w-4 h-4" />
          Upload Resume
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[425px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between pt-6 px-6">
              <DialogTitle className="text-lg font-semibold">
                Upload Resume
              </DialogTitle>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 cursor-pointer"
                onClick={() => setOpen(false)}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </div>
            <div className="px-6 pb-6">
              <DialogDescription>
                Upload recent resume twenty at a time.
              </DialogDescription>
              <div className="grid gap-4 py-4">
                <UploadDropzone
                  endpoint="companyDocumentUploader"
                  className="custom-class cursor-pointer w-full"
                  onClientUploadComplete={(res) => {
                    toast.success("Resume uploaded successfully.");

                    const data = res;

                    if (data && data.length > 0) {
                      // Process each file in the
                      setOpen(false);
                      const savePromises = data.map((file) => {
                        const resumeFile: UserFile = {
                          id: "",
                          userId: "userId",
                          companyId: companyId ? (companyId as string) : "",
                          fileUse: "RESUME",
                          fileType: file.name.split(".")[1].toUpperCase(),
                          fileName: file.name,
                          description: "User Resume",
                          key: file.key,
                          url: file.ufsUrl,
                          fileSize: file.size,
                        };

                        return SaveUserFile(resumeFile);
                      });

                      // Wait for all files to be saved
                      Promise.all(savePromises)
                        .then(() => {
                          toast.success(
                            `${data.length} resume(s) saved successfully.`
                          );
                        })
                        .catch((error) => {
                          console.error("Failed to save to database:", error);
                          toast.error("Failed to save file information");
                        });
                    }
                  }}
                  onUploadError={(error: Error) => {
                    toast.error(
                      `ERROR! A problem was encountered. ${error.message}`
                    );
                  }}
                  onUploadBegin={(name) => {
                    toast.info(`Uploading: ${name}`);
                  }}
                  onChange={(acceptedFiles) => {
                  }}
                  content={{
                    allowedContent({ ready, isUploading }) {
                      if (!ready) return "Checking what you allow";
                      if (isUploading) return "Uploading your resume...";
                      return (
                        <>
                          PDF or Word Document
                          <br />
                          (max size of 1MB per file)
                        </>
                      );
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
