"use client";

import styles, { layout } from "@/styles/style";
import Image from "next/image";
import { useRef, useState } from "react";
import { ImagePopup } from "../general/ImagePopup";

export function AppFeatures() {
  const [popupConfig, setPopupConfig] = useState<{ src: string; alt: string } | null>(null);
  
  const handleImageMouseEnter = (src: string, alt: string) => {
    if (popupConfig) { // If popup is already open, do nothing
      return;
    }
    setPopupConfig({ src, alt });
  };

 const closePopup = () => {
    setPopupConfig(null);
  };

  return (
    <>
      <section id="product" className={`${layout.sectionReverse} gap-4`}>
        {/* Job Seeker */}
        <div className={`${layout.sectionInfo} items-start justify-start border rounded-lg p-4`}>
          <h2 className={styles.heading2}> Job Seeker </h2>
          <p className="text-muted-foreground mt-4 mb-4">
            Let our AI do the heavy lifting—our intelligent platform automatically matches you with the right opportunities, evaluates your resume for maximum impact, and scores job fit so you can focus on landing your ideal role.
          </p>
          <div className="w-full grid grid-cols-3 grid-rows-1 gap-4 text-muted-foreground">
            <div onMouseEnter={() => handleImageMouseEnter("/feature/talent1.png", "AI generated online resume example")} className="cursor-pointer">
              <Image
                src="/feature/talent1.png"
                key="talent1_feature_img"
                alt="AI generated online resume example"
                width={400}
                height={500}
              />
            </div>
            <div onMouseEnter={() => handleImageMouseEnter("/feature/talent2.png", "AI job matching interface for job seeker")} className="cursor-pointer">
              <Image
                src="/feature/talent2.png"
                key="talent2_feature_img"
                alt="AI job matching interface for job seeker"
                width={400}
                height={500}
              />
            </div>
            <div onMouseEnter={() => handleImageMouseEnter("/feature/talent3.png", "Job fit score display for job seeker")} className="cursor-pointer">
              <Image
                src="/feature/talent3.png"
                key="talent3_feature_img"
                alt="Job fit score display for job seeker"
                width={400}
                height={500}
              />
            </div>
          </div>
          <div className="mt-4">
            <h3 className={styles.heading3}> How it works </h3>
                <div className="p-2">
                    <Image
                        src="/feature/talentWorks.png"
                        key="talentWorks"
                        alt="Talent process"
                        width={714}
                        height={300}
                    />
                </div>
          </div>
        </div>

        {/* Talent Seeker */}
        <div className={`${layout.sectionInfo} items-start justify-start border rounded-lg p-4`}>
          <h2 className={styles.heading2}> Talent Seeker </h2>
          <p className="text-muted-foreground mt-4 mb-4">
            Streamline your hiring process with our AI-powered platform—automated candidate matching, resume analysis, and job fit evaluations help you quickly identify top talent, so you can focus on making the right hires faster.
          </p>
        <div className="w-full grid grid-cols-3 grid-rows-1 gap-4 text-muted-foreground">
                <div onMouseEnter={() => handleImageMouseEnter("/feature/job1.png", "AI candidate matching interface for employer")} className="cursor-pointer">
                    <Image
                        src="/feature/job1.png"
                        key="job1_feature_img"
                        alt="AI candidate matching interface for employer"
                        width={400}
                        height={500}
                    />
                </div>
                <div onMouseEnter={() => handleImageMouseEnter("/feature/job2.png", "AI resume analysis tool for employer")} className="cursor-pointer">
                    <Image
                        src="/feature/job2.png"
                        key="job2_feature_img"
                        alt="AI resume analysis tool for employer"
                        width={400}
                        height={500}
                    />
                </div>
                <div onMouseEnter={() => handleImageMouseEnter("/feature/job3.png", "Job fit evaluation for candidate by AI")} className="cursor-pointer">
                    <Image
                        src="/feature/job3.png"
                        key="job3_feature_img"
                        alt="Job fit evaluation for candidate by AI"
                        width={400}
                        height={500}
                    />
                </div>
            </div>
          <div className="mt-4">
                <h3 className={styles.heading3}> How it works  </h3>
                <div className="p-2">
                    <Image
                        src="/feature/jobWorks.png"
                        key="talentWorks"
                        alt="Talent process"
                        width={714}
                        height={300}
                    />
                </div>
            </div>            
        </div>
    </section>

    {popupConfig && (
        <ImagePopup
          isOpen={!!popupConfig}
          src={popupConfig.src}
          alt={popupConfig.alt}
          onClose={closePopup}
        />
      )}

    </>
  );
}
