"use server";

import { JobSchema } from "@/data/zod/zodSchema";
import { auth } from "@/lib/auth/auth";
import { z } from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function updateJob(data: z.infer<typeof JobSchema>) {
  const session = await auth();
  
     if (!session?.user?.id) {
      throw new Error("User not authenticated.");
    }
  
    // Arcjet protection
    await ensureServerActionProtection();
    
  try {
    const validateData = JobSchema.parse(data);

    await prisma.jobPost.update({
      where: {
        id: validateData.id,
      },
      data: {
        jobTitle: validateData.jobTitle,
        employmentType: validateData.employmentType,
        experienceLevel: validateData.experienceLevel,
        country: validateData.country,
        location: validateData.location,
        department: validateData.department,
        salaryCurrency: validateData.salaryCurrency,
        salaryFrom: validateData.salaryFrom,
        salaryTo: validateData.salaryTo,
        jobDescription: validateData.jobDescription,
        listingDuration: validateData.listingDuration,
        interviewType: validateData.interviewType,
        localRemoteWork: validateData.localRemoteWork,
        overseasRemoteWork: validateData.overseasRemoteWork,
        skills: validateData.skills.map((skill) =>
          JSON.stringify({
            category: skill.category,
            name: skill.name,
          })
        ),
        languageRequirements: validateData.languageRequirements.map((lang) =>
          JSON.stringify({
            type: lang.type,
            language: lang.language,
            level: lang.level,
            certification: lang.certification,
          })
        ),
        tags: validateData.tags,
      },
    });

    return {
      success: "Success! Job information was updated.",
    };
  } catch (error) {
    return { error: `Error was encountered, please try again. ${error}` };
  }
}
