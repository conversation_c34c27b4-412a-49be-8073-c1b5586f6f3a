'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
  She<PERSON>Footer,
  SheetClose,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import { SaveUserFeedback } from '@/actions/user/userFeedback';
import { toast } from "sonner";

export function FloatingChatButton() {
  const [feedback, setFeedback] = useState('');
  const [submittedMessages, setSubmittedMessages] = useState<string[]>([]);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { data: session } = useSession();
  const maxLength = 500;

  // This function now only adds the message to the current session's list
  const handleAddMessageToSession = () => {
    if (feedback.trim()) {
      setSubmittedMessages(prevMessages => [...prevMessages, feedback.trim()]);
      setFeedback(''); // Clear textarea after adding
    }
  };

  const handleFeedbackChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (event.target.value.length <= maxLength) {
      setFeedback(event.target.value);
    }
  };

  const handleSaveSessionFeedback = async (messagesToSave: string[]) => {
    if (messagesToSave.length === 0) return;

    try {
        const response = await SaveUserFeedback(messagesToSave, session?.user?.id);

        if (response){
            if (response.success) {
                toast.success(response.success);
            } else if (response.error) {
                toast.error(response.error);
            } else {
                toast.error("An unexpected response was received from the server.");
            }
        } 
    } catch (error) {
        toast.error("An error occurred. Please try again.");      
    }
  };

  useEffect(() => {
    if (isSheetOpen) {
      // Clear messages when sheet opens, starting a new session
      setSubmittedMessages([]);
      setFeedback(''); // Also clear the input field
    } else {
      // Sheet is closing, save the session if there are messages
      handleSaveSessionFeedback(submittedMessages);
    }
  }, [isSheetOpen]); // eslint-disable-line react-hooks/exhaustive-deps 
  // submittedMessages is intentionally omitted from deps to avoid loop on save

  return (
    <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg z-50 bg-purple-gradient text-primary-foreground hover:bg-purple-gradient/90 cursor-pointer"
          aria-label="Open feedback form"
        >
          <Image
                src="/icons/positive-review.png"
                alt="Positive Review"
                width={32}
                height={32}
                className="rounded-md object-cover"
            />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] flex flex-col h-full">
        <SheetHeader className="mb-0">
          <div className="flex items-center space-x-3">
            <Image
                  src="/icons/positive-review.png"
                  alt="AI Assistant Icon"
                  width={32}
                  height={32}
                  className="rounded-md object-cover"
              />
            <SheetTitle>Your Feedback</SheetTitle>
          </div>
          <SheetDescription>
            Have a question or want to send us feedback? Let us know!
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 flex flex-col min-h-0 py-0 overflow-y-auto pl-4 pr-4">
          {/* Display all submitted messages */}
          {submittedMessages.map((message, index) => (
            <div key={index} className="p-2 border rounded-md bg-muted/50 mb-1">
              <p className="text-sm text-muted-foreground whitespace-pre-wrap break-words mb-2">{message}</p>
            </div>
          ))}
            {submittedMessages.length > 0 && ( // Show thank you message only if there are submitted messages
              <div className="text-xs text-blue-600 dark:text-blue-500 mt-2">
                {session?.user?.name ? (
                  <>
                    <p className="font-bold">Thank you, {session.user.name.split(' ')[0]}, for your feedback!</p>
                    <p>We appreciate you taking the time to share your thoughts. Our team will get back to you as soon as we can.</p>
                  </>
                ) : (
                  <>
                    <p className="font-bold">Thank you for your feedback!</p>
                    <p>We appreciate you taking the time to share your thoughts. Our team will get back to you as soon as we can.</p><br />
                    <p>If you haven’t registered yet, please include your name, email, or mobile number so we can follow up with you.</p>
                  </>
                )}
              </div>
            )}
        </div>

        <SheetFooter className="flex flex-col gap-2 pt-4">
            <div className="grid w-full gap-1.5">
            {/* Character counter */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pr-1">
              <p>Your message:</p>
              <span>{feedback.length} / {maxLength}</span>
            </div>
            <Textarea
              placeholder="Type your message or feedback here."
              id="feedback-message"
              value={feedback}
              onChange={handleFeedbackChange}
              className="w-full"
              rows={10}
              maxLength={maxLength} // HTML5 attribute for max length
            />
          </div>
          <div className="flex flex-row justify-between items-center space-x-2">
            <Button className="cursor-pointer" type="button" onClick={handleAddMessageToSession} disabled={!feedback.trim()}>Add Message</Button>
            <SheetClose asChild>
              <Button className="cursor-pointer" variant="outline">Close</Button>
            </SheetClose>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}