import { auth } from "@/lib/auth/auth";
import { redirect } from "next/navigation";

export async function manageAuthRedirect() {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // If not onboarded, always redirect to onboarding
  if (!session.user.onboarded) {
    return redirect("/home/<USER>");
  }

  // User is onboarded, redirect based on role/type
  if (session.user.role === "ADMIN") {
    return redirect("/admin/dashboard");
  }

  // Handle user types
  switch (session.user.userType) {
    case "COMPANY":
      return redirect("/company/dashboard");
    case "JOB_SEEKER":
    default:
      return redirect("/user/dashboard");
  }
}
