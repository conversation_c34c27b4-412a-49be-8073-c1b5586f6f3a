"use server";

import { auth } from "@/lib/auth/auth";;
import { inngest } from "@/lib/inngest/client";
import { applyToJobData } from "@/data/job/job";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function applyToJobPost(userId: string, jobId: string) {
  const session = await auth();

   if (!session?.user?.id) {
    // Or handle as per your middleware strategy, e.g., throw new Error("Unauthorized");
    // This assumes middleware might not cover this specific action directly,
    // or as a defense-in-depth. If covered by middleware, this error indicates an issue.
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await applyToJobData(userId, jobId);
    if (data) {
      return data;
    }
    return null;
  } catch (error) {
    return null;
  }
}
