"use server";

import * as z from "zod";
import { ResetSchema } from "@/data/zod/zodSchema";
import { GetUserByEmail } from "@/data/user/user";
import { GeneratePasswordResetToken } from "@/data/site/tokens";
import { hashEmail } from "@/utils/hashingHelpers";
import { SendEmail } from "@/lib/sendEmail";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { safeDecrypt } from "@/utils/security/safeDecrypt";

export const ForgotPassword = async (values: z.infer<typeof ResetSchema>) => {

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const validatedFields = ResetSchema.safeParse(values);

    if (!validatedFields.success) {
      return { error: "Invalid email!" };
    }

    const { email } = validatedFields.data;

    const hashedEmail = await hashEmail(email);

    const existingUser = await GetUserByEmail(hashedEmail);

    if (!existingUser) {
      return { error: "Email does not exist!" };
    }

    const passwordResetToken = await GeneratePasswordResetToken(hashedEmail);

    if (passwordResetToken) {
      const name = safeDecrypt(existingUser.name);
      const subject = "Your verification token";
      const html = `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div>
                        <p>Dear ${name},</p>
                        <p>You have requested to reset your password. Please click the link below to reset your password.</p>
                    </div>
                    <div>
                        <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/resetpassword?token=${passwordResetToken.token}" 
                           style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            Reset Password
                        </a>
                    </div>
                    <div>
                        <p>If you did not request this, please ignore this email. The token will expire and will be deleted.</p>
                    </div>
                    <div>
                        <p>Regards, <br /> Edison AIX Team</p>
                    </div>
                    </div>
                `;

      await SendEmail({
        sendTo: email,
        subject: subject,
        html: html,
      });

      return {
        success:
          "Success! A reset password email was sent. Please check your email.",
      };
    }
  } catch (error) {
    return { error: `Error was encountered, please try again. ${error}` };
  }
};
