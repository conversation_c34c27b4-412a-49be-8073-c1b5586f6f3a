import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { footerLinks, socialMedia } from "@/data/constants";
import Image from "next/image";

export function Footer() {
    return (
      <footer className="flex flex-col w-full items-start justify-start py-4 px-6">
        <section className="flex flex-col w-full">
            <div className="grid grid-cols-1 items-start md:grid-cols-5 md:items-center gap-10 mb-8 pr-20 w-full"> {/* Consider adjusting pr-20 if layout shifts too much */}
                <div className="md:col-span-2 flex flex-col md:flex-row pl-4 justify-start items-center">
                    <div className="flex flex-col pl-4 justify-start items-start w-[40%] pb-6">
                        <Image
                        src="/logo/logo.png"
                        alt="hoobank"
                        width={150}
                        height={150}
                        className="object-contain"
                        />
                    </div>
                    <div className="items-center flex justify-start w-[60%]">
                        <Image
                            src="/logo/MS_Startups_Celebration_Badge_Light.png"
                            alt="hoobank"
                            width={250}
                            height={175}
                            className="object-contain"
                        />
                    </div>
                </div>
                <div className="md:col-span-3 w-full flex flex-row text-muted-foreground justify-end ga-6 flex-wrap">
                    {footerLinks.map((footerLink) => (
                    <div
                        key={footerLink.id}
                        className="flex flex-col ss:my-0 my-4 min-w-[150px]"
                    >
                        <h4
                        className={`font-poppins font-medium text-[18px] leading-[27px] text-white`}
                        >
                        {footerLink.title}
                        </h4>
                        <ul className="list-none mt-4">
                        {footerLink.links.map((link, index) => (
                            <li
                                key={link.name}
                                className={`font-poppins font-normal text-[16px] leading-[24px] text-dimWhite hover:text-secondary cursor-pointer ${index !== footerLink.links.length - 1 ? "mb-4" : "mb-0"
                                    }`}
                            >
                                <a href={`${link.link}`}>
                                {link.name}
                                </a>
                            </li>
                        ))}
                        </ul>
                    </div>
                    ))}
                </div>
            </div>
            {/* Copyright, Powered by, and Social Media Section */}
            <div className="w-full grid grid-cols-1 md:grid-cols-3 items-center gap-x-4 gap-y-6 pt-4 pb-4 border-t border-gray-600">
                {/* Column 1: Copyright */}
                <div className="flex justify-center text-center md:justify-start md:text-left items-center">
                    <p className="font-poppins font-normal text-md leading-[27px] text-muted-foreground">
                        © {new Date().getFullYear()} Edison AI Solutions, Inc. All rights reserved.
                    </p>
                </div>

                {/* Column 2: Powered by */}
                <div className="flex justify-center items-center">
                    <div className="text-sm text-muted-foreground flex items-center">
                    Powered by{' '}
                    <div className="flex items-center gap-2 ml-2">
                        <a
                            rel="noopener noreferrer"
                            target="_blank"
                            href="https://www.edisonaix.com"
                            className="flex items-center"
                        >
                            <Avatar className="w-10 h-10 border-1">
                                <AvatarImage
                                    src="/logo/edisonlogo.png"
                                    alt="Edison AIX"
                                    title="Edison AIX"
                                />
                                <AvatarFallback className="text-sm">AIX</AvatarFallback>
                            </Avatar>
                        </a>
                        
                        <span className="mx-1">*</span>
                        
                        <a
                            rel="noopener noreferrer"
                            target="_blank"
                            href="https://azure.microsoft.com/en-us/solutions/ai/?msockid=09745308b37468df061e4775b22e6973"
                            className="flex items-center"
                        >
                            <Avatar className="w-10 h-10 border-1">
                                <AvatarImage
                                    src="/logo/azure.png"
                                    alt="Microsoft Azure"
                                    title="Microsoft Azure"
                                />
                                <AvatarFallback className="text-sm">A</AvatarFallback>
                            </Avatar>
                        </a>
                        
                        <span className="mx-1">*</span>
                        
                        <a
                            rel="noopener noreferrer"
                            target="_blank"
                            href="https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.3"
                            className="flex items-center"
                        >
                            <Avatar className="w-10 h-10 border-1">
                                <AvatarImage
                                    src="/logo/mistral.png"
                                    alt="Mistral AI"
                                    title="Mistral AI"
                                />
                                <AvatarFallback className="text-sm">M</AvatarFallback>
                            </Avatar>
                        </a>
                        
                        <span className="mx-1">*</span>
                        
                        <a
                            rel="noopener noreferrer"
                            target="_blank"
                            href="https://huggingface.co/google/gemma-3-12b-it"
                            className="flex items-center"
                        >
                            <Avatar className="w-10 h-10 border-1">
                                <AvatarImage
                                    src="/logo/gemma3.png"
                                    alt="Gemma"
                                    title="Google Gemma 3"
                                />
                                <AvatarFallback className="text-sm">G3</AvatarFallback>
                            </Avatar>
                        </a>
                        </div>
                    </div>
                </div>

                {/* Column 3: Social Media */}
                <div className="flex justify-center md:justify-end items-center">
                    <div className="flex flex-row text-muted-foreground">
                        {socialMedia.map((social, index) => (
                            <a
                                key={social.id}
                                title={social.id}
                                rel="noopener noreferrer"
                                target="_blank"
                                href={social.link}
                            >
                                <Image
                                    src={social.icon}
                                    alt={social.id}
                                    width={21}
                                    height={21}
                                    className={`object-contain cursor-pointer ${index !== socialMedia.length - 1 ? "mr-6" : "mr-0"}`}
                                />
                            </a>
                        ))}
                    </div>
                </div>
            </div>
        </section>
      </footer>
    );
  }
  
