import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useMemo } from "react";
import { TextEditor } from "@/components/general/TextEditor";

interface Education {
    degree?: string | undefined;
    institution?: string | undefined;
    location?: string | undefined;
    details?: string | undefined; // Changed to string to store JSON from TextEditor
    gpa?: string | undefined;
    honors?: string[] | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
    _tempId?: string; // Ensure _tempId is part of the interface
}

interface EducationFieldProps {
  edu: Education;
  index: number;
  onUpdate: (index: number, updatedEdu: Education) => void;
  onDelete: (index: number) => void;
}

export const EducationField: React.FC<EducationFieldProps> = ({
  edu,
  index,
  onUpdate,
  onDelete,
}) => {
  // Adapt the 'field' prop for TextEditor
  const fieldForDetailsEditor = useMemo(() => ({
    value: edu.details || "",
    onChange: (newContent: string) => {
      onUpdate(index, {
        ...edu,
        details: newContent,
      });
    },
    onBlur: () => {},
    ref: () => {},
  }), [edu, index, onUpdate]);

  return (
    <div className="relative p-4 border rounded-md group">
      <button
        type="button"
        title="Delete Education"
        className="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors"
        onClick={() => onDelete(index)}
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="md:col-span-2">
          <Label
            htmlFor={`edu-degree-${index}`}
            className="text-sm font-medium"
          >
            Degree
          </Label>
          <Input
            id={`edu-degree-${index}`}
            value={edu.degree}
            onChange={(e) => {
              onUpdate(index, {
                ...edu,
                degree: e.target.value,
              });
            }}
            placeholder="Degree"
            required
          />
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor={`edu-school-${index}`}
            className="text-sm font-medium"
          >
            School
          </Label>
          <Input
            id={`edu-school-${index}`}
            value={edu.institution}
            onChange={(e) => {
              onUpdate(index, {
                ...edu,
                institution: e.target.value,
              });
            }}
            placeholder="School/Institution"
            required
          />
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor={`edu-location-${index}`}
            className="text-sm font-medium"
          >
            Location
          </Label>
          <Input
            id={`edu-location-${index}`}
            value={edu.location}
            onChange={(e) => {
              onUpdate(index, {
                ...edu,
                location: e.target.value,
              });
            }}
            placeholder="Location"
            required
          />
        </div>

        <div className="md:col-span-2">
          <Label className="text-sm font-medium">Date Range</Label>
          <DateRangePicker
            startDate={edu.startDate ?? undefined}
            endDate={edu.endDate ?? undefined}
            onStartDateChange={(date) => {
              onUpdate(index, {
                ...edu,
                startDate: date,
              });
            }}
            onEndDateChange={(date) => {
              onUpdate(index, {
                ...edu,
                endDate: date,
              });
            }}
          />
        </div>
        
        <div className="md:col-span-2">
            <Label
            htmlFor={`edu-details-${index}`}
            className="text-sm font-medium"
            >
                Details
            </Label>            
            <TextEditor
              key={edu._tempId || `education-details-${index}`} // Prefer _tempId
              field={fieldForDetailsEditor}
            />
        </div>
        
        <div className="flex md:col-span-2 justify-between gap-4">
            <div className="flex flex-col w-full">
                <Label
                    htmlFor={`edu-gpa-${index}`}
                    className="text-sm font-medium"
                >
                    GPA
                </Label>
                <Input
                    id={`edu-gpa-${index}`}
                    type="number"
                    value={edu.gpa}
                    onChange={(e) => {
                    onUpdate(index, {
                        ...edu,
                        gpa: e.target.value,
                    });
                    }}
                    placeholder="GPA"
                    required
                />
            </div>
            <div className="flex flex-col w-full">
                <Label
                    htmlFor={`edu-honors-${index}`}
                    className="text-sm font-medium"
                >
                    Honors
                </Label>
                <textarea
                    id={`edu-honors-${index}`}
                    className="w-full p-2 border rounded-md font-mono text-sm"
                    value={Array.isArray(edu.honors) ? edu.honors.join('\n') : edu.honors || ''}
                    onChange={(e) => {
                        onUpdate(index, {
                            ...edu,
                            honors: e.target.value.split('\n'),
                        });
                    }}
                    placeholder="Honors"
                    rows={3}
                    required
                />
            </div>
        </div>

      </div>
    </div>
  );
};
