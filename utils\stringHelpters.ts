// string cleaner
export function safeField(field: string | string[] | null | undefined): string {
  if (Array.isArray(field)) {
    return field.join(', ');
  }
  return field?.trim() ?? '';
};

// name, title, header, etc normalizer
export function normalizeHeaders(header: string): string {
  if (!header || typeof header !== 'string') return ''; // Added type check for robustness
  
  // Handle special cases for common name prefixes
  const prefixes = ['mc', 'mac', 'o\'', 'van', 'von', 'de', 'la', 'du', 'di', 'del'];
  // List of known acronyms or specific capitalizations to preserve
  const knownAcronyms = new Set(["AI", "AWS", "BA", "BAU", "BI", "BFA", "BS", "BSc", "CA", "CAO", "CAPM", "CBAP", "CDO", "CEO", "CHRO", "CIO", "CMO", "COO", "CPA", "CPO", "CRM", "CSM", "CSO", "CTO", "DBA", "DEI", "DevOps", "Dir", "ED", "EHR", "ELS", "EOD", "EOM", "ETA", "ETD", "EU", "EVP", "FAQ", "FMCG", "FTE", "FYE", "FYI", "GIF", "GIS", "H1/H2", "HCM", "HIPAA", "HR", "HTML", "IAM", "ICT", "IoT", "IP", "IR", "ISO", "IT", "ITIL", "JD", "KPI", "KYC", "LAN", "LLC", "LLP", "MA", "MBA", "MD", "MFA", "MP", "MS", "MTD", "MVP", "NASA", "NDA", "OKR", "OSHA", "P&L", "PC", "PhD", "PIN", "PM", "PMP", "PO", "POC", "Pres", "PRINCE2", "QA", "QC", "R&D", "RFP", "ROI", "SaaS", "SCM", "SCUBA", "SDLC", "SEO", "SGT", "SLA", "SMB", "SME", "SOP", "SQL", "STEM", "SUPR", "SWOT", "TAT", "TBD", "TQM", "UI", "UK", "URL", "USA", "UX", "VC", "VM", "VPN", "VP", "WIP", "WiFi", "XML", "XSS"]); 
  
  return header.split(' ')
    .map(word => {
      // Preserve known acronyms
      if (knownAcronyms.has(word)) {
        return word;
      }

      // Convert the entire word to lowercase first
      const lowerWord = word.toLowerCase();
      
      // Check for prefixes
      for (const prefix of prefixes) {
        if (lowerWord.startsWith(prefix) && lowerWord.length > prefix.length) {
          // Capitalize the prefix itself (e.g., "Mc") and the letter after it
          const prefixPart = prefix.charAt(0).toUpperCase() + prefix.slice(1);
          const restOfWord = lowerWord.slice(prefix.length);
          return prefixPart + restOfWord.charAt(0).toUpperCase() + restOfWord.slice(1);
        }
      }
      
      // Handle hyphenated names (like Smith-Jones)
      if (lowerWord.includes('-')) {
        return lowerWord.split('-')
          .map(part => {
            if (knownAcronyms.has(part.toUpperCase())) return part.toUpperCase(); // Preserve acronyms within hyphenated words
            return part.charAt(0).toUpperCase() + part.slice(1);
          })
          .join('-');
      }
      
      // Preserve other all-caps words if they are short (e.g., initials or short acronyms not in the list)
      if (word.length > 1 && word.length <= 3 && word === word.toUpperCase()) {
        return word;
      }

      // Standard case: capitalize first letter only
      return lowerWord.charAt(0).toUpperCase() + lowerWord.slice(1);
    })
    .join(' ');
};

// Normalize paragraphs with multiple sentences
export function normalizeParagraph(text: string): string {
  if (!text) return '';
  
  // Split by sentence endings (., !, ?) followed by a space or end of string
  const sentences = text.split(/([.!?](?:\s|$))/);
  
  let result = '';
  for (let i = 0; i < sentences.length; i++) {
    // Even indices are sentence content, odd indices are punctuation
    if (i % 2 === 0) {
      // Sentence content - capitalize first letter, lowercase the rest
      const sentence = sentences[i].trim();
      if (sentence) {
        result += sentence.charAt(0).toUpperCase() + sentence.slice(1).toLowerCase();
      }
    } else {
      // Punctuation - keep as is
      result += sentences[i];
    }
  }
  
  return result;
}

// Converts snake_case to Title Case
export function snakeCaseToTitleCase(text: string): string {
  if (!text) return '';
  
  return text.split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

export function parseToNumberOrDefault(value: any, defaultValue: number = 0): number {
    if (value === null || value === undefined) return defaultValue;
    const num = parseFloat(value as string); 
    return isNaN(num) ? defaultValue : num;
};


// Helper function to parse string arrays
const parseStringArray = (data: string | null): string[] => {
  if (!data) return [];
  
  try {
    // Try to parse as JSON
    const parsed = JSON.parse(data);
    if (Array.isArray(parsed)) return parsed;
    return [];
  } catch (e) {
    // If it's not valid JSON, check if it looks like an array string
    if (data.startsWith('[') && data.endsWith(']')) {
      // Simple string parsing for array-like strings
      return data
        .slice(1, -1)
        .split(',')
        .map(item => item.trim().replace(/"/g, '').replace(/'/g, ''));
    }
    // Return as single item if it's just a string
    return [data];
  }
};

export function convertUndefinedToNull(obj: any): any {
  if (obj === undefined) {
    return null;
  }
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map(convertUndefinedToNull);
  }
  const newObj: { [key: string]: any } = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      newObj[key] = convertUndefinedToNull(obj[key]);
    }
  }
  return newObj;
};