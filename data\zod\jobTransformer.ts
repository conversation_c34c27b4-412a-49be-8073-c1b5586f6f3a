import { 
  JobData, 
  JobS<PERSON>dard<PERSON>ields, 
  JobAdditionalSection,
  SalaryRange,
  JobDataSchema,
  JobStandardFieldsSchema,
  JobSalaryRangeSchema
} from "@/data/zod/resumeZod";

type ParsedJobFields = Partial<JobStandardFields> & {
  title?: string;
  company?: string;
  location?: string;
  employment_type?: string;
  experience_level?: string;
  education_required?: string;
  salary_range?: {
    min?: string;
    max?: string;
    currency?: string;
  };
};

export function transformParsedDataToJobSchema(parsed_data: any): JobData {
  // Normalize keys to lowercase for consistent access
  const normalizedData = normalizeKeys(parsed_data);
  const data = normalizedData.job || normalizedData;
  const standard_fields = data.standard_fields || {};
  
  // Process additional sections
  const additionalSections: JobAdditionalSection[] = [];
  if (data.additional_sections) {
    if (typeof data.additional_sections === 'object' && !Array.isArray(data.additional_sections)) {
      // Handle case where additional_sections is an object with section names as keys
      Object.entries(data.additional_sections).forEach(([key, value]) => {
        additionalSections.push({
          section_name: key,
          details: Array.isArray(value) ? value.map(String) : [String(value)]
        });
      });
    } else if (Array.isArray(data.additional_sections)) {
      // Handle case where additional_sections is an array
      data.additional_sections.forEach((section: any) => {
        if (section && typeof section === 'object') {
          // If it's an object with section_name and details
          if (section.section_name) {
            additionalSections.push({
              section_name: typeof section.section_name === 'string' 
                ? section.section_name 
                : Array.isArray(section.section_name) 
                  ? section.section_name[0] 
                  : String(section.section_name),
              details: Array.isArray(section.details)
                ? section.details.map(String)
                : section.details ? [String(section.details)] : []
            });
          } else {
            // If it's an object with section name as key and details as value
            Object.entries(section).forEach(([key, value]) => {
              additionalSections.push({
                section_name: key,
                details: Array.isArray(value) ? value.map(String) : [String(value)]
              });
            });
          }
        }
      });
    }
  }

  // Create salary range object
  let salaryRange: SalaryRange = {
    min: standard_fields.salary_range?.min || '',
    max: standard_fields.salary_range?.max || '',
    currency: standard_fields.salary_range?.currency || ''
  };

  // If salary_range is an array (as in some formats), use the first item
  if (Array.isArray(standard_fields.salary_range) && standard_fields.salary_range.length > 0) {
    const firstRange = standard_fields.salary_range[0];
    salaryRange.min = firstRange.min || '';
    salaryRange.max = firstRange.max || '';
    salaryRange.currency = firstRange.currency || '';
  }

  // Try to detect currency from the salary values if not explicitly provided
  if (!salaryRange.currency) {
    const minSalary = String(salaryRange.min || '');
    const currencySymbols = {
      '$': 'USD',
      '€': 'EUR',
      '£': 'GBP',
      '¥': 'JPY',
      '₱': 'PHP',
      'P': 'PHP'
    };
    
    for (const [symbol, code] of Object.entries(currencySymbols)) {
      if (minSalary.includes(symbol)) {
        salaryRange.currency = code;
        break;
      }
    }
  }

  return {
    standardFields: {
      title: standard_fields.title || '',
      company: standard_fields.company || '',
      location: standard_fields.location || '',
      employment_type: standard_fields.employment_type || '',
      experience_level: standard_fields.experience_level || '',
      education_required: standard_fields.education_required || '',
      salary_range: [salaryRange], // Wrap in array to match schema
      requirements: Array.isArray(standard_fields.requirements) 
        ? standard_fields.requirements 
        : [],
      responsibilities: Array.isArray(standard_fields.responsibilities) 
        ? standard_fields.responsibilities 
        : [],
      skills_required: Array.isArray(standard_fields.skills_required) 
        ? standard_fields.skills_required 
        : [],
    } as JobStandardFields,
    additionalSections: additionalSections,
  };
}

// Helper function to normalize object keys to lowercase
function normalizeKeys(obj: any): any {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(normalizeKeys);
  }

  return Object.keys(obj).reduce((acc: any, key: string) => {
    const normalizedKey = key.toLowerCase();
    acc[normalizedKey] = normalizeKeys(obj[key]);
    return acc;
  }, {});
}




