"use client";

import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { UserFile } from "@/types/customTypes";
import { SaveUserFile } from "@/actions/file/saveFile";
import "@/styles/uploadthing.css";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { ErrorMessage } from "@/components/info/ErrorMessage";

export default function JobResumeUploadPage({companyId }: { companyId: String }) {
  const [postType, setPostType] = useState(2);
  const [errorJob, setErrorJob] = useState<string | undefined>("");
  const [successJob, setSuccessJob] = useState<string | undefined>("");
  const [errorRes, setErrorRes] = useState<string | undefined>("");
  const [successRes, setSuccessRes] = useState<string | undefined>("");

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex flex-col mb-6">
        <h1 className="text-2xl font-bold">Upload your Job Description and stack of Resumes</h1>
            
      </div>

        <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-1/2 border-1 border-dashed p-4">
                <div className="flex items-center justify-center font-bold bg-primary/10 text-primary">
                    Upload Jobs
                </div>
                <div className="flex flex-col gap-4 pt-6">                
                    <div className="text-sm pt-2">
                        <div className="flex flex-col space-y-2 rounded-lg border p-4 h-[120px]">
                            <label className="flex items-center space-x-2">
                                <input
                                type="radio"
                                name="postType"
                                value={1}
                                onChange={(e) => setPostType(Number(e.target.value))}
                                className="w-4 h-4"
                                />
                                <span>AI processing and automatic job posting (default: 30 days)</span>
                            </label>
                            <label className="flex items-center space-x-2">
                                <input
                                type="radio"
                                name="postType"
                                value={2}
                                defaultChecked
                                onChange={(e) => setPostType(Number(e.target.value))}
                                className="w-4 h-4"
                                />
                                <span>AI processing only (includes job matching and evaluation). I will post the job manually.</span>
                            </label>
                        </div>
                    </div>
                    <div className="flex justify-center pt-4 pb-4">
                        <Separator />
                    </div>
                    <div className="flex flex-col space-y-4">
                        <UploadDropzone
                            endpoint="companyDocumentUploader"
                            className="custom-class cursor-pointer w-full"
                            onClientUploadComplete={(res) => {
                                toast.success("Job Description uploaded successfully.");
                                setSuccessJob("Job Description uploaded successfully.");

                                const data = res;

                                if (data && data.length > 0) {
                                // Process each file in the
                                const savePromises = data.map((file) => {
                                    const resumeFile: UserFile = {
                                        id: "",
                                        userId: "userId",
                                        companyId: companyId ? (companyId as string) : "",
                                        fileUse: "JOB",
                                        fileType: file.name.split(".")[1].toUpperCase(),
                                        fileName: file.name,
                                        description: "Company Job Description",
                                        key: file.key,
                                        url: file.ufsUrl,
                                        fileSize: file.size,
                                        postType: postType
                                    };

                                    return SaveUserFile(resumeFile);
                                });

                                // Wait for all files to be saved
                                Promise.all(savePromises)
                                    .then(() => {
                                        toast.success(`${data.length} Job description saved successfully.`);
                                        setSuccessJob(`${data.length} Job description saved successfully.`);
                                    })
                                    .catch((error) => {
                                        console.error("Failed to save to database:", error);
                                        toast.error("Failed to save file information");
                                        setErrorJob("Failed to save file information");
                                    });
                                }
                            }}
                            onUploadError={(error: Error) => {
                                toast.error(
                                `ERROR! A problem was encountered. ${error.message}`
                                );
                                setErrorJob(
                                    `ERROR! A problem was encountered. ${error.message}`
                                );
                            }}
                            onUploadBegin={(name) => {
                                toast.info(`Uploading: ${name}`);
                            }}
                            onChange={(acceptedFiles) => {
                            }}
                            content={{
                                allowedContent({ ready, isUploading }) {
                                if (!ready) return "Checking what you allow";
                                if (isUploading) return "Uploading your resume...";
                                return (
                                    <>
                                    PDF or Word Document
                                    <br />
                                    (max size of 2MB per file)
                                    </>
                                );
                                },
                            }}
                        />
                    </div>
                    <div className="flex flex-col space-y-4 pt-10">
                        <ErrorMessage message={errorJob} />
                        <SuccessMessage message={successJob} />
                    </div>
                </div>
            </div>
            <div className="flex-col w-full md:w-1/2 border-1 border-dashed p-4">
                <div className="flex items-center justify-center font-bold bg-primary/10 text-primary">
                    Upload Resumes
                </div>
                <div className="flex flex-col gap-4 pt-6">                
                    <div className="text-sm pt-2">
                        <div className="flex flex-col space-y-2 rounded-lg border p-4 h-[120px]">
                            <em className="text-sm text-muted-foreground">
            By uploading the candidate's resume, you agree to our 
            <a href="/public/privacy" className="text-primary underline"> Privacy Policy</a> and 
            <a href="/public/terms" className="text-primary underline"> Terms of Service</a>.
                            </em>
                            <br />
                            <em className="text-sm text-muted-foreground">
                            We follow global regulations and standards on user privacy. All personally identifiable and sensitive data is stored encrypted in our database.
                            </em>
                        </div>
                    </div>
                    <div className="flex justify-center pt-4 pb-4">
                        <Separator />
                    </div>
                    <div className="flex flex-col space-y-4">
                        <UploadDropzone
                            endpoint="companyDocumentUploader"
                            className="custom-class cursor-pointer w-full"
                            onClientUploadComplete={(res) => {
                                toast.success("Resume uploaded successfully.");
                                setSuccessRes("Resume uploaded successfully.");

                                const data = res;

                                if (data && data.length > 0) {
                                // Process each file in the
                                const savePromises = data.map((file) => {
                                    const resumeFile: UserFile = {
                                    id: "",
                                    userId: "userId",
                                    companyId: companyId ? (companyId as string) : "",
                                    fileUse: "RESUME",
                                    fileType: file.name.split(".")[1].toUpperCase(),
                                    fileName: file.name,
                                    description: "User Resume",
                                    key: file.key,
                                    url: file.ufsUrl,
                                    fileSize: file.size,
                                    };

                                    return SaveUserFile(resumeFile);
                                });

                                // Wait for all files to be saved
                                Promise.all(savePromises)
                                    .then(() => {
                                        toast.success(`${data.length} resume(s) saved successfully.`);
                                        setSuccessRes(`${data.length} resume(s) saved successfully.`)
                                    })
                                    .catch((error) => {
                                        console.error("Failed to save to database:", error);
                                        toast.error("Failed to save resume file information");
                                        setErrorRes("Failed to save resume file information");
                                    });
                                }
                            }}
                            onUploadError={(error: Error) => {
                                toast.error(`ERROR! A problem was encountered. ${error.message}`);
                                setErrorRes(`ERROR! A problem was encountered. ${error.message}`);
                            }}
                            onUploadBegin={(name) => {
                                toast.info(`Uploading: ${name}`);
                            }}
                            onChange={(acceptedFiles) => {
                            }}
                            content={{
                                allowedContent({ ready, isUploading }) {
                                if (!ready) return "Checking what you allow";
                                if (isUploading) return "Uploading your resume...";
                                return (
                                    <>
                                    PDF or Word Document
                                    <br />
                                    (max size of 2MB per file)
                                    </>
                                );
                                },
                            }}
                        />
                    </div>
                    <div className="flex flex-col space-y-4 pt-10">
                    <ErrorMessage message={errorRes} />
                    <SuccessMessage message={successRes} />
                    </div>
                </div>
            </div>
      </div>
      <div className="flex flex-col w-full mt-10 p-4 border rounded-lg bg-slate-100 text-center">
        Your uploaded job file(s) or resume(s) will be processed by AI. This may take a little while, so please check back later in the job postings, matches list, or resume list.
      </div>
    </div>
  );
}
