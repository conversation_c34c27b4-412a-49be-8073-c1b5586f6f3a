// functions.ts (or a new utility file)

import { ResumeSections, ExperienceEntry, EducationEntry } from '@/utils/lmResume/refineResumeFlatTextToSections';
// Removed: import { ensureTiptapJSONString } from '@/data/zod/resumeTransformer'; // No longer needed here for LLM input

// This function adapts the ResumeSections output from refineResumeFlatTextToSections
// into a structure that transformParsedDataToResumeSchema can effectively process.
function adaptResumeSectionsToTransformerInput(
  sections: ResumeSections,
  // Assuming extractedContact is also available from prepareResumeForLLM
  contactInfo: { name?: string; email?: string; phone?: string; linkedin?: string; github?: string; website?: string; address?: string; shortAbout?: string }
): any { // This function returns the LLM's expected input format
  // Map other sections from ResumeSections to additional_sections (snake_case for LLM)
  const additionalSections = Object.entries(sections)
    .filter(([key]) => !['Summary', 'WorkExperience', 'Education', 'Skills', 'General'].includes(key))
    .map(([key, value]) => ({
      header: key,
      details: Array.isArray(value) ? value.map(String) : [String(value)], // LLM expects plain text or array of strings for details
      type: '' // You might need to infer or set a default type if your schema requires it
    }));

  return {
    // Use snake_case for top-level keys as expected by the LLM service
    standard_fields: { // Changed to snake_case
      header: {
        name: contactInfo.name || '',
        email: contactInfo.email || '',
        phone: contactInfo.phone ? [contactInfo.phone] : [], // Assuming phone might be an array in target schema
        linkedin: contactInfo.linkedin || '',
        github: contactInfo.github || '',
        website: contactInfo.website || '',
        address: contactInfo.address || '',
        // shortAbout is not in the LLM's example header, so omit it here
      },
      summary: {
        content: sections.Summary || '' // LLM expects plain string for summary content
      },
      experience: (sections.WorkExperience || []).map((exp: ExperienceEntry) => ({
        title: exp.title || '',
        company: exp.organization || '', // Mapping 'organization' to 'company'
        start_date: exp.startDate || '', // Changed to snake_case
        end_date: exp.endDate || '', // Changed to snake_case
        // description is now an array combining roleSummary and responsibilities
        description: [
          ...(exp.roleSummary ? [exp.roleSummary] : []),
          ...(exp.responsibilities || []),
        ],
        // highlights is now empty as its content moved to description
        highlights: [],
        // employmentType and location are not directly available from refineResumeFlatTextToSections
        // LLM might infer them or they remain empty.
        employment_type: '', // Changed to snake_case
        location: '',
        technologies: [], // Not extracted by refineResumeFlatTextToSections
      })),
      education: (sections.Education || []).map((edu: EducationEntry) => ({
        institution: edu.institution || '',
        degree: edu.degree || '', // Mapping 'program' to 'degree'
        start_date: edu.startDate || '', // Changed to snake_case
        end_date: edu.endDate || '', // Changed to snake_case
        // details should be an empty array for the LLM input, as the degree is already in 'degree' field
        details: [],
        // You might need to add logic here if there are other fields in EducationEntry that map to details
        location: '', // Not extracted by refineResumeFlatTextToSections
        gpa: null, // Changed to null as it's not extracted and null is more semantic
        honors: [], // Not extracted by refineResumeFlatTextToSections
      })),
      skills: sections.Skills || [], // Direct mapping for skills
    },
    additional_sections: additionalSections // Changed to snake_case
  };
}

export default adaptResumeSectionsToTransformerInput;