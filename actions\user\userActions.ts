"use server";

import { auth } from "@/lib/auth/auth";;
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { UserPlan } from "@/types/customTypes";
import { setUserPlanData } from "@/data/user/user";

export async function setUserPlan(plan: UserPlan) {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await setUserPlanData(plan);
    if (data) {
      return {
            data: data,
            success: "Successfully set user plan." 
        };
    } else {
      return {
            data: null,
            error: "Failed to set user plan." 
        };
    }
  } catch (error) {
      return {
            data: null,
            error: `Failed to set user plan. Error: ${error}` 
      };
  }
};
