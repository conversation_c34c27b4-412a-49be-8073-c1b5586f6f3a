"use server";

import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { inngest } from "@/lib/inngest/client";
import { prisma } from "@/lib/prisma/prismaClient";
import { UserFile } from "@/types/customTypes";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { v4 as uuidv4 } from "uuid";
import { sendInngestEvent } from "@/utils/sendInngestEvent";

export const saveFileData = async (userFile: UserFile) => {
  const session = await auth();

  if (!session?.user?.id) {
    return { error: "User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  let saveFileResult = null;

  try {
    saveFileResult = await prisma.userFile.create({
      data: {
        id: uuidv4(),
        userId: userFile.userId,
        companyId: userFile.companyId,
        fileUse: userFile.fileUse,
        fileType: userFile.fileType,
        fileName: userFile.fileName,
        fileDescription: userFile.description,
        fileSize: userFile.fileSize,
        key: userFile.key ? encryptToBuffer(userFile.key) : null,
        url: userFile.url ? encryptToBuffer(userFile.url) : null,
        parseCount: 1,
        parseStatus: "SUCCESS",
        parseResult: "Server processed resume file successfully.",
      },
    });

    if (saveFileResult) {
      userFile.id = saveFileResult.id;

      if (userFile.fileUse === "RESUME") {
        // console.log({RESUMEFILE: userFile});

        // Send to background processing
        await sendInngestEvent({
          name: "resume/extract-from-file",
          data: {
            userFile,
          },
        });
      } else if (userFile.fileUse === "JOB") {
        // Send to background processing
        await sendInngestEvent({
          name: "job/extract-from-file",
          data: {
            userFile,
          },
        });
      }

      return {
        success: "Success! File information was saved.",
        file: saveFileResult,
      };
    } else {
      return {
        error: "Error! Failed to save file.",
        file: saveFileResult,
      };
    }
  } catch (error) {
    return {
      error: `Error! Failed to save file: ${error}`,
      file: saveFileResult,
    };
  }
};

export const deleteFileData = async (id: string) => {
  try {
    const deletedFile = await prisma.userFile.delete({
      where: {
        id: id,
      },
    });

    if (deletedFile) {
      return {
        success: "Success! File was deleted.",
        file: deletedFile,
      };
    } else {
      return {
        error: "Error! Failed to delete file.",
        file: null,
      };
    }
  } catch (error) {
    return {
      error: `Error! Failed to delete file: ${error}`,
      file: null,
    };
  }
};

export const getFileData = async (id: string) => {
  try {
    const data = await prisma.userFile.findUnique({
      where: {
        id: id,
      },
    });

    if (data) {
      const file = {
        ...data,
        url: data.url ? safeDecrypt(data.url) : null,
      };

      return file;
    }

    return null;
  } catch (error) {
    return null;
  }
};
