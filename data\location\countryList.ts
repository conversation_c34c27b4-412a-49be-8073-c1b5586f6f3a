export function getFlagEmoji(location: string) {
    const cleanLocation = location.trim().toLocaleLowerCase();
  
    const country = countryList.find((country) =>
      cleanLocation.includes(country.name.toLocaleLowerCase())
    );
  
    return country?.flagEmoji || "";
}
  
  // Function to generate flag emoji from country code
  export function getFlagEmojiFromCode(countryCode: string) {
    if (!countryCode || countryCode.length !== 2) return ""; // Ensure it's a 2-letter ISO code
    return countryCode
      .toUpperCase()
      .split("")
      .map((char) => String.fromCodePoint(0x1f1e6 + char.charCodeAt(0) - 65))
      .join("");
  }

export const countryList = [
  {
    name: "Australia",
    code: "AU",
    phoneCode: "+61",
    flagEmoji: "🇦🇺",
  },
  {
    name: "Cambodia",
    code: "KH",
    phoneCode: "+855",
    flagEmoji: "🇰🇭",
  },
  {
    name: "China",
    code: "CN",
    phoneCode: "+86",
    flagEmoji: "🇨🇳",
  },
  {
    name: "India",
    code: "IN",
    phoneCode: "+91",
    flagEmoji: "🇮🇳",
  },
  {
    name: "Indonesia",
    code: "ID",
    phoneCode: "+62",
    flagEmoji: "🇮🇩",
  },
  {
    name: "Japan",
    code: "JP",
    phoneCode: "+81",
    flagEmoji: "🇯🇵",
  },
  {
    name: "South Korea",
    code: "KR",
    phoneCode: "+82",
    flagEmoji: "🇰🇷",
  },
  {
    name: "Laos",
    code: "LA",
    phoneCode: "+856",
    flagEmoji: "🇱🇦",
  },
  {
    name: "Malaysia",
    code: "MY",
    phoneCode: "+60",
    flagEmoji: "🇲🇾",
  },
  {
    name: "Myanmar",
    code: "MM",
    phoneCode: "+95",
    flagEmoji: "🇲🇲",
  },
  {
    name: "New Zealand",
    code: "NZ",
    phoneCode: "+64",
    flagEmoji: "🇳🇿",
  },
  {
    name: "Philippines",
    code: "PH",
    phoneCode: "+63",
    flagEmoji: "🇵🇭",
  },
  {
    name: "Qatar",
    code: "QA",
    phoneCode: "+974",
    flagEmoji: "🇶🇦",
  },
  {
    name: "Singapore",
    code: "SG",
    phoneCode: "+65",
    flagEmoji: "🇸🇬",
  },
  {
    name: "Taiwan",
    code: "TW",
    phoneCode: "+886",
    flagEmoji: "🇹🇼",
  },
  {
    name: "Thailand",
    code: "TH",
    phoneCode: "+66",
    flagEmoji: "🇹🇭",
  },
  {
    name: "Timor-Leste",
    code: "TL",
    phoneCode: "+670",
    flagEmoji: "🇹🇱",
  },
  {
    name: "United Arab Emirates",
    code: "AE",
    phoneCode: "+971",
    flagEmoji: "🇦🇪",
  },
  {
    name: "United Kingdom",
    code: "GB",
    phoneCode: "+44",
    flagEmoji: "🇬🇧",
  },
  {
    name: "United States",
    code: "US",
    phoneCode: "+1",
    flagEmoji: "🇺🇸",
  },
  {
    name: "Vietnam",
    code: "VN",
    phoneCode: "+84",
    flagEmoji: "🇻🇳",
  },
];
