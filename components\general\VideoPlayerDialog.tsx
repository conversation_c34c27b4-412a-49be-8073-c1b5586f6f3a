"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogOverlay,
  DialogPortal,
} from "@/components/ui/dialog"; // DialogContent will not be used directly for the box
import { DialogClose } from "@radix-ui/react-dialog"; // Using <PERSON><PERSON><PERSON>'s DialogClose for the button
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface VideoPlayerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string;
  title?: string;
}

export function VideoPlayerDialog({
  isOpen,
  onClose,
  videoId,
  title = "YouTube video player",
}: VideoPlayerDialogProps) {
  if (!isOpen) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/70 z-[9999]" /> {/* Overlay */}
        {/* Centering wrapper, similar to JobResumeNotesDialog.tsx */}
        <div
          className="fixed inset-0 z-[10000] flex items-center justify-center p-4"
          onClick={(e) => {
            // If the click is directly on this backdrop, and not on the video player div or its children, close.
            if (e.target === e.currentTarget) {
              onClose();
            }
          }}
        >
          {/* Video container - this is the "dialog box" */}
          <div
            className="bg-black p-1 sm:p-2 rounded-lg shadow-xl relative w-[95vw] max-w-[1400px] aspect-video max-h-[85vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()} // Prevent clicks inside the video box from bubbling to the backdrop
          >
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-3 -right-3 md:-top-4 md:-right-4 text-white bg-black/50 hover:bg-black/70 rounded-full h-8 w-8 z-[10001] cursor-pointer"
                title="Close video"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button>
            </DialogClose>
            <iframe
              className="w-full h-full rounded"
              src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
              title={title}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            >
            </iframe>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
