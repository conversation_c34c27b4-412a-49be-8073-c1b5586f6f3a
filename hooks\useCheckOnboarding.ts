import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { checkIfUserHasOnboarded } from "@/actions/user/getUser";

export function useCheckOnboarding() {
  const { data: session, status, update } = useSession();

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (status === "authenticated" && !session.user.onboarded) {
        // Get fresh data from database
        const user = await checkIfUserHasOnboarded(session?.user?.id as string);
        
        if (user?.onboarded) {
          // Update session if onboarding was completed
          await update({
            ...session,
            user: {
              ...session.user,
              onboarded: user.onboarded
            }
          });
        }
      }
    };

    checkOnboardingStatus();
  }, [status, session, update]);

  return {
    onboarded: session?.user?.onboarded || false,
    loading: status === "loading"
  };
}