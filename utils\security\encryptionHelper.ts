import { createCipheriv, createDecipheriv, randomBytes, scryptSync } from 'crypto';

const algorithm = 'aes-256-gcm';

const ENCRYPTION_PASSWORD: string = process.env.ENCRYPTION_PASSWORD!;
if (!ENCRYPTION_PASSWORD) {
  console.log('ENCRYPTION_PASSWORD on load:', process.env.ENCRYPTION_PASSWORD);
    console.log('All env keys:', Object.keys(process.env)); 
}

const saltLength = 16;
const ivLength = 12;
const authTagLength = 16;

/**
 * Encrypts a string and returns a Buffer containing:
 * [salt | iv | ciphertext | authTag]
 */
export function encryptToBuffer(text: string): Buffer {
  const salt = randomBytes(saltLength); // ✅ new salt per encryption
  const iv = randomBytes(ivLength);     // ✅ new IV per encryption
  const key = scryptSync(ENCRYPTION_PASSWORD, salt, 32);

  const cipher = createCipheriv(algorithm, key, iv);
  const ciphertext = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
  const authTag = cipher.getAuthTag();

  return Buffer.concat([salt, iv, ciphertext, authTag]);
};

/**
 * Decrypts a buffer containing: [salt | iv | ciphertext | authTag]
 */
export function decryptFromBuffer(encryptedData: Buffer): string {
  try {
    if (!Buffer.isBuffer(encryptedData) || encryptedData.length < (saltLength + ivLength + authTagLength)) {
      return "";
    }

    const salt = encryptedData.subarray(0, saltLength);
    const iv = encryptedData.subarray(saltLength, saltLength + ivLength);
    const authTag = encryptedData.subarray(-authTagLength);
    const ciphertext = encryptedData.subarray(saltLength + ivLength, -authTagLength);
    const key = scryptSync(ENCRYPTION_PASSWORD, salt, 32);

    const decipher = createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);
    return Buffer.concat([decipher.update(ciphertext), decipher.final()]).toString('utf8');
  } catch (err) {
    console.error("Decryption failed:", err);
    return "";
  }
};