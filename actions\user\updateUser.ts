"use server";

import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { prisma } from "@/lib/prisma/prismaClient";
import arcjet, { shield, detectBot } from "@/lib/arcjet/arcjet";
import { request } from "@arcjet/next";
import { auth } from "@/lib/auth/auth";
import { redirect } from "next/navigation";
import { UserJobSeekerSchema } from "@/data/zod/zodSchema";
import { z } from "zod";
import { UpdateJobSeekerProfileData } from "@/data/user/user";

const aj = arcjet
  .withRule(
    shield({
      mode: "DRY_RUN",
    })
  )
  .withRule(
    detectBot({
      mode: "DRY_RUN",
      allow: [],
    })
  );

export const UpdateUser = async (user: any) => {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  const req = await request();
  const decision = await aj.protect(req);
  if (decision.isDenied()) {
    throw new Error("Forbidden");
  }

  if (!user) {
    return { error: "Missing user info!" };
  }

  try {
    await prisma.user.update({
      where: {
        id: user.userId,
      },
      data: {
        firstName: encryptToBuffer(user.firstName),
        lastName: encryptToBuffer(user.lastName),
        email: encryptToBuffer(user.email),
        gender: user.gender,
      },
    });
  } catch (error) {
    return { error: "Error encountered updating profile." };
  }

  return { success: "Success! Profile has been updated." };
};

export async function UpdateJobSeekerProfile(
  data: z.infer<typeof UserJobSeekerSchema>
) {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  const req = await request();
  const decision = await aj.protect(req);
  if (decision.isDenied()) {
    throw new Error("Forbidden");
  }

  try {
    const validateData = UserJobSeekerSchema.parse(data);
    console.log({validateUserDataUpdate: validateData})

    validateData.userId = session?.user?.id;

    if (validateData) {
      const jobSeeker = await UpdateJobSeekerProfileData(validateData);

      if (jobSeeker) {
        return {
          success: "Success! Your information was saved.",
        };
      } else {
        return {
          error: "Error! Failed to save user information.",
        };
      }
    }
  } catch (error) {
        console.log(`Error was encountered updating user, please try again. ${error}`);
    if (error instanceof z.ZodError) {
              console.log(error)
    }
    return {
      error: `Error was encountered updating user, please try again. ${error}`,
    };
  }
}
