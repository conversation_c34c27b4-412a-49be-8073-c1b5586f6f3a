"use client";

import { XIcon } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Label } from "../ui/label";
import { Checkbox } from "../ui/checkbox";
import { Separator } from "../ui/separator";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { countryList } from "@/data/location/countryList";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";

const jobTypes = ["Full Time", "Part Time", "Contract", "Internship"];

export function JobFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentJobTypes = searchParams.get("jobTypes")?.split(",") || [];
  const currentLocation = searchParams.get("location") || "";

  function clearAllFilters() {
    router.push("/");
  }

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());

      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }

      return params.toString();
    },
    [searchParams]
  );

  function handleJobTypeChange(jobType: string, checked: boolean) {
    const current = new Set(currentJobTypes);

    if (checked) {
      current.add(jobType);
    } else {
      current.delete(jobType);
    }

    const newValue = Array.from(current).join(",");
    router.push(`?${createQueryString("jobTypes", newValue)}`);
  }

  function handleLocationChange(location: string) {
    router.push(`?${createQueryString("location", location)}`);
  }

  return (
    <Card className="col-span-1">
      <CardHeader className="flex justify-between">
        <CardTitle className="text-2xl font-semibold">Filters</CardTitle>
        <Button
          variant="destructive"
          size="sm"
          className="h-8"
          onClick={clearAllFilters}
        >
          <span>Clear All</span>
          <XIcon className="size-4" />
        </Button>
      </CardHeader>

      <Separator />

      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Label className="text-lg font-semibold">Job Type</Label>

          <div className="grid grid-cols-2 gap-4">
            {jobTypes.map((type, index) => {
              const value = type.toLowerCase().replace(" ", "-");
              return (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox
                    id={value}
                    value={value}
                    checked={currentJobTypes.includes(value)}
                    onCheckedChange={(checked) =>
                      handleJobTypeChange(value, checked as boolean)
                    }
                  />
                  <Label className="text-sm font-medium" htmlFor={value}>
                    {type}
                  </Label>
                </div>
              );
            })}
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <Label className="text-lg font-semibold">Location</Label>

          <Select
            value={currentLocation}
            onValueChange={(location) => {
              handleLocationChange(location);
            }}
          >
            <SelectTrigger className="w-full cursor-pointer">
              <SelectValue placeholder="Select Location" />
            </SelectTrigger>
            <SelectContent className="p-4">
              <SelectGroup className="cursor-pointer">
                <SelectLabel>Worldwide</SelectLabel>
                <SelectItem value="worldwide">
                  <span>🌎</span>
                  <span className="pl-2">Worldwide / Remote</span>
                </SelectItem>
              </SelectGroup>
              <SelectGroup className="cursor-pointer">
                <SelectLabel>Location</SelectLabel>
                {countryList.map((country) => (
                  <SelectItem key={country.code} value={country.name}>
                    <span className="emoji">{country.flagEmoji}</span>
                    <span className="p-2">{country.name}</span>
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
