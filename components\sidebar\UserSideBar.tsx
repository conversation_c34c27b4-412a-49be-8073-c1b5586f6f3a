"use client";

import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { SidebarRoutes } from "./SidebarRoutes";
import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { cn } from "@/lib/utils";
import { useCompanyActions } from "@/hooks/useCompanyActions";

export const Sidebar = ({
  collapsed,
  setCollapsed,
}: {
  collapsed: boolean;
  setCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { data: session, status } = useSession();

  const { company, isLoading, isError } = useCompanyActions(session?.user?.companyId as string);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [setCollapsed]);

  return (
    <div
      className={`h-screen ${collapsed ? "w-16" : "w-56"} bg-athena-gradient text-white transition-all duration-300 flex flex-col justify-between fixed z-50`}
    >
      {/* Top: Logo + Collapse Button */}
      <div className="flex items-center justify-between pl-4 pt-1">
        <div className="flex items-center justify-center gap-1">
          <div className="text-xl font-bold">🤖</div>
        </div>
        <div className="pt-1">
          <Button
            size="icon"
            variant="ghost"
            onClick={() => setCollapsed(!collapsed)}
            className="ml-auto cursor-pointer text-gray-500 hover:text-gray-600"
          >
            <Menu />
          </Button>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center p-2">
        <Image src="/logo/logo.png" height="100" width="100" alt="logo" />
        {!collapsed && (
          <span className="text-xs pb-10 text-[#858686] transition-all duration-300 ease-in-out ${collapsed ? 'opacity-0' : 'opacity-100'}`">
          </span>
        )}
      </div>

      {/* User Welcome */}
      {!collapsed && (
        <div className="flex flex-col text-sm text-muted-foreground items-center justify-center mb-4">
          Welcome
          <span className="text-md text-[#6e6f70] font-semibold transition-all duration-300 ease-in-out ${collapsed ? 'opacity-0' : 'opacity-100'}`">
            {session?.user?.name}
          </span>
          {session?.user?.userType === "COMPANY" && (
            <div className="flex flex-col items-center justify-center text-center mt-2 mb-4">
                <span className="text-xs text-muted-foreground transition-all duration-300 ease-in-out w-full truncate px-2 ${collapsed ? 'opacity-0' : 'opacity-100'}`">
                {company?.name}
                </span>
                {company?.logo && !collapsed && (
                  <div className="mt-2">
                    <Image src={company.logo} height="40" width="40" alt="Company Logo" className="rounded-md" />
                  </div>
                )}
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className={cn("flex-1 mt-4 space-y-2", collapsed ? "px-2" : "px-6")}>
        {session?.user?.onboarded && ((
            <SidebarRoutes collapsed={collapsed} />
        ))}
      </nav>

      {/* User Menu at Bottom */}
      <div className="mb-4 px-2">
        {/* <UserMenu collapsed={collapsed} /> */}
      </div>
    </div>
  );
};
