"use server";

import { prisma } from "@/lib/prisma/prismaClient"; // Ensure this path is correct

async function updateCachedRate(
    baseCode: string,
    targetCode: string,
    rate: number,
    symbol: string,
    localeStr: string
) {
  try {
    const upsertedRate = await prisma.cachedExchangeRate.upsert({
      where: {
        // This is how Prisma finds an existing record to update
        unique_currency_pair: { // The name you gave to your @@unique constraint
          baseCurrencyCode: baseCode,
          targetCurrencyCode: targetCode,
        },
      },
      update: {
        // Fields to update if the record exists
        exchangeRate: rate,
        // lastFetchedAt is automatically updated by <PERSON><PERSON><PERSON> due to @updatedAt
      },
      create: {
        // Fields to use if a new record needs to be created
        baseCurrencyCode: baseCode,
        targetCurrencyCode: targetCode,
        exchangeRate: rate,
        currencySymbol: symbol,
        locale: localeStr,
      },
    });
    console.log('Successfully upserted exchange rate:', upsertedRate);
    return upsertedRate;
  } catch (error) {
    console.error('Error upserting exchange rate:', error);
    throw error; // Or handle it as appropriate for your application
  }
}

/**
 * Fetches all exchange rates from the external API against a base currency (USD)
 * and updates the CachedExchangeRate table in the database.
 * This function is intended to be run periodically (e.g., daily by a scheduled job).
 */
export async function updateAllExchangeRatesFromAPI() {
  const apiKey = process.env.EXCHANGE_RATE_API_KEY;
  const baseCurrencyCodeForAPI = "USD"; // We always fetch against USD from the API

  if (!apiKey) {
    console.error("DAILY UPDATE: EXCHANGE_RATE_API_KEY not found.");
    return { success: false, message: "EXCHANGE_RATE_API_KEY not configured." };
  }

  try {
    console.log(`DAILY UPDATE: Fetching all live rates against ${baseCurrencyCodeForAPI} from API.`);
    const response = await fetch(`https://v6.exchangerate-api.com/v6/${apiKey}/latest/${baseCurrencyCodeForAPI}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = `API request failed: ${response.status} ${errorData['error-type'] || response.statusText}`;
      console.error("DAILY UPDATE: ", errorMessage, { status: response.status, errorData });
      return { success: false, message: errorMessage };
    }

    const data = await response.json();
    if (data.result === "error") {
      const errorMessage = `ExchangeRate-API error: ${data['error-type']}`;
      console.error("DAILY UPDATE: ", errorMessage, { errorType: data['error-type'] });
      return { success: false, message: errorMessage };
    }

    const allRates = data.conversion_rates;
    if (!allRates || typeof allRates !== 'object') {
      const errorMessage = 'Invalid or missing conversion_rates in API response.';
      console.error("DAILY UPDATE: ", errorMessage);
      return { success: false, message: errorMessage };
    }

    console.log(`DAILY UPDATE: Received ${Object.keys(allRates).length} rates. Caching to DB...`);
    let successfulUpserts = 0;
    let failedUpserts = 0;

    for (const [targetCode, exRate] of Object.entries(allRates)) {
      if (typeof exRate !== 'number') {
        console.warn(`DAILY UPDATE: Skipping invalid rate for ${targetCode}:`, exRate);
        failedUpserts++;
        continue;
      }

      // CRITICAL: Determine symbol and locale for each currency code.
      // This mapping needs to be comprehensive for accurate display.
      let currentSymbol = "$"; // Default symbol
      let currentLocale = "en-US"; // Default locale

      // Example Mappings - EXPAND THIS SIGNIFICANTLY
      if (targetCode === "PHP") { currentSymbol = "₱"; currentLocale = "en-PH"; }
      else if (targetCode === "EUR") { currentSymbol = "€"; currentLocale = "de-DE"; }
      else if (targetCode === "GBP") { currentSymbol = "£"; currentLocale = "en-GB"; }
      else if (targetCode === "JPY") { currentSymbol = "¥"; currentLocale = "ja-JP"; }
      else if (targetCode === "AUD") { currentSymbol = "A$"; currentLocale = "en-AU"; }
      else if (targetCode === "CAD") { currentSymbol = "CA$"; currentLocale = "en-CA"; }
      // ... Add many more mappings for currencies you expect to display correctly ...

      try {
        await updateCachedRate(baseCurrencyCodeForAPI, targetCode, exRate, currentSymbol, currentLocale);
        successfulUpserts++;
      } catch (error) {
        console.error(`DAILY UPDATE: Failed to upsert rate for ${targetCode}:`, error);
        failedUpserts++;
      }
    }

    const summaryMessage = `Daily update complete. Successful upserts: ${successfulUpserts}, Failed/Skipped upserts: ${failedUpserts}.`;
    console.log("DAILY UPDATE: ", summaryMessage);
    return { success: true, message: summaryMessage, successfulUpserts, failedUpserts };

  } catch (error) {
    const errorMessage = `Failed to update all exchange rates: ${error instanceof Error ? error.message : String(error)}`;
    console.error("DAILY UPDATE: ", errorMessage, error);
    return { success: false, message: errorMessage };
  }
}
