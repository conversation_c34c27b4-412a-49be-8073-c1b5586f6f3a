import { EmptyState } from "@/components/general/EmptyState";
import { auth } from "@/lib/auth/auth";
import { GetCompanyFiles } from "@/actions/company/getCompany";
import { CompanyResumeList } from "@/components/company/CompanyResumes";
import { UploadCompanyResume } from "@/components/company/UploadCompanyResume";
import { CompanyFilesList } from "@/components/company/CompanyFiles";

export default async function CompanyFilesPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>;
}) {
  const session = await auth();
  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50;

  const resumeListResponse = await GetCompanyFiles(session?.user?.companyId as string
);
  const data = resumeListResponse || [];

  // Pagination logic
  const totalPages = Math.ceil(data.length / pageSize);
  const paginatedResumes = data.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <>
      {data?.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
            <div className="flex items-center justify-between gap-2 pb-4">
                <h1 className="text-2xl font-semibold">Uploaded Files</h1>
                <UploadCompanyResume companyId={session?.user?.companyId as string} />
            </div>
          <EmptyState
            title="No files found"
            description="You don't have files uploaded yet."
            buttonText="Upload Resumes"
            href="#"
          />
          <em>Wait for AI to finish processing your uploaded files. All files are added automatically to either job or resume list.</em>
        </div>
      ) : (
        <div className="space-y-6">
          <CompanyFilesList
            paginatedResumes={paginatedResumes}
            currentPage={currentPage}
            totalPages={totalPages}
          />
        </div>
      )}
    </>
  );
}
