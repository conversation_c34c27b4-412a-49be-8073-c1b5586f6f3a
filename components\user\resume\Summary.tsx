import { ResumeDataSchemaType } from '@/data/zod/resumeZod';
import { Section } from '@/components/ui/section';
import { JsonToHTML } from '@/components/general/JsonToHTML';
// import { normalizeParagraph } from '@/utils/stringHelpters'; // No longer needed if using JsonToHTML

interface AboutProps {
  summary: ResumeDataSchemaType['standardFields']['summary']['content'];
  className?: string;
}

/**
 * Summary section component
 * Displays a summary of professional experience and goals
 */
export function Summary({ summary, className }: AboutProps) {
  let summaryContent: React.ReactNode = null;

  if (summary && typeof summary === 'string' && summary.trim() !== "") {
    try {
      const parsedSummary = JSON.parse(summary); // `summary` is now always a Tiptap JSON string
      summaryContent = <JsonToHTML json={parsedSummary} />;
    } catch (e) {
      // This fallback should ideally not be hit if ensureTiptapJSONString works correctly,
      // but it's good to keep for robustness.
      console.warn("Summary content could not be parsed as JSON, rendering as plain text:", summary, e);
      summaryContent = <div className="text-pretty font-mono text-sm text-design-resume print:text-[12px] whitespace-pre-wrap">{summary}</div>;
    }
  }

  return (
    <Section className={className}>
      <h2 className="text-xl font-bold" id="about-section">
        Summary
      </h2>
      {summaryContent && (
        <div
          className="text-pretty font-mono text-sm text-design-resume print:text-[12px]"
          aria-labelledby="about-section"
        >
          {summaryContent}
        </div>
      )}
    </Section>
  );
}
