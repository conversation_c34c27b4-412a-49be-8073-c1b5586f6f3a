"use client";

import * as React from "react";
import { useFieldArray, type Control } from "react-hook-form";
import {
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Trash2, X } from "lucide-react";
import { z } from "zod";
import { JobSchema } from "@/data/zod/zodSchema";
import { Badge } from "@/components/ui/badge";
import { XCircleIcon } from "lucide-react";

const languageOptions = [
  { value: "TAGALOG", label: "Tagalog" },
  { value: "CEBUANO", label: "Cebuano" },
  { value: "JAPANESE", label: "Japanese" },
  { value: "ENGLISH", label: "English" },
  { value: "MANDARIN", label: "Mandarin" },
  { value: "KOREAN", label: "Korean" },
  { value: "VIETNAMESE", label: "Vietnamese" },
] as const;

const levelOptions = [
  { value: "NATIVE", label: "Native" },
  { value: "FLUENT", label: "Fluent" },
  { value: "BUSINESS", label: "Business" },
  { value: "CONVERSATIONAL", label: "Conversational" },
] as const;

interface LanguageRequirementsSectionProps {
  control: Control<z.infer<typeof JobSchema>>;
}

export function LanguageRequirementsSection({
  control,
}: LanguageRequirementsSectionProps) {
  const { fields, append, remove } = useFieldArray({
    control,
    name: "languageRequirements",
  });
  const [open, setOpen] = React.useState(false);
  const [tempLanguage, setTempLanguage] = React.useState({
    language: "",
    level: "",
    certification: "",
    type: "REQUIRED" as const,
  });

  // Group languages by language name
  const groupedLanguages = React.useMemo(() => {
    return fields.reduce(
      (acc, field) => {
        if (!acc[field.language]) {
          acc[field.language] = [];
        }
        acc[field.language].push({
          level: field.level,
          certification: field.certification || "",
        });
        return acc;
      },
      {} as Record<string, Array<{ level: string; certification: string }>>
    );
  }, [fields]);

  const handleAdd = () => {
    append(
      tempLanguage as {
        language:
          | "TAGALOG"
          | "CEBUANO"
          | "JAPANESE"
          | "ENGLISH"
          | "MANDARIN"
          | "KOREAN"
          | "VIETNAMESE";
        level: "NATIVE" | "FLUENT" | "BUSINESS" | "CONVERSATIONAL";
        certification?: string;
        type: "REQUIRED" | "PREFERRED";
      }
    );
    setTempLanguage({
      language: "",
      level: "",
      certification: "",
      type: "REQUIRED",
    });
    setOpen(false);
  };

  const handleRemoveLanguageRequirement = (language: string, level: string) => {
    const index = fields.findIndex(
      (field) => field.language === language && field.level === level
    );
    if (index !== -1) {
      remove(index);
    }
  };

  return (
    <div className="flex flex-col w-full">
      <div className="flex justify-between items-center">
        <span className="text-sm font-bold">Language Requirements</span>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="cursor-pointer">Add Language</Button>
          </DialogTrigger>
          <DialogPortal>
            <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
            <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
                <div className="bg-white rounded-lg shadow-lg w-full max-w-[300px] max-h-[90vh] overflow-y-auto">
                    <div className="flex items-center justify-between pt-6 px-6">
                        <DialogTitle className="text-lg font-semibold">
                            Select Language
                        </DialogTitle>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 cursor-pointer"
                            onClick={() => setOpen(false)}
                        >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Close</span>
                        </Button>
                    </div>
                    <div className="p-6">
                        <div className="p-2">
                            <FormItem>
                                <FormLabel>Language</FormLabel>
                                <Select
                                    value={tempLanguage.language}
                                    onValueChange={(value) =>
                                        setTempLanguage({ ...tempLanguage, language: value })
                                    }
                                >
                                    <SelectTrigger className="cursor-pointer w-full">
                                        <SelectValue placeholder="Select language" />
                                    </SelectTrigger>
                                    <SelectContent className="popper z-[12000]">
                                        {languageOptions.map((option) => (
                                            <SelectItem className="cursor-pointer" key={option.value} value={option.value}>
                                            {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        </div>
                                    
                        <div className="p-2">
                            <FormItem>
                                <FormLabel>Level</FormLabel>
                                <Select
                                value={tempLanguage.level}
                                onValueChange={(value) =>
                                    setTempLanguage({ ...tempLanguage, level: value })
                                }
                                >
                                <SelectTrigger className="cursor-pointer w-full">
                                    <SelectValue placeholder="Select level" />
                                </SelectTrigger>
                                <SelectContent>
                                    {levelOptions.map((option) => (
                                        <SelectItem className="cursor-pointer" key={option.value} value={option.value}>
                                        {option.label}
                                        </SelectItem>
                                    ))}
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        </div>
                        <div className="p-2">
                            <FormItem>
                                <FormLabel>Certification (Optional)</FormLabel>
                                <Input
                                placeholder="e.g. JLPT N1"
                                value={tempLanguage.certification}
                                onChange={(e) =>
                                    setTempLanguage({
                                    ...tempLanguage,
                                    certification: e.target.value,
                                    })
                                }
                                />
                            </FormItem>
                        </div>
                        <div className="p-2">
                            <Button
                                type="button"
                                onClick={handleAdd}
                                disabled={!tempLanguage.language || !tempLanguage.level}
                                className="cursor-pointer"
                            >
                                Add Language
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
          </DialogPortal>
        </Dialog>
      </div>

      {Object.keys(groupedLanguages).length > 0 && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Language</TableHead>
              <TableHead>Requirements</TableHead>
              <TableHead className="w-[100px]">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(groupedLanguages).map(
              ([language, requirements]) => (
                <TableRow key={language}>
                  <TableCell>
                    {
                      languageOptions.find((opt) => opt.value === language)
                        ?.label
                    }
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {requirements.map((req, idx) => (
                        <Badge
                          key={`${language}-${req.level}-${idx}`}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {
                            levelOptions.find((opt) => opt.value === req.level)
                              ?.label
                          }
                          {req.certification && ` (${req.certification})`}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 hover:bg-transparent cursor-pointer"
                            onClick={() =>
                              handleRemoveLanguageRequirement(
                                language,
                                req.level
                              )
                            }
                          >
                            <XCircleIcon className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      className="cursor-pointer"
                      size="sm"
                      onClick={() => {
                        requirements.forEach(() => {
                          const index = fields.findIndex(
                            (field) => field.language === language
                          );
                          if (index !== -1) {
                            remove(index);
                          }
                        });
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        </Table>
      )}
    </div>
  );
}

