import { inngest } from "@/lib/inngest/client";
import { Resend } from "resend";
import { prisma } from "@/lib/prisma/prismaClient";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { v4 as uuidv4 } from "uuid";
import { JobDataSchema, ResumeDataSchema } from "@/data/zod/resumeZod";
import { transformParsedDataToResumeSchema } from "@/data/zod/resumeTransformer";
import { transformParsedDataToJobSchema } from "@/data/zod/jobTransformer";
import {
  GetAllResumeRawText,
  GetUserRawResume,
  GetUserResumeFile,
} from "@/actions/resume/userResumeActions";
import {
  convertUndefinedToNull,
  normalizeHeaders,
  normalizeParagraph,
  safeField,
} from "@/utils/stringHelpters";
import { GetAllJobsRawText } from "@/actions/job/getJob";
import { updateAllExchangeRatesFromAPI } from "@/actions/utils/currencyAction";
import { getRawResumesByIds } from "@/data/user/resume";
import {
  handleFileExtraction,
  getResumeHeaderChunk,
} from "@/utils/llm/common/fileDataExtraction";
import { prepareResumeForLLM } from "@/utils/lmResume/prepareResumeForLLM";
import { CreateResume } from "@/actions/resume/userResumeActions";
import {
  CandidateResume,
  UserFile,
  JobDescPost,
} from "@/types/customTypes";
import { PublishStatus } from "@prisma/client";
import adaptResumeSectionsToTransformerInput from "@/utils/llm/common/adaptResumeSectionsToTransformerInput";
import { prepareJobForLLM } from "@/utils/lmJob/prepareJobForLLM"; // Keep this import
import { adaptJobSectionsToJobPost } from "@/utils/lmJob/adaptJobSectionsToJobPost"; // New import
import { CreateJobFromFile } from "@/actions/job/createJob";
import { sendInngestEvent } from "@/utils/sendInngestEvent";

const resend = new Resend(process.env.RESEND_API_KEY);
const AI_API_URL = process.env.AI_API_URL || "http://localhost:8000";
const COMPREHENSIVE_PERIODIC_MATCHING_FLAG =
  "comprehensive_periodic_matching_in_progress";

export const helloWorld = inngest.createFunction(
  { id: "hello-world" },
  { event: "app/hello.world" },
  async ({ event }) => {
    console.log("Hello World function received event:", event);
    return { message: "Hello world!" };
  }
);

export const handleJobExpiration = inngest.createFunction(
  {
    id: "job-expiration",
    cancelOn: [
      {
        event: "job/cancel.expiration",
        if: "async.data.jobId == event.data.jobId",
      },
    ],
  },
  { event: "job/created" },

  async ({ event, step }) => {
    const { jobId, expirationDays } = event.data;

    await step.sleep("wait-for-expiration", `${expirationDays}d`);

    await step.run("update-job-status", async () => {
      await prisma?.jobPost.update({
        where: {
          id: jobId,
        },
        data: {
          status: "EXPIRED",
        },
      });
    });

    return {
      message: `Job ${jobId} has expired after ${expirationDays} days.`,
    };
  }
);

export const emailPeriodicJobListings = inngest.createFunction(
  { id: "email-job-listings" },
  { event: "jobseeker/created" },

  async ({ event, step }) => {
    const { userId } = event.data;

    const totalDays = 30;
    const intervalDays = 2;
    let currentDay = 0;

    while (currentDay < totalDays) {
      await step.sleep("wait-interval", `${intervalDays}d`);
      currentDay += intervalDays;

      const recentJobs = await step.run("fetch-recent-jobs", async () => {
        return await prisma?.jobPost.findMany({
          where: {
            status: "ACTIVE",
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
          include: {
            company: {
              select: {
                name: true,
              },
            },
          },
        });
      });

      if (recentJobs && recentJobs.length > 0) {
        await step.run("send-email", async () => {
          const jobListingsHtml = recentJobs
            .map(
              (job: any) =>
                `
              <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #eee; border-radius: 5px;">
                <h3 style="margin: 0;">${job.jobTitle}</h3>
                <p style="margin: 5px 0;">${job.company.name} • ${
                  job.location
                }</p>
                <p style="margin: 5px 0;">$${job.salaryFrom?.toLocaleString()} - $${job.salaryTo?.toLocaleString()}</p>
              </div>
            `
            )
            .join("");

          await resend.emails.send({
            from: "Edison AIX Jobs <<EMAIL>>",
            to: ["<EMAIL>"],
            subject: "Latest Job Opportunities for you",
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Latest Job Opportunities</h2>
                  ${jobListingsHtml}
                  <div style="margin-top: 30px; text-align: center;">
                    <a href="${process.env.NEXT_PUBLIC_APP_URL}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View More Jobs</a>
                  </div>
                </div>
              `,
          });
        });
      }
    }

    return { userId, message: "Completed 30 day job listing notications" };
  }
);

export const sendResumeToAI = inngest.createFunction(
  { id: "process-resume-ai" },
  { event: "resume/ai.process" },
  async ({ event, step }) => {
    const { userFile } = event.data;
    let rawContent = "";

    const resumeObject = await step.run("send-resume-to-ai", async () => {
      // Send file to AI for parsing
      try {
        console.log({ sendResumeToAI: userFile.id });

        await prisma.systemFlag.upsert({
          where: { name: "resume_file_parsing_in_progress" },
          update: { value: "true" },
          create: { name: "resume_file_parsing_in_progress", value: "true" },
        });

        const response = await fetch(`${AI_API_URL}/parse/resume`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            url: userFile.url,
            companyId: userFile.companyId,
            fileId: userFile.id,
            filename: userFile.fileName,
            userId: userFile.userId,
          }),
        });

        const result = await response.json();

        // Store parsing results in database
        if (result && result.status === "success" && result.error === null) {
          await prisma.userFile.update({
            where: { id: userFile.id },
            data: {
              parseStatus: "SUCCESS",
              parseResult: "AI processed resume file successfully.",
              parseCount: { increment: 1 },
            },
          });

          await prisma.systemFlag.update({
            where: { name: "resume_file_parsing_in_progress" },
            data: { value: "false" },
          });
        } else {
          await prisma.userFile.update({
            where: { id: userFile.id },
            data: {
              parseStatus: "FAILED",
              parseResult: JSON.stringify(result.detail),
              parseCount: { increment: 1 },
            },
          });
        }
      } catch (error) {
        console.error(`Error parsing file ${userFile.id}:`, error);
      }
    });

    return {
      success: true,
      userId: userFile.userId,
      message: "PDF processing completed successfully",
    };
  }
);

export const resumeTempPeriodicParse = inngest.createFunction(
  { id: "resume-temp-periodic-parsing" },
  { event: "resume/temp-available" },

  async ({ event, step }) => {
    const resumes = await prisma.userResumeTemp.findMany({});

    for (const resume of resumes) {
      await step.run(`process-resume-${resume.id}`, async () => {
        let encryptedResumeData = null;
        let resumeName = "";
        let resumeUsername = "";
        let resumeEmail = "";
        let resumePhone = "";
        let resumeWebsite = "";
        let resumeXaccount = "";
        let resumeLinkedin = "";
        let resumeGithub = "";
        let resumeAddress = "";
        let resumeAbout = "";

        try {
          const parsedResumeData = JSON.parse(resume.resumeData);
          const transformedData =
            transformParsedDataToResumeSchema(parsedResumeData);
          const validated = ResumeDataSchema.safeParse(transformedData);

          if (!validated.success) {
            console.error("Validation failed:", validated.error);
            return;
          }
          const header = validated.data.standardFields.header;

          resumeUsername = safeField(
            generateUsername(normalizeHeaders(header.name))
          );
          resumeName = safeField(normalizeHeaders(header.name));
          resumeEmail = safeField(header.email);
          resumePhone = safeField(header.phone);
          resumeWebsite = safeField(header.website);
          resumeXaccount = safeField(header.xaccount);
          resumeLinkedin = safeField(header.linkedin);
          resumeGithub = safeField(header.github);
          resumeAddress = safeField(header.address);
          resumeAbout = safeField(header.shortAbout);
          encryptedResumeData = encryptToBuffer(JSON.stringify(validated.data));
        } catch (error) {
          console.error("Failed to parse resume data:", error);
          return;
        }

        // Only proceed if we have valid data
        if (encryptedResumeData) {
          const encryptedFileContent = encryptToBuffer(resume.rawText);

          // Check if user and company exists, this is company data
          if (resume.userId && resume.companyId) {
            try {
              await prisma.$transaction(async (tx: any) => {
                // Check if user already has a resume
                const exists = await tx.userResume.findFirst({
                  where: {
                    fileId: resume.fileId,
                  },
                });

                if (exists) {
                  // Update existing resume
                  await tx.userResume.update({
                    where: {
                      id: exists.id,
                    },
                    data: {
                      fileId: resume.fileId,
                      companyId: resume.companyId,
                      username: resumeUsername,
                      name: encryptToBuffer(resumeName),
                      about: encryptToBuffer(resumeAbout),
                      email: encryptToBuffer(resumeEmail),
                      phone: encryptToBuffer(resumePhone),
                      website: encryptToBuffer(resumeWebsite),
                      xaccount: encryptToBuffer(resumeXaccount),
                      linkedin: encryptToBuffer(resumeLinkedin),
                      github: encryptToBuffer(resumeGithub),
                      address: encryptToBuffer(resumeAddress),
                      fileContent: encryptedFileContent,
                      resumeData: encryptedResumeData,
                    },
                  });
                } else {
                  // Create new resume
                  await tx.userResume.create({
                    data: {
                      id: uuidv4(),
                      userId: resume.userId,
                      companyId: resume.companyId,
                      fileId: resume.fileId,
                      username: resumeUsername,
                      name: encryptToBuffer(resumeName),
                      about: encryptToBuffer(resumeAbout),
                      email: encryptToBuffer(resumeEmail),
                      phone: encryptToBuffer(resumePhone),
                      website: encryptToBuffer(resumeWebsite),
                      xaccount: encryptToBuffer(resumeXaccount),
                      linkedin: encryptToBuffer(resumeLinkedin),
                      github: encryptToBuffer(resumeGithub),
                      address: encryptToBuffer(resumeAddress),
                      fileContent: encryptedFileContent,
                      resumeData: encryptedResumeData,
                    },
                  });
                }

                // Delete the processed resume from temp table
                await tx.userResumeTemp.delete({
                  where: {
                    id: resume.id,
                  },
                });
              });
            } catch (error) {
              console.error(
                `Failed to process resume (company) ${resume.id} in transaction:`,
                error
              );
            }
          } else if (resume.userId) {
            try {
              await prisma.$transaction(async (tx: any) => {
                // Check if user already has a resume
                const exists = await tx.userResume.findFirst({
                  where: {
                    OR: [{ userId: resume.userId }, { fileId: resume.fileId }],
                  },
                });

                if (exists) {
                  // Update existing resume
                  await tx.userResume.update({
                    where: {
                      id: exists.id,
                    },
                    data: {
                      fileId: resume.fileId,
                      companyId: resume.companyId,
                      username: resumeUsername,
                      name: encryptToBuffer(resumeName),
                      about: encryptToBuffer(resumeAbout),
                      email: encryptToBuffer(resumeEmail),
                      phone: encryptToBuffer(resumePhone),
                      website: encryptToBuffer(resumeWebsite),
                      xaccount: encryptToBuffer(resumeXaccount),
                      linkedin: encryptToBuffer(resumeLinkedin),
                      github: encryptToBuffer(resumeGithub),
                      address: encryptToBuffer(resumeAddress),
                      fileContent: encryptedFileContent,
                      resumeData: encryptedResumeData,
                    },
                  });
                } else {
                  // Create new resume
                  await tx.userResume.create({
                    data: {
                      id: uuidv4(),
                      userId: resume.userId,
                      companyId: resume.companyId,
                      fileId: resume.fileId,
                      username: resumeUsername,
                      name: encryptToBuffer(resumeName),
                      about: encryptToBuffer(resumeAbout),
                      email: encryptToBuffer(resumeEmail),
                      phone: encryptToBuffer(resumePhone),
                      website: encryptToBuffer(resumeWebsite),
                      xaccount: encryptToBuffer(resumeXaccount),
                      linkedin: encryptToBuffer(resumeLinkedin),
                      github: encryptToBuffer(resumeGithub),
                      address: encryptToBuffer(resumeAddress),
                      fileContent: encryptedFileContent,
                      resumeData: encryptedResumeData,
                    },
                  });
                }

                // Delete the processed resume from temp table
                await tx.userResumeTemp.delete({
                  where: {
                    id: resume.id,
                  },
                });
              });
            } catch (error) {
              console.error(
                `Failed to process resume (user) ${resume.id} in transaction:`,
                error
              );
            }
          }
        }
      });

      await step.sleep("processing-delay", "1s");
    }

    // If there are still items, trigger the function again
    const remainingResumes = await prisma.userResumeTemp.findMany({});
    if (remainingResumes.length > 0) {
      await step.run("trigger-next-batch", async () => {
        await sendInngestEvent({
          name: "resume/temp-available",
          data: {},
        });
      });
    }

    return { message: "Completed temp resume processing" };
  }
);

export const consolidatedCronJobs = inngest.createFunction(
  {
    id: "consolidated-cron-jobs",
    retries: 0, // Disable retries
  },
  { cron: "0 * * * *" }, // Run every hour (at minute 0)
  async ({ event, step }) => {
    await step.run("check-temp-jobs", async () => {
      await sendInngestEvent({
        name: "job/temp-available",
        data: {},
      });
    });

    // Unified periodic matching trigger
    await step.run("trigger-periodic-matching", async () => {
      const matchingRunning = await prisma.systemFlag.findUnique({
        where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
      });

      if (matchingRunning?.value !== "true") {
        // Set the unified flag
        await prisma.systemFlag.upsert({
          where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
          update: { value: "true" },
          create: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG, value: "true" },
        });

        // Decision logic:
        // 1. Prioritize globally unmatched jobs
        const jobsWithMatches = await prisma.jobResumeMatch.findMany({
          select: { jobId: true },
          distinct: ["jobId"],
        });
        const matchedJobIds = jobsWithMatches.map((match: any) => match.jobId);
        const unmatchedJobsCount = await prisma.jobPost.count({
          where: {
            id: { notIn: matchedJobIds },
            status: "ACTIVE",
          },
        });

        if (unmatchedJobsCount > 0) {
          await sendInngestEvent({ name: "job/resume-matching", data: {} });
        } else {
          // 2. Prioritize globally unmatched resumes
          const resumesWithMatches = await prisma.jobResumeMatch.findMany({
            select: { resumeId: true },
            distinct: ["resumeId"],
          });
          const matchedResumeIds = resumesWithMatches.map(
            (match: any) => match.resumeId
          );
          const unmatchedResumesCount = await prisma.userResume.count({
            where: { id: { notIn: matchedResumeIds } },
          });

          if (unmatchedResumesCount > 0) {
            await sendInngestEvent({ name: "resume/job-matching", data: {} });
          } else {
            // 3. Fallback: No globally unmatched items, trigger one for its internal fallback.
            //    Default to job-to-resume matching for fallback.
            //    The triggered function will handle its own fallback logic.
            await sendInngestEvent({ name: "job/resume-matching", data: {} });
          }
        }
      }
    });

    await step.run("evaluate-resume", async () => {
      // Check if an evaluation process is already running
      const evaluationRunning = await prisma.systemFlag.findUnique({
        where: { name: "resume_evaluation_in_progress" },
      });

      if (evaluationRunning?.value !== "true") {
        const evaluatedResumes = await prisma.userResumeEvaluation.findMany({
          select: { resumeId: true },
        });

        const evaluatedResumeIds = evaluatedResumes.map(
          (item: any) => item.resumeId
        );

        const unevaluatedResumesCount = await prisma.userResume.count({
          where: {
            id: { notIn: evaluatedResumeIds },
          },
        });

        if (unevaluatedResumesCount > 0) {
          await prisma.systemFlag.upsert({
            where: { name: "resume_evaluation_in_progress" },
            update: { value: "true" },
            create: { name: "resume_evaluation_in_progress", value: "true" },
          });

          await sendInngestEvent({
            name: "resume/evaluation",
            data: {},
          });
        }
      }
    });

    await step.run("parse-resume-file", async () => {
      // Check if a parsing process is already running
      const parsingRunning = await prisma.systemFlag.findUnique({
        where: { name: "resume_file_parsing_in_progress" },
      });

      if (parsingRunning?.value !== "true") {
        const parsedFiles = await prisma.userResume.findMany({
          select: { fileId: true },
        });

        const parsedFilesIds = parsedFiles.map((item: any) => item.fileId);

        const unparsedFilesCount = await prisma.userFile.count({
          where: {
            id: {
              notIn: parsedFilesIds.filter(
                (id: any): id is string => id !== null
              ),
            },
            fileUse: "RESUME",
            OR: [{ parseStatus: null }, { parseStatus: { not: "SUCCESS" } }],
            parseCount: { lt: 3 },
          },
        });

        if (unparsedFilesCount > 0) {
          await prisma.systemFlag.upsert({
            where: { name: "resume_file_parsing_in_progress" },
            update: { value: "true" },
            create: { name: "resume_file_parsing_in_progress", value: "true" },
          });

          await sendInngestEvent({
            name: "file/resume-parsing",
            data: {},
          });
        }
      }
    });

    return { success: true };
  }
);

export const sendJobToAI = inngest.createFunction(
  { id: "process-job-ai" },
  { event: "job/ai.process" },
  async ({ event, step }) => {
    const { userFile } = event.data;
    let rawContent = "";

    const user = await prisma.user.findFirst({
      where: {
        id: userFile.userId,
      },
      select: {
        companyId: true,
      },
    });

    const resumeObject = await step.run("send-job-to-ai", async () => {
      const response = await fetch(`${AI_API_URL}/parse/job`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: userFile.url,
          filename: userFile.fileName,
          userId: userFile.userId,
          companyId: user?.companyId,
        }),
      });

      const result = await response.json();
    });

    return {
      success: true,
      userId: userFile.userId,
      message: "AI processing completed successfully",
    };
  }
);

export const jobTempPeriodicParse = inngest.createFunction(
  { id: "job-temp-periodic-parsing" },
  { event: "job/temp-available" },

  async ({ event, step }) => {
    const jobs = await prisma.userJobTemp.findMany({});

    for (const job of jobs) {
      await step.run(`process-job-${job.id}`, async () => {
        let encryptedJobData = null;
        let transformedData;

        try {
          const parsedJobData = JSON.parse(job.jobData);
          transformedData = transformParsedDataToJobSchema(parsedJobData);
          const validated = JobDataSchema.safeParse(transformedData);

          if (!validated.success) {
            console.error("Validation failed:", validated.error);
            return;
          }

          encryptedJobData = encryptToBuffer(JSON.stringify(validated.data));
        } catch (error) {
          console.error("Failed to parse job data:", error);
          return;
        }

        // Create or update the job post with the processed data
        if (encryptedJobData && transformedData) {
          try {
            // Extract salary values and convert to numbers
            const salaryRangeArray =
              transformedData.standardFields.salary_range || [];
            const salaryRange =
              salaryRangeArray.length > 0
                ? salaryRangeArray[0]
                : { min: "0", max: "0", currency: "USD" };
            const salaryMin = salaryRange.min || "0";
            const salaryMax = salaryRange.max || "0";
            const salaryCurrency = salaryRange.currency || "USD";

            // Extract numeric values from salary strings (removing currency symbols and commas)
            const salaryFromValue =
              parseInt(salaryMin.replace(/[^0-9]/g, "")) || 0;
            const salaryToValue =
              parseInt(salaryMax.replace(/[^0-9]/g, "")) || 0;

            // console.log("Salary conversion:", {
            //   original: {
            //     min: salaryMin,
            //     max: salaryMax,
            //     currency: salaryCurrency,
            //   },
            //   converted: { from: salaryFromValue, to: salaryToValue },
            // });

            // Extract benefits from additional sections
            const benefits = [];
            for (const section of transformedData.additionalSections) {
              if (
                section.section_name.toLowerCase().includes("offer") ||
                section.section_name.toLowerCase().includes("benefit")
              ) {
                if (section.details && section.details.length > 0) {
                  benefits.push(...section.details);
                }
              }
            }

            // Extract skills as simple string array
            const skills = transformedData.standardFields.skills_required || [];

            await prisma.jobPost.create({
              data: {
                userId: job.userId,
                companyId: job.companyId,
                jobTitle: transformedData.standardFields.title,
                employmentType: transformedData.standardFields.employment_type,
                experienceLevel:
                  transformedData.standardFields.experience_level,
                country: "PH", // Default value or extract from location
                location: transformedData.standardFields.location,
                salaryCurrency: salaryCurrency,
                salaryFrom: salaryFromValue,
                salaryTo: salaryToValue,
                jobDescription: JSON.stringify({
                  requirements: transformedData.standardFields.requirements,
                  responsibilities:
                    transformedData.standardFields.responsibilities,
                  skills: transformedData.standardFields.skills_required,
                  additionalSections: transformedData.additionalSections,
                }),
                listingDuration: 30, // Default value
                interviewType: "ONLINE", // Default value
                localRemoteWork: false, // Default value
                overseasRemoteWork: false, // Default value
                skills: skills, // Pass as simple string array
                languageRequirements: [],
                tags: [],
                status: "ACTIVE",
                rawText: job.rawText,
              },
            });

            // Delete the temp job after processing
            await prisma.userJobTemp.delete({
              where: {
                id: job.id,
              },
            });
          } catch (error) {
            console.error("Failed to create job post:", error);
          }
        }
      });

      await step.sleep("processing-delay", "1s");
    }

    // If there are still items, trigger the function again
    const remainingJobs = await prisma.userJobTemp.findMany({});
    if (remainingJobs.length > 0) {
      await step.run("trigger-next-batch", async () => {
        await sendInngestEvent({
          name: "job/temp-available",
          data: {},
        });
      });
    }

    return { message: "Completed temp job processing" };
  }
);

export const jobToResumePeriodicMatching = inngest.createFunction(
  {
    id: "job-resume-periodic-matching",
    retries: 0, // Disable retries
  },
  { event: "job/resume-matching" },

  async ({ event, step }) => {
    try {
      const JOB_PROCESSING_BATCH_SIZE = 5; // How many globally unmatched jobs to process in one run
      const FALLBACK_JOB_PROCESSING_BATCH_SIZE = 2; // Smaller batch for fallback processing
      const RESUME_BATCH_SIZE_FOR_AI = 50; // How many resumes to send per AI call for a single job
      let processedFallbackBatch = false;

      let jobsToProcess = await step.run(
        "fetch-unmatched-jobs-batch",
        async () => {
          const jobsWithMatches = await prisma.jobResumeMatch.findMany({
            select: { jobId: true },
            distinct: ["jobId"],
          });
          const matchedJobIds = jobsWithMatches.map(
            (match: any) => match.jobId
          );

          return await prisma.jobPost.findMany({
            where: {
              id: { notIn: matchedJobIds },
              status: "ACTIVE",
            },
            select: {
              id: true,
              rawText: true,
              jobDescription: true,
              userId: true,
            },
            take: JOB_PROCESSING_BATCH_SIZE,
          });
        }
      );

      // Tier 2: Fallback if no globally unmatched jobs found
      if (!jobsToProcess || jobsToProcess.length === 0) {
        jobsToProcess = await step.run(
          "fetch-fallback-active-jobs-batch",
          async () => {
            // Fetch oldest active jobs that haven't been processed by fallback recently
            return await prisma.jobPost.findMany({
              where: {
                status: "ACTIVE",
                // Optional: Add a condition to re-process after a certain time if needed
                // OR: [
                //   { lastProcessedForFallbackMatchingAt: null },
                //   { lastProcessedForFallbackMatchingAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // e.g., older than 1 day
                // ]
              },
              select: {
                id: true,
                rawText: true,
                jobDescription: true,
                userId: true,
              },
              orderBy: [
                // Prioritize jobs not yet processed in fallback, then by oldest
                {
                  lastProcessedForFallbackMatchingAt: {
                    sort: "asc",
                    nulls: "first",
                  },
                },
                { createdAt: "asc" }, // Secondary sort for those with null or same timestamp
              ],
              take: FALLBACK_JOB_PROCESSING_BATCH_SIZE,
            });
          }
        );
        if (jobsToProcess && jobsToProcess.length > 0) {
          processedFallbackBatch = true;
        }
      }

      if (!jobsToProcess || jobsToProcess.length === 0) {
        // No work found in either tier
        await step.run(
          "clear-comprehensive-matching-flag-no-job-work",
          async () => {
            await prisma.systemFlag.update({
              where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
              data: { value: "false" },
            });
          }
        );
        return {
          message:
            "No globally unmatched jobs and no fallback active jobs found to process.",
        };
      }

      const allResumesResult = await step.run(
        "fetch-all-resumes-with-content",
        async () => {
          return await GetAllResumeRawText();
        }
      );
      const allResumesWithContent = allResumesResult?.data || [];

      if (allResumesWithContent.length === 0) {
        // No need to clear flag here if jobs were found, but no resumes exist.
        // console.log("jobToResumePeriodicMatching: No resumes found via GetAllResumeRawText.");
        // The loop over jobs won't run, and it will proceed to flag clearing or recursion check.
        // However, if we want to be explicit:
        // await step.run("clear-job-matching-flag-no-resumes-for-jobs", async () => { ... });
        // For now, let it proceed. The function will complete if no resumes.
      }

      for (const job of jobsToProcess) {
        await step.run(`match-job-${job.id}-to-resumes`, async () => {
          const jobDataForAI = {
            id: job.id,
            raw_text: job.rawText || job.jobDescription || "",
          };

          if (!jobDataForAI.raw_text) {
            console.warn(`Skipping job ${job.id} due to no raw_text.`);
            return;
          }

          if (allResumesWithContent.length === 0) {
            return;
          }

          const existingMatchesForThisJob =
            await prisma.jobResumeMatch.findMany({
              where: { jobId: job.id as string },
              select: { resumeId: true },
            });
          const matchedResumeIdsForThisJob = existingMatchesForThisJob.map(
            (m: any) => m.resumeId
          );

          const resumesToConsiderForThisJob = allResumesWithContent
            .filter(
              (resume: any) => !matchedResumeIdsForThisJob.includes(resume.id) // raw_text is already guaranteed by GetAllResumeRawTextData
            )
            .map((resume: any) => ({
              // Format for AI
              id: resume.id,
              raw_text: resume.raw_text, // Directly use the raw_text from GetAllResumeRawTextData
            }));

          const options = {
            skills_match: 0.4,
            experience_alignment: 0.35,
            education_fit: 0.25,
          };

          for (
            let i = 0;
            i < resumesToConsiderForThisJob.length;
            i += RESUME_BATCH_SIZE_FOR_AI
          ) {
            const resumeChunk = resumesToConsiderForThisJob.slice(
              i,
              i + RESUME_BATCH_SIZE_FOR_AI
            );

            await fetch(`${AI_API_URL}/match/job-to-resumes`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                userId: job.userId, // Job poster's ID
                jobId: job.id,
                job: jobDataForAI,
                resumes: resumeChunk,
                options: options,
              }),
            });
            // AI service handles saving results. Log AI response if needed for debugging.
          }
        }); // End of step.run(`match-job-${job.id}-to-resumes`)

        // If this job was processed as part of a fallback batch, update its timestamp.
        // This is done regardless of whether AI calls were made for it in this run.
        if (processedFallbackBatch) {
          await step.run(
            `update-fallback-timestamp-job-${job.id}`,
            async () => {
              await prisma.jobPost.update({
                where: { id: job.id },
                data: { lastProcessedForFallbackMatchingAt: new Date() },
              });
            }
          );
        }
      }

      // Recursion logic adjustment
      if (!processedFallbackBatch) {
        const moreGloballyUnmatchedJobsExist = await step.run(
          "check-if-more-globally-unmatched-jobs-exist",
          async () => {
            const jobsWithMatches = await prisma.jobResumeMatch.findMany({
              select: { jobId: true },
              distinct: ["jobId"],
            });
            const matchedJobIds = jobsWithMatches.map(
              (match: any) => match.jobId
            );
            const count = await prisma.jobPost.count({
              where: {
                id: { notIn: matchedJobIds },
                status: "ACTIVE",
              },
            });
            return count > 0;
          }
        );

        if (moreGloballyUnmatchedJobsExist) {
          await step.sleep("batch-cooldown", "5m");
          await step.run(
            "trigger-next-batch-for-globally-unmatched-jobs",
            async () => {
              await sendInngestEvent({
                name: "job/resume-matching", // Triggers this function again
                data: {},
              });
            }
          );
          // Return here to prevent clearing the flag if we are recursing for globally unmatched
          return {
            message: "Continuing with next batch of globally unmatched jobs.",
          };
        }
      }

      // If we processed a fallback batch, or if no more globally unmatched jobs exist, clear the flag.
      // `consolidatedCronJobs` will re-trigger if needed for further fallback batches.
      await step.run(
        "clear-comprehensive-matching-flag-job-completed-or-fallback",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );

      return {
        message: `Completed batch of job-to-resume matching. Processed fallback: ${processedFallbackBatch}`,
      };
    } catch (error) {
      // If there's an error, make sure we still clear the flag
      await step.run(
        "clear-comprehensive-matching-flag-job-on-error",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );

      throw error; // Re-throw the error for Inngest to handle
    }
  }
);

export const resumeToJobPeriodicMatching = inngest.createFunction(
  {
    id: "resume-job-periodic-matching",
    retries: 0, // Disable retries
  },
  { event: "resume/job-matching" },

  // This function processes a batch of globally unmatched resumes and finds job matches for them.
  async ({ event, step }) => {
    console.log("[resumeToJobPeriodicMatching] Function started.");
    try {
      const RESUME_PROCESSING_BATCH_SIZE = 10; // How many globally unmatched resumes to process in one run
      const FALLBACK_RESUME_PROCESSING_BATCH_SIZE = 1; // Process one resume at a time in fallback
      const JOB_BATCH_SIZE_FOR_AI = 50; // If a resume needs to be matched against many jobs, send jobs to AI in these batch sizes
      let processedFallbackBatch = false;

      console.log(
        `[resumeToJobPeriodicMatching] Attempting to fetch globally unmatched resumes. Batch size: ${RESUME_PROCESSING_BATCH_SIZE}`
      );
      let targetResumes = await step.run(
        "fetch-batch-globally-unmatched-resumes",
        async () => {
          const resumesWithMatches = await prisma.jobResumeMatch.findMany({
            select: { resumeId: true },
            distinct: ["resumeId"],
          });
          const idsOfResumesWithAnyMatch = resumesWithMatches.map(
            (match: any) => match.resumeId
          );

          // Get IDs of globally unmatched resumes
          const globallyUnmatchedResumeInfos = await prisma.userResume.findMany(
            {
              where: { id: { notIn: idsOfResumesWithAnyMatch } },
              select: { id: true, userId: true }, // Select necessary fields
              take: RESUME_PROCESSING_BATCH_SIZE,
            }
          );

          if (globallyUnmatchedResumeInfos.length === 0) {
            console.log(
              "[resumeToJobPeriodicMatching] No globally unmatched resume infos found."
            );
            return [];
          }

          // Fetch full resume data (including raw text) for these specific IDs
          // This might require a helper like `GetUserResumesWithRawTextByIds(ids)`
          // For now, assuming GetUserRawResume can be adapted or we fetch individually (less efficient)
          // or GetUserRawResume([]) is filtered here.
          // Let's use the new getRawResumesByIds function.
          const globallyUnmatchedResumeIdsToFetch =
            globallyUnmatchedResumeInfos.map((r: any) => r.id);
          if (globallyUnmatchedResumeIdsToFetch.length === 0) {
            // This case should be covered by "No globally unmatched resume infos found" log,
            // but as a safeguard if that log was missed.
            return [];
          }
          console.log(
            `[resumeToJobPeriodicMatching] Attempting to fetch content for ${globallyUnmatchedResumeIdsToFetch.length} globally unmatched resume IDs.`
          );
          const resumesWithContent = await getRawResumesByIds(
            globallyUnmatchedResumeIdsToFetch
          );
          console.log(
            `[resumeToJobPeriodicMatching] Fetched content for ${resumesWithContent.length} globally unmatched resumes out of ${globallyUnmatchedResumeIdsToFetch.length} IDs.`
          );
          return resumesWithContent;
        }
      );
      console.log(
        `[resumeToJobPeriodicMatching] Found ${targetResumes?.length || 0} globally unmatched resumes to process.`
      );

      // Tier 2: Fallback if no globally unmatched resumes found
      if (!targetResumes || targetResumes.length === 0) {
        console.log(
          `[resumeToJobPeriodicMatching] No globally unmatched resumes. Attempting to fetch fallback resumes. Batch size: ${FALLBACK_RESUME_PROCESSING_BATCH_SIZE}`
        );
        targetResumes = await step.run(
          "fetch-fallback-active-resumes-batch",
          async () => {
            const globallyUnmatchedResumeInfos =
              await prisma.userResume.findMany({
                where: {
                  // Optionally, add a condition to re-process after a certain time
                  // OR: [ // TODO: Consider re-enabling this if needed for re-processing logic
                  //   { lastProcessedForFallbackMatchingAt: null },
                  //   { lastProcessedForFallbackMatchingAt: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // e.g., older than 1 day
                  // ]
                },
                select: { id: true, userId: true },
                orderBy: [
                  // Prioritize resumes not yet processed in fallback, then by oldest
                  {
                    lastProcessedForFallbackMatchingAt: {
                      sort: "asc",
                      nulls: "first",
                    },
                  },
                  { createdAt: "asc" }, // Secondary sort
                ],
                take: FALLBACK_RESUME_PROCESSING_BATCH_SIZE,
              });
            if (globallyUnmatchedResumeInfos.length === 0) {
              console.log(
                "[resumeToJobPeriodicMatching] No fallback resume infos found."
              );
              return [];
            }
            const fallbackResumeIdsToFetch = globallyUnmatchedResumeInfos.map(
              (r: any) => r.id
            );
            if (fallbackResumeIdsToFetch.length === 0) {
              // Safeguard, though covered by "No fallback resume infos found"
              return [];
            }
            console.log(
              `[resumeToJobPeriodicMatching] Attempting to fetch content for ${fallbackResumeIdsToFetch.length} fallback resume IDs.`
            );
            const resumesWithContent = await getRawResumesByIds(
              fallbackResumeIdsToFetch
            );
            console.log(
              `[resumeToJobPeriodicMatching] Fetched content for ${resumesWithContent.length} fallback resumes out of ${fallbackResumeIdsToFetch.length} IDs.`
            );
            return resumesWithContent;
          }
        );
        if (targetResumes && targetResumes.length > 0) {
          processedFallbackBatch = true;
          console.log(
            `[resumeToJobPeriodicMatching] Found ${targetResumes.length} fallback resumes to process.`
          );
        }
      }

      if (!targetResumes || targetResumes.length === 0) {
        console.log(
          "[resumeToJobPeriodicMatching] No target resumes found (neither globally unmatched nor fallback). Clearing flag and exiting."
        );
        await step.run(
          "clear-comprehensive-matching-flag-no-resume-work",
          async () => {
            await prisma.systemFlag.update({
              where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
              data: { value: "false" },
            });
          }
        );
        return {
          message:
            "No globally unmatched resumes and no fallback active resumes found to process.",
        };
      }

      // If the number of jobs is extremely large (e.g., 1000s), this also might need batching.
      // For now, assume fetching all job details (ID, rawText, userId) is manageable.
      const allJobsResult = await step.run(
        "fetch-all-active-jobs",
        async () => {
          console.log(
            "[resumeToJobPeriodicMatching] Fetching all active jobs."
          );
          return await GetAllJobsRawText();
        }
      );
      const allJobs = allJobsResult?.data || [];
      console.log(
        `[resumeToJobPeriodicMatching] Found ${allJobs.length} active jobs.`
      );
      if (allJobs.length === 0) {
        console.log(
          "[resumeToJobPeriodicMatching] No active jobs found. Clearing flag and exiting."
        );
        await step.run(
          "clear-comprehensive-matching-flag-no-jobs-for-resumes",
          async () => {
            await prisma.systemFlag.update({
              where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
              data: { value: "false" },
            });
          }
        );
        return { message: "No active jobs found to match against." };
      }

      for (const resume of targetResumes) {
        console.log(
          `[resumeToJobPeriodicMatching] Processing resume ID: ${resume.id}`
        );
        await step.run(`match-resume-${resume.id}-to-jobs`, async () => {
          const resumeDataForAI = {
            id: resume.id,
            raw_text: resume.fileContent || "", // Use only fileContent for raw text
            // userId: resume.userId // If AI needs resume owner's ID
          };

          if (!resumeDataForAI.raw_text) {
            console.warn(
              `[resumeToJobPeriodicMatching] Skipping resume ${resume.id} due to no raw_text. fileContent was: '${resume.fileContent}'`
            );
            return;
          }

          const existingMatchesForThisResume =
            await prisma.jobResumeMatch.findMany({
              where: { resumeId: resume.id },
              select: { jobId: true },
            });
          const matchedJobIdsForThisResume = existingMatchesForThisResume.map(
            (m: any) => m.jobId
          );

          const candidateJobsForResume = allJobs.filter(
            (job: any) =>
              !matchedJobIdsForThisResume.includes(job.id) &&
              (job.rawText || job.jobDescription)
          );
          console.log(
            `[resumeToJobPeriodicMatching] Resume ${resume.id}: Found ${candidateJobsForResume.length} candidate jobs to match against (after filtering already matched and jobs without text).`
          );

          if (candidateJobsForResume.length === 0) {
            console.log(
              `[resumeToJobPeriodicMatching] Resume ${resume.id}: No new candidate jobs to match. Skipping AI calls for this resume.`
            );
            return; // No new jobs for this resume to be matched against
          }

          const options = {
            skills_match: 0.4,
            experience_alignment: 0.35,
            education_fit: 0.25,
          };

          // Using existing AI endpoint /match/job-to-resumes by calling it for each (job, [resume]) pair.
          // This means multiple AI calls if a resume has many potential job matches.
          for (const job of candidateJobsForResume) {
            const jobDataForAI = {
              id: job.id,
              raw_text: job.rawText || job.jobDescription || "",
            };
            console.log(
              `[resumeToJobPeriodicMatching] Preparing AI call for resume ${resume.id} and job ${job.id}.`
            );

            const response = await fetch(`${AI_API_URL}/match/job-to-resumes`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                userId: job.userId, // Job poster's ID
                jobId: job.id,
                job: jobDataForAI,
                resumes: [resumeDataForAI], // Send only the current resume in a list
                options: options,
              }),
            });
            const aiResult = await response.json();

            console.log(
              `[resumeToJobPeriodicMatching] AI call completed for resume ${resume.id} and job ${job.id}. Status: ${response.status}, AI Result: ${JSON.stringify(aiResult)}`
            );
            // The AI service handles saving the match results directly to the JobResumeMatch table.
            // No need to upsert here. We can log the AI result if needed for debugging.
          } // End loop for candidateJobsForResume
        }); // End step.run for `match-resume-${resume.id}-to-jobs`

        // Update lastProcessedForFallbackMatchingAt after all jobs for the current resume have been processed
        console.log(
          `[resumeToJobPeriodicMatching] Finished processing jobs for resume ${resume.id}. Processed fallback batch: ${processedFallbackBatch}`
        );
        if (processedFallbackBatch) {
          await step.run(
            `update-fallback-timestamp-resume-${resume.id}`,
            async () => {
              await prisma.userResume.update({
                where: { id: resume.id },
                data: { lastProcessedForFallbackMatchingAt: new Date() },
              });
            }
          );
        }
      } // End loop for targetResumes

      if (!processedFallbackBatch) {
        console.log(
          "[resumeToJobPeriodicMatching] Not a fallback batch. Checking if more globally unmatched resumes exist for recursion."
        );
        const moreGloballyUnmatchedResumesExist = await step.run(
          "check-if-more-globally-unmatched-resumes-exist",
          async () => {
            const resumesWithAnyMatch = await prisma.jobResumeMatch.findMany({
              select: { resumeId: true },
              distinct: ["resumeId"],
            });
            const idsOfResumesWithAtLeastOneMatch = resumesWithAnyMatch.map(
              (match: any) => match.resumeId
            );
            const count = await prisma.userResume.count({
              where: { id: { notIn: idsOfResumesWithAtLeastOneMatch } },
            });
            return count > 0;
          }
        );

        if (moreGloballyUnmatchedResumesExist) {
          console.log(
            "[resumeToJobPeriodicMatching] More globally unmatched resumes exist. Scheduling next batch."
          );
          await step.sleep("batch-cooldown", "5m");
          await step.run(
            "trigger-next-batch-for-globally-unmatched-resumes",
            async () => {
              await sendInngestEvent({
                name: "resume/job-matching",
                data: {},
              });
            }
          );
          return {
            message:
              "Continuing with next batch of globally unmatched resumes.",
          };
        }
        console.log(
          "[resumeToJobPeriodicMatching] No more globally unmatched resumes exist."
        );
      }

      console.log(
        "[resumeToJobPeriodicMatching] Batch processing complete or was a fallback batch. Clearing comprehensive matching flag."
      );
      await step.run(
        "clear-comprehensive-matching-flag-resume-completed-or-fallback",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );

      return {
        message: `Completed batch of resume-to-job matching. Processed fallback: ${processedFallbackBatch}`,
      };
    } catch (error) {
      console.error("[resumeToJobPeriodicMatching] Error occurred:", error);
      // If there's an error, make sure we still clear the flag
      await step.run(
        "clear-comprehensive-matching-flag-resume-on-error",
        async () => {
          await prisma.systemFlag.update({
            where: { name: COMPREHENSIVE_PERIODIC_MATCHING_FLAG },
            data: { value: "false" },
          });
        }
      );
      throw error; // Re-throw the error for Inngest to handle
    }
  }
);

export const resumePeriodicEvaluation = inngest.createFunction(
  {
    id: "resume-periodic-evaluation",
    retries: 0, // Disable retries
  },
  { event: "resume/evaluation" },
  async ({ event, step }) => {
    try {
      // Get resumes that are not yet evaluated
      const resumes = await step.run("fetch-unevaluated-resumes", async () => {
        const evaluatedResumes = await prisma.userResumeEvaluation.findMany({
          select: { resumeId: true },
        });

        const evaluatedResumeIds = evaluatedResumes.map(
          (item: any) => item.resumeId
        );

        return await GetUserRawResume(evaluatedResumeIds);
      });

      // Process each resume if resumes array exists
      if (resumes && resumes.length > 0) {
        for (const resume of resumes) {
          await step.run(`evaluate-resume-${resume.id}`, async () => {
            // Skip if resume has no data
            if (!resume.resumeData) {
              return;
            }

            // Parse resume data if needed
            let resumeData = resume.resumeData;
            if (typeof resumeData === "string") {
              try {
                resumeData = JSON.parse(resumeData);
              } catch (error) {
                console.error(
                  `Failed to parse resume data for ${resume.id}:`,
                  error
                );
                return;
              }
            }

            // Send to AI for evaluation
            try {
              const response = await fetch(`${AI_API_URL}/evaluate/resume`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  userId: resume.userId,
                  resumeId: resume.id,
                  resume_data: resumeData,
                }),
              });

              const result = await response.json();

              // Store evaluation results in database
              if (result && result.evaluation) {
                // await prisma.userResumeEvaluation.create({
                //   data: {
                //     userId: resume.userId,
                //     resumeId: resume.id,
                //     evaluationData: JSON.stringify(result),
                //   },
                // });
              }
            } catch (error) {
              console.error(`Error evaluating resume ${resume.id}:`, error);
            }
          });
        }

        // If there are more resumes to process, schedule the next batch after 5 minutes
        await step.sleep("batch-cooldown", "5m");

        // Check if there are still unevaluated resumes
        const remainingResumes = await step.run(
          "check-remaining-resumes",
          async () => {
            const evaluatedResumes = await prisma.userResumeEvaluation.findMany(
              {
                select: { resumeId: true },
              }
            );

            const evaluatedResumeIds = evaluatedResumes.map(
              (item: any) => item.resumeId
            );

            return await prisma.userResume.count({
              where: {
                id: { notIn: evaluatedResumeIds },
              },
            });
          }
        );

        // If there are still resumes to process, trigger the function again
        if (remainingResumes > 0) {
          await step.run("trigger-next-batch", async () => {
            await sendInngestEvent({
              name: "resume/evaluation",
              data: {},
            });
          });
        }
      }

      // At the end of processing, clear the processing flag
      await step.run("clear-evaluation-flag", async () => {
        await prisma.systemFlag.update({
          where: { name: "resume_evaluation_in_progress" },
          data: { value: "false" },
        });
      });

      return { message: "Completed resume evaluation" };
    } catch (error) {
      // If there's an error, make sure we still clear the flag
      await step.run("clear-evaluation-flag-after-error", async () => {
        await prisma.systemFlag.update({
          where: { name: "resume_evaluation_in_progress" },
          data: { value: "false" },
        });
      });

      throw error; // Re-throw the error for Inngest to handle
    }
  }
);

export const resumeFilePeriodicParsing = inngest.createFunction(
  {
    id: "resume-file-periodic-parsing",
    retries: 0, // Disable retries
  },
  { event: "file/resume-parsing" },
  async ({ event, step }) => {
    try {
      // Get resumes that are not yet parsed
      const files = await step.run(
        "fetch-unprocessed-resume-files",
        async () => {
          const processedResumes = await prisma.userResume.findMany({
            select: { fileId: true },
          });

          const processedResumeFileIds = processedResumes.map(
            (item: any) => item.fileId
          );

          return await GetUserResumeFile(
            processedResumeFileIds.filter(
              (id: any): id is string => id !== null
            )
          );
        }
      );

      // Process each file if files array exists
      if (files && files.length > 0) {
        for (const file of files) {
          await step.run(`process-file-${file.id}`, async () => {
            // Skip if file has no data
            if (!file.url) {
              return;
            }

            // Send file to AI for parsing
            try {
              const response = await fetch(`${AI_API_URL}/parse/resume`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  url: file.url,
                  companyId: file.companyId,
                  fileId: file.id,
                  filename: file.fileName,
                  userId: file.userId,
                }),
              });

              const result = await response.json();

              // Store parsing results in database
              if (
                result &&
                result.status === "success" &&
                result.error === null
              ) {
                await prisma.userFile.update({
                  where: { id: file.id },
                  data: {
                    parseStatus: "SUCCESS",
                    parseResult: "AI processed resume file successfully.",
                    parseCount: { increment: 1 },
                  },
                });
              } else {
                await prisma.userFile.update({
                  where: { id: file.id },
                  data: {
                    parseStatus: "FAILED",
                    parseResult: JSON.stringify(result.detail),
                    parseCount: { increment: 1 },
                  },
                });
              }
            } catch (error) {
              console.error(`Error parsing file ${file.id}:`, error);
            }
          });
        }

        // If there are more resumes to process, schedule the next batch after 5 minutes
        await step.sleep("batch-cooldown-files", "5m");

        // Check if there are still unparsed resumes
        const remainingFiles = await step.run(
          "check-remaining-files",
          async () => {
            const parsedFiles = await prisma.userResume.findMany({
              select: { fileId: true },
            });

            const parsedFilesIds = parsedFiles.map((item: any) => item.fileId);

            return await prisma.userFile.count({
              where: {
                id: {
                  notIn: parsedFilesIds.filter(
                    (id: any): id is string => id !== null
                  ),
                },
                fileUse: "RESUME",
                OR: [
                  { parseStatus: null },
                  { parseStatus: { not: "SUCCESS" } },
                ],
                parseCount: { lt: 3 },
              },
            });
          }
        );

        // If there are still resumes to process, trigger the function again
        if (remainingFiles > 0) {
          await step.run("trigger-next-batch", async () => {
            await sendInngestEvent({
              name: "file/resume-parsing",
              data: {},
            });
          });
        }
      }

      // At the end of processing, clear the processing flag
      await step.run("clear-file-parsing-flag", async () => {
        await prisma.systemFlag.update({
          where: { name: "resume_file_parsing_in_progress" },
          data: { value: "false" },
        });
      });

      return { message: "Completed resume file parsing" };
    } catch (error) {
      // If there's an error, make sure we still clear the flag
      await step.run("clear-file-parsing-flag-after-error", async () => {
        await prisma.systemFlag.update({
          where: { name: "resume_file_parsing_in_progress" },
          data: { value: "false" },
        });
      });

      throw error; // Re-throw the error for Inngest to handle
    }
  }
);

function generateUsername(name: string): string {
  const cleanName = name.replace(/\s+/g, "").toLowerCase(); // remove spaces and lowercase
  const randomDigits = Math.floor(10000000 + Math.random() * 90000000); // 8-digit number
  return `${randomDigits}-${cleanName}`;
}

// Inngest function for daily currency exchange rate updates
export const dailyCurrencyUpdate = inngest.createFunction(
  {
    id: "daily-currency-update-job",
    name: "Daily Currency Exchange Rate Update",
  },
  { cron: "0 5 * * *" }, // Run daily at 5 AM UTC (HH MM * * DayOfWeek) - Adjust as needed
  async ({ step }) => {
    await step.run("fetch-and-cache-all-exchange-rates", async () => {
      console.log(
        "INNGES_JOB: Starting daily currency exchange rate update..."
      );
      const result = await updateAllExchangeRatesFromAPI();
      console.log(
        "INNGES_JOB: Daily currency exchange rate update finished.",
        result
      );

      if (!result.success) {
        // You might want to throw an error here to have Inngest retry
        // or handle the failure in a specific way (e.g., send a notification)
        // For now, we'll throw an error to leverage Inngest's retry mechanism if configured.
        // If retries are disabled for this function, it will just mark the run as failed.
        throw new Error(`Daily currency update failed: ${result.message}`);
      }
      return { data: result }; // Inngest step.run expects a serializable object
    });

    return {
      body: {
        message: "Daily currency update process completed successfully.",
      },
    };
  }
);

export const extractResumeData = inngest.createFunction(
  { id: "process-resume-from-file", name: "Process Resume from File" },
  { event: "resume/extract-from-file" },
  async ({ event, step }) => {
    const { userFile }: { userFile: UserFile } = event.data;

    const extractedText = await step.run("extract-file-text", async () => {
      return await handleFileExtraction(userFile);
    });

    const { promptMarkdown, structured, extractedContact } = await step.run(
      "prepare-for-llm",
      async () => {
        return prepareResumeForLLM(extractedText);
      }
    );

    const transformedResumeData = adaptResumeSectionsToTransformerInput(
      structured,
      extractedContact
    );

    const resume: CandidateResume = {
      id: uuidv4(),
      userId: userFile.userId,
      companyId: userFile.companyId,
      fileId: userFile.id,
      username: "",
      name: extractedContact.name,
      title: "",
      about: "",
      email: extractedContact.email,
      phone: extractedContact.phone,
      website: "",
      xaccount: "",
      linkedin: "",
      github: "",
      address: "",
      picture: "",
      fileContent: extractedText,
      resumeData: transformedResumeData,
      promptMarkdown: promptMarkdown,
      structured: structured,
      metadata: extractedContact,
      status: PublishStatus.DRAFT,
    };

    await step.run("create-resume-record", async () => {
      const result = await CreateResume(resume);

      if (result) {
        if (resume.id && resume.fileContent) {
          const structuredWithId = {
            userId: resume.userId,
            resumeId: resume.id,
            resume_data: transformedResumeData,
          };

        //   console.log({ structured: JSON.stringify(structuredWithId) });

          const info: Record<string, string> = {
            userId: resume.userId as string,
            resumeId: resume.id,
            resumeText: await getResumeHeaderChunk(
              resume.fileContent ? resume.fileContent : ""
            ),
            resumeStructured: JSON.stringify(structuredWithId),
          };

          await sendInngestEvent({
            name: "resume/extract-contact-info",
            data: { info },
          });

          // Send to evaluation
          const resumeInfo = {
            userId: resume.userId as string,
            resumeId: resume.id,
            resume_text: extractedText,
          };
          console.log({
            resumeInfoEvaluation: JSON.stringify(resumeInfo, null, 2),
          });

          // Send to evaluation info
          await sendInngestEvent({
            name: "resume/evaluate-resume-info",
            data: { resumeInfo },
          });
        }
      }
    });

    return {
      message: `Successfully processed resume for fileId: ${userFile.id}`,
    };
  }
);

export const extractResumeContactInfo = inngest.createFunction(
  { id: "process-resume-contact-info", name: "Process Resume contact info" },
  { event: "resume/extract-contact-info" },
  async ({ event, step }) => {
    const { info } = event.data;

    if (info) {
      // Send to AI contact extract
      const aiResponse = await step.run(
        "send-resume-contact-to-ai",
        async () => {
          try {
            const response = await fetch(`${AI_API_URL}/parse/contact`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                userId: info.userId,
                resumeId: info.resumeId,
                resumeText: info.resumeText,
              }),
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error(
                `AI service returned non-OK status: ${response.status} ${response.statusText}. Body: ${errorText}`
              );
              throw new Error(
                `AI service HTTP error: ${response.status} ${response.statusText}. Details: ${errorText}`
              );
            }

            const result = await response.json();
            console.log({ PARSECONTACT: JSON.stringify(result, null, 2) });

            if (result.status === "success") {
              return result;
            } else {
              console.error(
                `AI service returned success: false. Details: ${JSON.stringify(result.detail)}`
              );
              throw new Error(
                `AI service processing failed: ${JSON.stringify(result.detail)}`
              );
            }
          } catch (error) {
            console.error(
              `Error during fetch or JSON parsing for resumeId ${info.resumeId}:`,
              error
            );
            throw error;
          }
        }
      );

      await step.run("update-resume-with-contact-info", async () => {
        const parsedData = aiResponse.data?.parsed_data;

        if (!parsedData) {
          console.error("AI response successful, but parsed_data is missing.", {
            resumeId: info.resumeId,
            aiResponse,
          });
          throw new Error(
            "AI response successful, but parsed_data is missing."
          );
        }

        let structuredForEvaluation: {
          userId: string;
          resumeId: string;
          resume_data: any;
        };
        try {
          structuredForEvaluation = JSON.parse(info.resumeStructured);
        } catch (e) {
          console.error("Failed to parse info.resumeStructured:", e);
          throw new Error("Invalid JSON in info.resumeStructured");
        }

        let resumeDataToUpdate = structuredForEvaluation.resume_data;

        if (
          resumeDataToUpdate &&
          resumeDataToUpdate.standardFields &&
          resumeDataToUpdate.standardFields.header
        ) {
          const header = resumeDataToUpdate.standardFields.header;
          if (parsedData.name !== null) header.name = parsedData.name;
          if (parsedData.email !== null) header.email = parsedData.email;
          if (parsedData.phone !== null) {
            // AI returns phone as string, ResumeDataSchema expects string[]
            header.phone = Array.isArray(parsedData.phone)
              ? parsedData.phone.map(String)
              : [String(parsedData.phone)];
          }
          if (parsedData.website !== null) header.website = parsedData.website;
          if (parsedData.xaccount !== null)
            header.xaccount = parsedData.xaccount;
          if (parsedData.linkedin !== null)
            header.linkedin = parsedData.linkedin;
          if (parsedData.github !== null) header.github = parsedData.github;
          if (parsedData.address !== null) header.address = parsedData.address;
          if (parsedData.summary !== null)
            header.shortAbout = parsedData.summary;
        }

        // This is the actual ResumeData object that will be sent as `resume_data`
        // in the AI evaluation request body, and also saved to Prisma.
        const resumeDataForAIAndPrisma = structuredForEvaluation.resume_data;

        // Updateresume
        const updated = await prisma.userResume.update({
          where: { id: info.resumeId },
          data: {
            name: parsedData.name
              ? encryptToBuffer(normalizeHeaders(parsedData.name))
              : null,
            title: parsedData.title
              ? encryptToBuffer(normalizeHeaders(parsedData.title))
              : null,
            about: parsedData.summary
              ? encryptToBuffer(normalizeParagraph(parsedData.summary))
              : null,
            email: parsedData.email ? encryptToBuffer(parsedData.email) : null,
            phone: parsedData.phone ? encryptToBuffer(parsedData.phone) : null,
            website: parsedData.website
              ? encryptToBuffer(parsedData.website)
              : null,
            xaccount: parsedData.xaccount
              ? encryptToBuffer(parsedData.xaccount)
              : null,
            linkedin: parsedData.linkedin
              ? encryptToBuffer(parsedData.linkedin)
              : null,
            github: parsedData.github
              ? encryptToBuffer(parsedData.github)
              : null,
            address: parsedData.address
              ? encryptToBuffer(parsedData.address)
              : null,
            resumeData: resumeDataForAIAndPrisma
              ? encryptToBuffer(JSON.stringify(resumeDataForAIAndPrisma))
              : null,
          },
        });

        if (updated) {
          await sendInngestEvent({
            name: "resume/job-matching",
            data: {},
          });
        }
      });

      return {
        message: `Successfully processed resume contact info for resumeId: ${info.resumeId}`,
      };
    } else {
      return {
        error: `Failed to extract contact info for resumeId: ${info.resumeId}`,
      };
    }
  }
);

export const evaluateExtractedResume = inngest.createFunction(
  { id: "process-resume-evaluate-info", name: "Process Resume evaluation" },
  { event: "resume/evaluate-resume-info" },
  async ({ event, step }) => {
    const { resumeInfo } = event.data;

    console.log({ resumeInfo: JSON.stringify(resumeInfo, null, 2) });

    if (resumeInfo) {
      await step.run("send-resume-evaluation-to-ai", async () => {
        try {
          const response = await fetch(`${AI_API_URL}/evaluate/resume_plain`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              userId: resumeInfo.userId,
              resumeId: resumeInfo.resumeId,
              resume_text: resumeInfo.resume_text,
            }),
          });

          console.log({ EVALRESPONSE: JSON.stringify(response, null, 2) });
        } catch (error) {
          console.error(
            `Error during resume evaluation for resumeId ${resumeInfo.resumeId}:`,
            error
          );
        }
      });
    } else {
      return {
        error: `Failed to evaluate for resumeId: ${resumeInfo.resumeId}`,
      };
    }
  }
);

export const extractJobData = inngest.createFunction(
  { id: "process-job-from-file", name: "Process Job from File" },
  { event: "job/extract-from-file" },
  async ({ event, step }) => {
    const { userFile }: { userFile: UserFile } = event.data;

    const extractedText = await step.run("extract-file-text", async () => {
      return await handleFileExtraction(userFile);
    });

    const { promptMarkdown, structured, metadata } = await step.run(
      "prepare-for-llm",
      async () => {
        return prepareJobForLLM(extractedText);
      }
    );

    const savedJobPost = await step.run("create-job-record", async () => {
      // Adapt structured job data to the JobPost type
      const adaptedJobData = adaptJobSectionsToJobPost(structured, metadata);
      console.log({ JOBFROMFILE: JSON.stringify(adaptedJobData, null, 2) });

      const jobToCreate: JobDescPost = {
        id: uuidv4(), // ID is now generated inside the memoized step
        userId: userFile.userId,
        companyId: userFile.companyId as string,
        fileId: userFile.id,
        jobTitle: metadata.jobTitle || "",
        employmentType: adaptedJobData.employmentType || "",
        experienceLevel: adaptedJobData.experienceLevel || "",
        country: adaptedJobData.country || "",
        location: normalizeHeaders(adaptedJobData.location || ""),
        department: normalizeHeaders(adaptedJobData.department || ""),
        salaryCurrency: adaptedJobData.salaryCurrency || "",
        salaryFrom: adaptedJobData.salaryFrom || 0,
        salaryTo: adaptedJobData.salaryTo || 0,
        jobDescription: JSON.stringify(structured) || "",
        listingDuration: adaptedJobData.listingDuration || 30,
        interviewType: adaptedJobData.interviewType || "ONSITE",
        localRemoteWork: adaptedJobData.localRemoteWork || false,
        overseasRemoteWork: adaptedJobData.overseasRemoteWork || false,
        skills: adaptedJobData.skills || [],
        languageRequirements: adaptedJobData.languageRequirements || [],
        tags: adaptedJobData.tags || [],
        status: userFile.postType === 1 ? "ACTIVE" : "DRAFT",
        rawText: extractedText || "",
        promptMarkdown: promptMarkdown || "",
        structured: structured || {},
        metadata: convertUndefinedToNull(metadata) || {},
      };

      console.log({ JobDescPost: JSON.stringify(jobToCreate, null, 2) });
      const createdJob = await CreateJobFromFile(jobToCreate);
      console.log({ JOBFROMSAVED: JSON.stringify(createdJob, null, 2) });
      return createdJob; // Return the saved record from the database
    });

    if (!savedJobPost) {
      // If the job record was not created, we cannot proceed.
      // Throwing an error will cause the Inngest function to fail and retry if configured.
      console.error(
        "Failed to create job record, 'savedJobPost' is null. Aborting function."
      );
      throw new Error(
        "Job record creation returned null. Cannot proceed with job header extraction."
      );
    }

    // Type guard to handle the case where CreateJobFromFile returns an error object
    if ("error" in savedJobPost) {
      console.error(
        `Failed to create job record: ${savedJobPost.error}. Aborting function.`
      );
      throw new Error(`Job record creation failed: ${savedJobPost.error}`);
    }

    // Send to AI for job extraction
    const jobInfo = {
      jobId: savedJobPost.id, // Use the ID from the saved record
      userId: userFile.userId as string,
      jobText: extractedText,
    };
    console.log({ JobHeaderExtraction: JSON.stringify(jobInfo, null, 2) });

    // Send to extraction of job
    await sendInngestEvent({
      name: "job/extract-job-info",
      data: { jobInfo },
    });

    return {
      message: `Successfully processed job for fileId: ${userFile.id}`,
    };
  }
);

export const extractJobInfo = inngest.createFunction(
  { id: "process-job-info", name: "Process Job info from text" },
  { event: "job/extract-job-info" },
  async ({ event, step }) => {
    const { jobInfo } = event.data;

    if (jobInfo) {
      // Send to AI job header extraction
      const aiResponse = await step.run("send-job-info-to-ai", async () => {
        try {
          const response = await fetch(`${AI_API_URL}/parse/job-v2`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              userId: jobInfo.userId,
              jobId: jobInfo.jobId,
              jobText: jobInfo.jobText,
            }),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `AI service returned non-OK status: ${response.status} ${response.statusText}. Body: ${errorText}`
            );
            throw new Error(
              `AI service HTTP error: ${response.status} ${response.statusText}. Details: ${errorText}`
            );
          }

          const result = await response.json();
          console.log({ PARSEJOBHEADER: JSON.stringify(result, null, 2) });

          if (result.status === "success") {
            return result;
          } else {
            console.error(
              `AI service returned success: false. Details: ${JSON.stringify(result.detail)}`
            );
            throw new Error(
              `AI service processing failed: ${JSON.stringify(result.detail)}`
            );
          }
        } catch (error) {
          console.error(
            `Error during fetch or JSON parsing for JOBId ${jobInfo.jobId}:`,
            error
          );
          throw error;
        }
      });

      await step.run("update-job-with-header-info", async () => {
        const parsedData = aiResponse.data?.parsed_data;

        console.log({
          JobHeaderParsedData: JSON.stringify(parsedData, null, 2),
        });

        if (!parsedData) {
          console.error("AI response successful, but parsed_data is missing.", {
            jobId: jobInfo.jobId,
            aiResponse,
          });
          throw new Error(
            "AI response successful, but parsed_data is missing."
          );
        }

        // Update job
        const updated = await prisma.jobPost.update({
          where: { id: jobInfo.jobId },
          data: {
            jobTitle: parsedData.jobTitle ? parsedData.jobTitle : "",
            country: parsedData.country ? parsedData.country : "",
            location: parsedData.location ? parsedData.location : "",
            department: parsedData.department ? parsedData.department : "",
            experienceLevel: parsedData.experienceLevel
              ? parsedData.experienceLevel
              : "",
            interviewType: parsedData.interviewType
              ? parsedData.interviewType
              : "",
            localRemoteWork:
              parsedData.localRemoteWork === true ||
              parsedData.localRemoteWork === "true",
            overseasRemoteWork:
              parsedData.overseasRemoteWork === true ||
              parsedData.overseasRemoteWork === "true",
            salaryCurrency: parsedData.salaryCurrency
              ? parsedData.salaryCurrency
              : "",
            salaryFrom: parsedData.salaryFrom ? parsedData.salaryFrom : 0,
            salaryTo: parsedData.salaryTo ? parsedData.salaryTo : 0,
            jobDescription: parsedData.jobDescription
              ? JSON.stringify(parsedData.jobDescription)
              : "",
          },
        });

        if (updated) {
          await sendInngestEvent({
            name: "job/resume-matching",
            data: {},
          });
        }
      });

      return {
        message: `Successfully processed job header info for jobId: ${jobInfo.jobId}`,
      };
    }

    return {
      message: `Successfully processed job for fileId: ${jobInfo.jobId}`,
    };
  }
);
