"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { UserJobSeekerSchema } from "@/data/zod/zodSchema";
import { Textarea } from "@/components/ui/textarea";
import { countryList } from "@/data/location/countryList";
import "@uploadthing/react/styles.css";
import { toast } from "sonner";
import { UpdateJobSeekerProfile } from "@/actions/user/updateUser";
import { useUserActions } from "@/hooks/useJobSeekerActions";
import { Button } from "../ui/button";

interface UserProfileFormProps {
  onSuccess?: () => void;
}

export function UserProfileForm({ onSuccess }: UserProfileFormProps) {
  const [pending, setPending] = useState(false);
  const [formInitialized, setFormInitialized] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [open, setOpen] = useState(false);
  
  const { jobSeekerData } = useUserActions();

  const form = useForm<z.infer<typeof UserJobSeekerSchema>>({
    resolver: zodResolver(UserJobSeekerSchema),
    defaultValues: {
      firstname: "",
      lastname: "",
      title: "",
      location: "",
      about: "",
      portfolio: "",
      linkedin: "",
      github: "",
      writing: "",
      mobilePhone: "",
      birthDate: "",
    },
  });

  useEffect(() => {
    if (!jobSeekerData.isLoading && jobSeekerData.data && !formInitialized) {
      const location = jobSeekerData?.data?.jobSeeker?.location || "";
      setSelectedLocation(location);
      
      form.reset({
        id: jobSeekerData?.data?.jobSeeker?.id || "",
        userId: jobSeekerData?.data?.jobSeeker?.userId || jobSeekerData?.data?.jobSeeker?.id || "",
        firstname: jobSeekerData?.data?.jobSeeker?.user?.firstName || jobSeekerData?.data?.jobSeeker?.firstName || "",
        lastname: jobSeekerData?.data?.jobSeeker?.user?.lastName || jobSeekerData?.data?.jobSeeker?.lastName || "",
        title: jobSeekerData?.data?.jobSeeker?.title || "",
        location: location,
        about: jobSeekerData?.data?.jobSeeker?.about || "",
        portfolio: jobSeekerData?.data?.jobSeeker?.portfolio || "",
        linkedin: jobSeekerData?.data?.jobSeeker?.linkedin || "",
        github: jobSeekerData?.data?.jobSeeker?.github || "",
        writing: jobSeekerData?.data?.jobSeeker?.writing || "",
        mobilePhone: jobSeekerData?.data?.jobSeeker?.mobilePhone || jobSeekerData?.data?.jobSeeker?.mobilePhone || "",
        birthDate: jobSeekerData?.data?.jobSeeker?.birthDate || jobSeekerData?.data?.jobSeeker?.birthDate || "",
      });
      setFormInitialized(true);
    }
  }, [jobSeekerData.isLoading, jobSeekerData.data, formInitialized, form]);

  // Update form value when location changes
  useEffect(() => {
    form.setValue("location", selectedLocation);
  }, [selectedLocation, form]);

  async function onSubmit(data: z.infer<typeof UserJobSeekerSchema>) {
    try {
      setPending(true);

      const response = await UpdateJobSeekerProfile(data);

      if (response) {
        if (response.success) {
          toast.success(response.success);
          
          // Call the onSuccess callback to close the dialog
          if (onSuccess) {
            onSuccess();
          }
          
          // Refresh the page or redirect
          window.location.href = "/home/<USER>/profile";
        } else if (response.error) {
          toast.error(response.error);
        }
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error(`Unexpected error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setPending(false);
    }
  }

  if (jobSeekerData.isLoading) {
    return <div>Loading...</div>;
  }

  const onSubmitWithValidation = form.handleSubmit(
    async (data) => {
      await onSubmit(data);
    },
    (errors) => {
      console.error("Form validation errors:", errors);
      toast.error("Please fix the form errors before submitting");
    }
  );

  return (
    <Form {...form}>
      <form onSubmit={onSubmitWithValidation} className="space-y-2">
        <div className="text-sm text-muted-foreground border-2 rounded-lg p-2">
            We adhere to the Data Privacy Act of 2012 (RA 10173). All your
            personal and sentive data, including links to your portfolio and
            social media profiles, are encrypted and stored securely.
        </div>
        <div className="py-2 pb-2 text-muted-foreground text-sm">
          <span className="text-red-500 text-sm">*</span> Required fields
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="firstname"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  First Name<span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Your first name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lastname"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Last Name<span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Your last name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <FormLabel>
              Current Location<span className="text-red-500">*</span>
            </FormLabel>
            <div className="relative">
              <button
                type="button"
                onClick={() => setOpen(!open)}
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer"
              >
                {selectedLocation ? (
                  <span>
                    {countryList.find(c => c.code === selectedLocation)?.flagEmoji}{" "}
                    {countryList.find(c => c.code === selectedLocation)?.name || selectedLocation}
                  </span>
                ) : (
                  <span className="text-muted-foreground">Select Country</span>
                )}
              </button>
              
              {open && (
                <div className="absolute z-50 w-full mt-1 bg-background rounded-md border shadow-lg max-h-60 overflow-auto">
                  <div className="p-1">
                    {countryList.map((country) => (
                      <div
                        key={country.code}
                        className="flex items-center px-2 py-1.5 text-sm rounded hover:bg-accent cursor-pointer"
                        onClick={() => {
                          setSelectedLocation(country.code);
                          form.setValue("location", country.code);
                          setOpen(false);
                        }}
                      >
                        <span className="mr-2">{country.flagEmoji}</span>
                        <span>{country.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            {form.formState.errors.location && (
              <p className="text-sm font-medium text-destructive mt-2">
                {form.formState.errors.location.message}
              </p>
            )}
          </div>

          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Current or Aspired Job Title
                </FormLabel>
                <FormControl>
                  <Input placeholder="e.g. Frontend Developer" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="about"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Short Bio</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us about yourself. Enter or paste text."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="portfolio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Portfolio Website</FormLabel>
                <FormControl>
                  <Input placeholder="https://your-portfolio.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="linkedin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LinkedIn Profile</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://linkedin.com/in/username"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="github"
            render={({ field }) => (
              <FormItem>
                <FormLabel>GitHub Profile</FormLabel>
                <FormControl>
                  <Input placeholder="https://github.com/username" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="writing"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Do you write? Let companies find it.</FormLabel>
                <FormControl>
                  <Input placeholder="https://blog/username" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex justify-center mt-6">
          <Button
            type="submit"
            className="w-[200px] cursor-pointer"
            disabled={pending || form.formState.isSubmitting}
            size="lg"
          >
            {pending || form.formState.isSubmitting ? "Submitting..." : "Submit"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
