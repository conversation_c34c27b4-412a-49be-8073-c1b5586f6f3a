"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { EyeIcon } from "lucide-react";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import Image from "next/image";
import { Pagination } from "../ui/pagination";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";

export function ShortlistedApplicants({
  jobId,
  shortlistedApplicants,
}: {
  jobId: string;
  shortlistedApplicants: any[];
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  if (!shortlistedApplicants || shortlistedApplicants.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        No shortlisted applicants yet
      </div>
    );
  }

  // Calculate pagination
  const totalPages = Math.ceil(shortlistedApplicants.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentApplicants = shortlistedApplicants.slice(startIndex, endIndex);

  return (
    <>
      <div className="w-full">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead style={{ width: "45%" }}>Name</TableHead>
              <TableHead style={{ width: "15%" }}>Match</TableHead>
              <TableHead style={{ width: "15%" }}>Evaluation</TableHead>
              <TableHead style={{ width: "15%" }}>Resume</TableHead>
              <TableHead style={{ width: "10%" }}>Shortlist</TableHead>
            </TableRow>
          </TableHeader>
        </Table>

        <ScrollArea className="w-full h-[300px]">
          <Table>
            <TableBody>
              {currentApplicants.map((applicant) => (
                <TableRow key={applicant.id}>
                  <TableCell
                    style={{ width: "45%" }}
                    className="flex items-center gap-2"
                  >
                    <Image
                      src={applicant.resume?.picture || "/icons/profile.png"}
                      alt={`${applicant.user?.firstName || ""}`}
                      width={28}
                      height={28}
                      className="rounded-full"
                    />
                    {applicant.user?.firstName || ""}&nbsp;
                    {applicant.user?.lastName || ""}
                  </TableCell>
                  <TableCell style={{ width: "15%" }}>
                    <MatchScoreCircle
                      score={
                        applicant.resume?.resumeMatches?.[0]?.overall_score
                      }
                    />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-4">
                    <ResumeMatchDialog
                      resumeMatch={applicant.resume?.resumeMatches?.[0]}
                      applicantName={`${applicant.user?.firstName || ""} ${applicant.user?.lastName || ""}`}
                    />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-4">
                    <a
                      href={`/home/<USER>/resumedoc/${applicant.user?.id}`}
                      target="_blank"
                      className="text-primary hover:underline cursor-pointer"
                      title="View Resume"
                    >
                      <Button
                        variant="ghost"
                        className="text-primary hover:underline cursor-pointer"
                        title="View Resume"
                      >
                        <EyeIcon className="w-6 h-6" />
                      </Button>
                    </a>
                  </TableCell>
                  <TableCell
                    style={{ width: "10%" }}
                    className="pl-4 cursor-pointer"
                  >
                    <ShortlistCheckbox
                      jobId={jobId}
                      resumeId={applicant.resume?.id || ""}
                      initialShortlisted={true}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>

        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <Pagination>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="mx-4">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </Pagination>
          </div>
        )}
      </div>
    </>
  );
}

