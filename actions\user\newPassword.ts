"use server";

import * as z from "zod";
import { NewPasswordSchema } from "@/data/zod/zodSchema";
import { GetPasswordResetTokenByToken } from "@/data/site/passwordResetToken";
import { GetUserByEmail } from "@/data/user/user";
import { prisma } from "@/lib/prisma/prismaClient";
import { hashString } from "@/utils/hashingHelpers";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const NewPassword = async (
  values: z.infer<typeof NewPasswordSchema>,
  token?: string | null
) => {
  
    // Arcjet protection
    await ensureServerActionProtection();

  if (!token) {
    return { error: "Missing token!" };
  }

  const validatedFields = NewPasswordSchema.safeParse(values);
  if (!validatedFields.success) {
    return { error: "Invalid fields!" };
  }

  const { password } = validatedFields.data;

    try {
        const existingToken = await GetPasswordResetTokenByToken(token);
        if (!existingToken?.token) {
            return { error: "Token does not exist or invalid!" };
        }

        const hasExpired = new Date(existingToken.expires) < new Date();
        if (hasExpired) {
            return { error: "Token has expired!" };
        }

        const existingUser = await GetUserByEmail(existingToken.emailHash);

        if (!existingUser) {
            return { error: "Email does not exist!" };
        }

        await prisma.user.update({
            where: { id: existingUser.id },
            data: {
            password: await hashString(password),
            },
        });

        await prisma.userPasswordResetToken.delete({
            where: { id: existingToken.id },
        });

        return { success: "Your password has been changed." };
        
    } catch (error) {
      return { error: `Error changing your password. ${error}`};
    }
};
