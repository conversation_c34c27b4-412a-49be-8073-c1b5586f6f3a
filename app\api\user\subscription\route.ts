import { NextRequest, NextResponse } from 'next/server'
import { useSession } from "next-auth/react";
import { prisma } from '@/lib/prisma/prismaClient';
import { hashEmail } from '@/utils/hashingHelpers';

export async function GET(req: NextRequest) {
  const { data: session } = useSession();
  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.user.findUnique({
    where: { emailHash: await hashEmail(session.user.email) },
    include: { subscriptions: { orderBy: { createdAt: 'desc' }, take: 1 } },
  })

  if (!user || user.subscriptions.length === 0) {
    return NextResponse.json({ subscription: null }, { status: 200 })
  }

  return NextResponse.json({ subscription: user.subscriptions[0] }, { status: 200 })
}
