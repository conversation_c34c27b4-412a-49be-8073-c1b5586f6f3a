'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function GlobalErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // You can log the error to an error reporting service here
    console.error("Error caught by global error boundary:", error);
  }, [error]);

  let title = 'Something Went Wrong';
  let message = "We're sorry, but an unexpected error occurred.";

  if (error.message === 'Access Denied') {
    title = 'Access Denied';
    message = 'You do not have permission to view this page.';
  } else if (error.message === 'Forbidden') {
    // Note: This global error.tsx primarily catches page rendering errors.
    // "Forbidden" from a server action needs client-side handling,
    // but if it somehow led to a page rendering error, it could be caught here.
    title = 'Action Forbidden';
    message = 'You do not have permission to perform this action.';
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      textAlign: 'center',
      padding: '20px',
      fontFamily: 'sans-serif'
    }}>
      <h1 className='text-wite'>{title}</h1>
      <p className='text-wite'>{message}</p>
      <p className='text-wite'>If the problem persists, please contact support.</p>
      <div style={{ marginTop: '20px' }}>
        <button
          onClick={() => reset()}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            cursor: 'pointer',
            border: '1px solid #ccc',
            background: '#f0f0f0'
          }}
        >
          Try Again
        </button>
        <Link href="/" style={{
            padding: '10px 20px',
            cursor: 'pointer',
            border: '1px solid #ccc',
            background: '#f0f0f0',
            textDecoration: 'none',
            color: 'black'
          }}>
          Go to Homepage
        </Link>
      </div>
    </div>
  );
}
