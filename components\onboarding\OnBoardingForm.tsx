"use client";

import { Suspense, useState } from "react";
import { UserTypeSelection } from "./UserTypeForm";
import { CompanyForm } from "./CompanyForm";
import { JobSeekerForm } from "./JobSeekerForm";
import { Card, CardContent } from "../ui/card";
import { useSession } from "next-auth/react";

type UserSelectionType = "company" | "jobSeeker" | null;

export function OnboardingForm() {    
    const { data: session, status: sessionStatus } = useSession({
        required: true,
    });

  const [step, setStep] = useState(1);
  const [userType, setUserType] = useState<UserSelectionType>(null);

  function handleUserTypeSelection(type: UserSelectionType) {
    setUserType(type);
    setStep(2);
  }

  function renderStep() {
    switch (step) {
      case 1:
        return <UserTypeSelection onSelect={handleUserTypeSelection} />;
      case 2:
        return userType === "company" ? <CompanyForm /> : <JobSeekerForm />;
      default:
        return null;
    }
  }

  return (
    <>
      <Card className="w-full h-full md:w-[600px] shadow-md border-1">
         <Suspense fallback={<p>Loading...</p>}>
            <CardContent className="pt-2 pr-10 pb-2">{renderStep()}</CardContent>
        </Suspense>
      </Card>
    </>
  );
}
