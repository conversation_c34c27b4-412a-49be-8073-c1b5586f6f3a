// utils/sendInngestEvent.ts
import { inngest } from "@/lib/inngest/client";

export async function sendInngestEvent({
  name,
  data,
}: {
  name: string;
  data: Record<string, unknown>;
}) {
  try {
    /**
     * By using the Inngest client's `send` method, we no longer need to manually
     * manage URLs or headers for different environments. The SDK handles it
     * automatically.
     */
    await inngest.send({
      name,
      data,
    });
    console.log(`[Inngest] ✅ Event sent: ${name}`);
  } catch (err) {
    console.error(`[Inngest] ❌ Error sending event:`, err);
  }
}
