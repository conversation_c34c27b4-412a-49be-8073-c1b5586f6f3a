"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { applyToJobPost } from "@/actions/job/applyJob";
import { Loader2 } from "lucide-react";

export function ApplyButton({ jobId, userId, hasApplied }: { jobId: string; userId: string; hasApplied: boolean }) {

    const [applied, setApplied] = useState(false);
  const [pending, setPending] = useState(false);

    useEffect(() => {
        if (hasApplied) {
            setApplied(true);
        }
    }, [hasApplied]);

    const handleApply = async () => {
        setPending(true);
        const data = await applyToJobPost(userId, jobId);
        if (data) {
            setApplied(true);
            toast.success("You have applied to this job successfully!");
        } else {
            toast.error("Failed to apply to this job.");
        }
        setPending(false);
    };

    return (
        <>
            <Button
                className="w-full cursor-pointer"
                disabled={pending || applied}
                variant={applied ? "outline" : "default"}
                onClick={async () => {
                    if (userId) {
                        await handleApply();
                    } else {
                        toast.error("Please sign in to apply for this job.");
                    }
                }}
            >
                {pending ? (
                    <>
                        <Loader2 className="size-4 animate-spin mr-2" />
                        Applying...
                    </>
                ) : (                        
                    applied ? "Applied already" : "Apply now"
                )}              
            </Button>
        </>
    );
}
