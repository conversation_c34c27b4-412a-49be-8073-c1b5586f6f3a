"use client";

import { z } from "zod";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { FcGoogle } from "react-icons/fc";
import { FaGithub, FaLinkedin } from "react-icons/fa";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { RegisterSchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useState, useTransition } from "react";
import { signup } from "@/actions/user/signup";
import { SuccessMessage } from "../info/SuccessMessage";
import { ErrorMessage } from "../info/ErrorMessage";
import { CardWrapper } from "../info/CardWrapper";
import { signIn } from "next-auth/react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export function SignUpForm() {
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const form = useForm<z.infer<typeof RegisterSchema>>({
    resolver: zodResolver(RegisterSchema),
    defaultValues: {
      email: "",
      password: "",
      firstname: "",
      lastname: "",
    },
  });

  const onSubmit = (values: z.infer<typeof RegisterSchema>) => {
    setError("");
    setSuccess("");

    startTransition(() => {
      signup(values).then((data) => {
        if (data?.success) {
          toast.success("Account created successfully. Redirecting to sign in page...");
          setSuccess(data.success);
          setTimeout(() => {
            router.push("/auth/signin");
          }, 1000);
        } else if (data?.error) {
          setError(data.error);
        } else {
          setError("An unexpected response was received from the server.");
        }
      }).catch(err => {
        console.error("Signup action error:", err);
        setError("An error occurred during signup. Please try again.");
      });
    });
  };

  const onSocialClick = (provider: "google" | "github" | "linkedIn") => {
    signIn(provider, { redirectTo: "/home/<USER>" });
  };

  return (
    <div>
      <CardWrapper
        captionLabel="Sign Up!"
        headerLabel="Create an account"
        backButtonLabel="Already have an account? Sign In"
        backButtonHref="/auth/signin"
        customClassName="w-full h-full md:w-[450px] shadow-md border-1"
      >
        <div className="flex text-muted-foreground text-medium items-center justify-center">
          <p>
            By signing up, you agree to our&nbsp;
            <a
              title="privacy"
              href="/privacy"
              target="_blank"
              className="text-blue-600 text-medium"
            >
              Privacy Policy
            </a>
            &nbsp;and&nbsp;
            <a
              title="terms"
              href="/terms"
              target="_blank"
              className="text-blue-600 text-medium"
            >
              Terms of Service
            </a>
            .
          </p>
        </div>
        <div className="mt-6 mb-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="firstname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Firstname</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Your first name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lastname</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Your last name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email <em className="text-xs text-muted-foreground">(Company user? Please use your work email.)</em></FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="<EMAIL>"
                          type="email"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />                
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password<em className="text-xs text-muted-foreground">(Minimum 8 alphanumeric characters.)</em></FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="********"
                          type="password"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <ErrorMessage message={error} />
              <SuccessMessage message={success} />
              <Button
                type="submit"
                disabled={isPending}
                size="lg"
                className="w-full cursor-pointer"
              >
                Sign Up
              </Button>
            </form>
          </Form>
        </div>
        <div className="text-medium items-center justify-center text-center">
          Or sign up using these services:
        </div>
        <div className="flex items-center justify-center gap-4 p-2">
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
              onSocialClick("google");
            }}
          >
            <FcGoogle className="mr-2 size-5" />
          </Button>
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
              onSocialClick("linkedIn");
            }}
          >
            <FaLinkedin className="mr-2 size-5 text-blue-500" />
          </Button>
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
              onSocialClick("github");
            }}
          >
            <FaGithub className="mr-2 size-5" />
          </Button>
        </div>
      </CardWrapper>
    </div>
  );
}
