"use client";

import { <PERSON><PERSON> } from "../ui/button";
import { ThemeToggle } from "@/components/theme/ThemeToggle";
import Link from "next/link";
import { UserMenu } from "./UserMenu";
import { Logo } from "./Logo";
import { UserRole, UserType } from "@prisma/client";
import { useSession } from "next-auth/react";
import { BeatLoader } from "react-spinners";
import { navLinks } from "@/data/constants";
import styles from "@/styles/style";
import { usePathname } from 'next/navigation';

export function Navbar() {
    const { data: session, status } = useSession();
    const pathname = usePathname();
    
    return (
      <nav className="flex w-full items-center justify-between py-6 text-muted-foreground pr-6">
        <Link href="/" className="flex items-center gap-2 w-[275px]">
          <Logo />
        </Link>

        <div className="flex items-center gap-6">
            <ul className="list-none sm:flex hidden justify-end items-center flex-1 pr-4">
                {navLinks.map((nav, index) => (
                <li
                    key={nav.id}
                    className={`font-poppins font-normal cursor-pointer text-[16px] text-white ${index === navLinks.length - 1 ? 'mr-0' : 'mr-10'}`}>
                    <a href={`${nav.link}`}>
                    {nav.title}
                    </a>
                </li>
                ))}
            </ul>
          {/* <ThemeToggle /> */}
          {session?.user?.userType === UserType.COMPANY && (
            <Link href="/home/<USER>/job/post" >
              <Button
                className={`${styles} py-4 px-6 bg-blue-gradient font-poppins font-medium text-[18px] text-primary outline-none rounded-[10px] hover:translate-x-2  transition-all ease-linear cursor-pointer`}
              >
                Post Job
            </Button>
            </Link>
          )}
          {session?.user?.userType === UserType.JOB_SEEKER && (
            pathname !== "/public/job/list" && (
                <Link href="/public/job/list" >
                    <Button
                        className={`${styles} py-4 px-6 bg-blue-gradient font-poppins font-medium text-[18px] text-primary outline-none rounded-[10px] hover:translate-x-2  transition-all ease-linear cursor-pointer`}
                    >
                        Apply Job
                    </Button>
                </Link>
            )
          )}
          {status === "loading" ? (
            <BeatLoader color="#00BFFF" loading={true} />
          ) : status === "authenticated" && session?.user ? (
            <UserMenu
              email={session.user.email ?? ""} // session.user is guaranteed by isAuthenticated
              name={session.user.name ?? ""}
              image={session.user.image ?? ""}
              role={session.user.role ?? UserRole.USER}
              type={session.user.userType ?? UserType.JOB_SEEKER}
              onboarded={session.user.onboarded ?? false}
            />
          ) : (
            <div className="flex gap-4">
                { pathname !== "/auth/signin" && (
                                                <Link
                    href="/auth/signin"
                    >
                    <Button
                        className={`${styles} py-4 px-6 bg-blue-gradient font-poppins font-medium text-[18px] text-primary outline-none rounded-[10px] hover:translate-x-2  transition-all ease-linear cursor-pointer`}
                    >
                    Sign In
                    </Button>
                    </Link>
                )}
                {/* { pathname !== "/auth/signup" && (
                    <Link
                    href="/auth/signup"
                    >
                    <Button
                        className={`${styles} py-4 px-6 bg-purple-gradient font-poppins font-medium text-[18px] text-primary outline-none rounded-[10px] hover:translate-x-2 transition-all ease-linear cursor-pointer`}
                    >
                    Sign Up
                    </Button>
                    </Link>
                )} */}
            </div>
          )}
        </div>
      </nav>
    );
}
