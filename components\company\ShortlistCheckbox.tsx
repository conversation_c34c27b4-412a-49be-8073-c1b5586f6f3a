"use client";

import { useState } from "react";
import { Checkbox } from "../ui/checkbox";
import { ToggleShortlistResume } from "@/actions/resume/userResumeActions";
import { toast } from "sonner";

interface ShortlistCheckboxProps {
  jobId: string;
  resumeId: string;
  initialShortlisted: boolean;
}

export function ShortlistCheckbox({
  jobId,
  resumeId,
  initialShortlisted,
}: ShortlistCheckboxProps) {
  const [isShortlisted, setIsShortlisted] = useState(initialShortlisted);
  const [isLoading, setIsLoading] = useState(false);

  const handleShortlistToggle = async (checked: boolean) => {
    setIsLoading(true);
    // Optimistically update UI
    setIsShortlisted(checked);

    try {
      // Call API to update shortlist status
      await ToggleShortlistResume(jobId, resumeId, checked);
      toast.success(
        checked ? "Resume shortlisted" : "Resume removed from shortlist"
      );
    } catch (error) {
      console.error("Error toggling shortlist:", error);

      // Revert UI on error
      setIsShortlisted(!checked);

      toast.error("Failed to update shortlist status");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Checkbox
      checked={isShortlisted}
      onCheckedChange={(checked) => handleShortlistToggle(checked === true)}
      disabled={isLoading}
      aria-label="Shortlist candidate"
      title="Shortlist candidate"
      className="cursor-pointer"
    />
  );
}
