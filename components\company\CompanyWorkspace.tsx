"use client";

import {
  Card,
  CardTitle,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useCompanyActions } from "@/hooks/useCompanyActions";
import { formatCurrencyWithDecimals } from "@/utils/formatCurrency";
import { AlertDialogDelete } from "../general/AlertDialogDelete";
import { useState } from "react";
import { toast } from "sonner";
import { SetUserStatus } from "@/actions/user/setUserStatus";
import { UserRole, UserStatus } from "@prisma/client";
import { normalizeHeaders } from "@/utils/stringHelpters";

export function CompanyWorkspace({userId, companyId}: {userId: string, companyId: string}) {

    const [pending, setPending] = useState(false);
    const [deleting, setDeleting] = useState(false);

    const { companyTeam, companyPlan, companyInfoQuery } = useCompanyActions(companyId, userId);

        const onDelete = async (id: string) => {
            try {
                setPending(true);
                setDeleting(true);
                
                const result = await SetUserStatus(id, UserStatus.INACTIVE);
                if (result?.success) {
                    toast.success(result.success);
                    companyInfoQuery.refetch(); 
                } else {
                    toast.error(result?.error || "Failed to set status.");
                }

            } catch (error) {
                console.error("Error setting status:", error);
                toast.error("An unexpected error occurred.");            
            } finally {
                setPending(false);
                setDeleting(false);
            }
      }

    return (
        <div className="flex w-full items-center justify-start">            
            <div className="w-full grid grid-cols-3 gap-2">
                <div className="flex w-full">
                    {companyTeam ? (                        
                        <CompanyTeam team={companyTeam} onDelete={onDelete} />
                    ): (
                        <em>No team found</em>
                    )}
                </div>
                <div className="flex w-full">
                    {companyPlan ? (                        
                        <CompanyPlan plan={companyPlan} />
                    ): (
                        <em>No team found</em>
                    )}
                </div>
                <div className="flex w-full">
                            
                </div>
            </div>
        </div>
    );
};

function CompanyTeam({team, onDelete}: {team: any, onDelete: (id: string) => void }) {

    return (
        <>
         <Card className="flex w-full justify-start">
                <CardHeader>
                    <CardTitle>Your Team</CardTitle>
                    <CardDescription>Employees that have access to Athena.</CardDescription>
                    <CardContent className="w-full p-0">
                        <Table className="w-full text-xs">
                            <TableHeader>
                                <TableRow className="w-full">
                                    <TableHead>Joined</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Posts</TableHead>
                                    <TableHead>Role</TableHead>
                                    <TableHead>Del</TableHead>
                                </TableRow> 
                            </TableHeader> 
                            <TableBody className="w-full text-xs">
                                {team?.map((item: any) => (
                                    <TableRow key={item.id}>
                                        <TableCell>{item.createdAt.toLocaleDateString()}</TableCell>
                                        <TableCell>{item.firstName} {item.lastName}</TableCell>
                                        <TableCell className="text-left">{item.jobCount}</TableCell>
                                        <TableCell className="text-left">{normalizeHeaders(item.role)}</TableCell>
                                        <TableCell>
                                            {item.role !== UserRole.ADMIN && (
                                                <AlertDialogDelete id={item.id} title="user" description="This will deactivate user and removed from your team. Company data entered by this user will remain and accessible to you and your team." onDelete={onDelete} />
                                            )}
                                        </TableCell>
                                    </TableRow>
                               ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </CardHeader>
            </Card>
        </>
    );
};



function CompanyPlan({plan}: {plan: any}) {
    return (
        <>
         <Card className="flex w-full justify-start">
                <CardHeader>
                    <CardTitle>Your Plan</CardTitle>
                    <CardDescription>Your organization's subscribed plan.</CardDescription>
                    <CardContent className="w-full p-0">
                        <div className="flex flex-col w-full text-sm text-muted-foreground space-y-1 pt-2">
                                <div className="flex">
                                    <span className="font-semibold w-20">Plan</span>
                                    <span>: {plan?.plan?.planName}</span>
                                </div>
                                <div className="flex">
                                    <span className="font-semibold w-20">Mode</span>
                                    <span>: {plan?.paymentFrequency}</span>
                                </div>
                                <div className="flex">
                                    <span className="font-semibold w-20">Price</span>
                                    <span>: {formatCurrencyWithDecimals(plan?.amount, plan?.baseCurrencyCode)}</span>
                                </div>
                                <div className="flex">
                                    <span className="font-semibold w-20">Local</span>
                                    <span>: {formatCurrencyWithDecimals(plan?.amount * plan?.exchangeRate, plan?.exchangeCurrencyCode)}</span>
                                </div>
                                <div className="flex">
                                    <span className="font-semibold w-20">Exchange</span>
                                    <span>: {formatCurrencyWithDecimals(plan?.exchangeRate, plan?.exchangeCurrencyCode)}</span>
                                </div>
                                <div className="flex">
                                    <span className="font-semibold w-20">Date</span>
                                    <span>: {plan?.createdAt.toLocaleDateString()}</span>
                                </div>
                        </div>
                    </CardContent>
                </CardHeader>
            </Card>
        </>
    );
};