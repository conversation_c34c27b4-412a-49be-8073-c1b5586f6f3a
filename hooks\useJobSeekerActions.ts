import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Resume, ResumeData } from "@/lib/resume/resumeActions";
import {
  CheckResumeUsername,
  GetUserOriginalResume,
  GetUserResume,
  GetUserResumeUsername,
  UpdateResume,
  UpdateResumeStatus,
  UpdateResumeUsername,
} from "@/actions/resume/userResumeActions";
import { PublishStatus } from "@prisma/client";
import { GetUserJobSeekerProfile } from "@/actions/user/getUser";
import { ResumeDataSchema } from "@/data/zod/resumeZod";

// Fetch resume data
const fetchResume = async (
  userId: string
): Promise<{
  resume: Resume | undefined;
}> => {
  try {
    const data = await GetUserResume(userId);
    console.log({resumeQueryData: data});
    return { resume: data };
  } catch (error) {
    console.error("Resume fetch error:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch resume"
    );
  }
};

type UsernameResponse = {
  username: string;
};

const fetchUsername = async (userId: string): Promise<UsernameResponse> => {
  try {
    const data = await GetUserResumeUsername(userId);
    return {
      username: data?.username || "",
    };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch username"
    );
  }
};

const fetchOriginalResume = async (userId: string) => {
  try {
    const data: any = await GetUserOriginalResume(userId);
    return {
      fileName: data?.fileName || "",
      url: data?.url || "#",
    };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch data"
    );
  }
};

const fetchJobSeekerData = async (userId: string) => {
  try {
    const data: any = await GetUserJobSeekerProfile(userId);
    return data;
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch data"
    );
  }
};

const updateResumeStatus = async (resumeId: string, status: PublishStatus) => {
  try {
    const data = await UpdateResumeStatus(resumeId, status);
    return { data };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to update status"
    );
  }
};

type UpdateResponse = {
  success: boolean;
  message: string;
};

const updateResumeUsername = async (
  resumeId: string,
  username: string
): Promise<UpdateResponse> => {
  try {
    const data = await UpdateResumeUsername(resumeId, username);
    return {
      success: !!data,
      message: data
        ? "Username updated successfully"
        : "Username not available",
    };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to update username"
    );
  }
};

type UsernameAvailabilityResponse = {
  available: boolean;
  message?: string;
};

const checkResumeUsername = async (
  username: string
): Promise<UsernameAvailabilityResponse> => {
  try {
    const isAvailable = await CheckResumeUsername(username);
    return {
      available: isAvailable,
      message: isAvailable
        ? "Username is available"
        : "Username is already taken",
    };
  } catch (error) {
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to check username availability"
    );
  }
};

const updateResume = async (resume: Resume): Promise<UpdateResponse> => {
  try {
    const data = await UpdateResume(resume);
    return {
      success: !!data,
      message: data
        ? "Username updated successfully"
        : "Username not available",
    };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : "Failed to update username"
    );
  }
};

export function useUserActions(username?: string, enabled: boolean = false) {
  const queryClient = useQueryClient();
  const { data: session } = useSession();
  const userId = session?.user?.id as string;

  // Fetch user's resume
  const resumeQuery = useQuery({
    queryKey: ["resume", userId],
    queryFn: () => {
      if (!userId) return null;
      return fetchResume(userId);
    },
    enabled: !!userId,
    staleTime: 30000,
  });

  // Fetch current username
  const usernameQuery = useQuery({
    queryKey: ["username", userId],
    queryFn: () => fetchUsername(session?.user?.id as string),
    enabled: !!session?.user?.id,
    staleTime: 30000,
  });

  // Toggle resume publish status
  const toggleStatusMutation = useMutation({
    mutationFn: async (newPublishStatus: PublishStatus) => {
      if (!resumeQuery.data?.resume?.id) return;
      return await updateResumeStatus(
        resumeQuery.data.resume.id,
        newPublishStatus
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["resume"] });
    },
  });

  type UpdateUsernameVariables = {
    newUsername: string;
    resumeId: string;
  };

  // Update username
  const updateUsernameMutation = useMutation<
    UpdateResponse,
    Error,
    UpdateUsernameVariables
  >({
    mutationFn: async ({ newUsername, resumeId }) => {
      return updateResumeUsername(resumeId as string, newUsername);
    },
    onSuccess: (data) => {
      if (data.success) {
        queryClient.invalidateQueries({ queryKey: ["username"] });
      }
    },
    onError: (error) => {
      console.error("Failed to update username:", error);
    },
  });

  // ✅ Check username availability using useQuery
  const checkUsername = useQuery({
    queryKey: ["checkUsername", username],
    queryFn: () => checkResumeUsername(username!),
    enabled: enabled && !!username && username.length > 2,
    staleTime: 30000,
    refetchOnWindowFocus: false,
  });

  const originalResume = useQuery({
    queryKey: ["originalResume", userId],
    queryFn: () => {
      if (!userId) return null;
      return fetchOriginalResume(userId);
    },
    enabled: !!userId,
    staleTime: 30000,
  });

  const jobSeekerData = useQuery({
    queryKey: ["jobSeekerData", userId],
    queryFn: () => {
      if (!userId) return null;
      return fetchJobSeekerData(userId);
    },
    enabled: !!userId,
    staleTime: 30000,
  });

  // Function to save resume data changes
  const saveResumeDataChanges = async (newResumeData: ResumeData) => {
    // Validate the resume data using Zod schema
    try {
      // Validate the resume data
      ResumeDataSchema.parse(newResumeData);

      // If validation passes, update the resume
      if (!resumeQuery.data?.resume) {
        throw new Error("No resume found to update");
      }

      const updatedResume: Resume = {
        ...resumeQuery.data.resume,
        resumeData: newResumeData,
      };

      await updateResume(updatedResume);

      return { success: true };
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Validation failed: ${error.message}`);
      }
      throw error;
    }
  };

  // Mutation for saving resume data changes
  const saveResumeDataMutation = useMutation({
    mutationFn: saveResumeDataChanges,
    onSuccess: () => {
      // Invalidate and refetch resume data
      queryClient.invalidateQueries({ queryKey: ["resume"] });
    },
  });

  return {
    resumeQuery,
    usernameQuery,
    toggleStatusMutation,
    updateUsernameMutation,
    checkUsername,
    originalResume,
    jobSeekerData,
    saveResumeDataMutation,
  };
}
