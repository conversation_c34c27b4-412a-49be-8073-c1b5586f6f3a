"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { request } from "@arcjet/next";
import { redirect } from "next/navigation";
import { auth } from "@/lib/auth/auth";
import {
  getCompanyData,
  getCompanyFilesData,
  getCompanyInfoData,
  getCompanyJobListData,
  getCompanyResumesData,
  getCompanyStatsData,
  SaveCompanyLogoData,
} from "@/data/company/company";

export const SearchCompany = async (searchString: string) => {
  const session = await auth();

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  if (!searchString) {
    return { error: "Missing search string." };
  }

  try {
    // Use "contains" for partial matching
    const companies = await prisma.company.findMany({
      where: {
        name: { contains: searchString, mode: "insensitive" },
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (companies.length > 0) {
      return companies;
    } else {
      return { error: "Company not found" };
    }
  } catch (error) {
    console.error(error);
    return { error: "Error encountered fetching companies." };
  }
};

export const GetCompanies = async () => {
  const session = await auth();

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    // Use "contains" for partial matching
    const companies = await prisma.company.findMany({
      orderBy: {
        name: "asc",
      },
        include: {      
            _count: {
                select: {
                    jobPosts: true, // This will count all job posts for this company
                },
            },
            plan: {
                include: {
                    plan: true,
                },
            }
        }
    });

    return companies;
  } catch (error) {
    // console.error(error);
    return null;
  }
};

export async function GetCompany(id: string) {
  const session = await auth();

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  if (!id) {
    return null;
  }

  try {
    const company = await getCompanyData(id);
    // console.log({COMPANY: company})
    return company;
  } catch (error) {
    console.error("Error fetching company:", error);
    return null;
  }
}

export async function GetCompanyResumes(companyId: string) {
  const session = await auth();

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getCompanyResumesData(companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    //   console.error("Error in getJobApplicants:", error);
    return [];
  }
}

export async function GetCompanyFiles(id: string) {
  const session = await auth();

  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getCompanyFilesData(id);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    //   console.error("Error in getJobApplicants:", error);
    return [];
  }
}

export async function getCompanyJobList(companyId: string) {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  try {
    const data = await getCompanyJobListData(companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    //   console.error("Error in getJobApplicants:", error);
    return [];
  }
}

export async function SaveCompanyLogo(companyId: string, url: string) {
  // Added authentication and Arcjet protection
  const session = await auth();
  
  if (!session?.user?.id) {
    // Or handle as per your middleware strategy, e.g., throw new Error("Unauthorized");
    // This assumes middleware might not cover this specific action directly,
    // or as a defense-in-depth. If covered by middleware, this error indicates an issue.
    return { error: "Error! User not authenticated." };
  }

  await ensureServerActionProtection();

  if (!companyId) {
    return { error: "Error! Missing company ID." };
  }

  try {
    const result = await SaveCompanyLogoData(companyId, url);

    if (result) {
      return {
        success: "Success! Logo was saved.",
      };
    } else {
      return {
        error: "Error! Failed to save logo.",
      };
    }
  } catch (error) {
    console.error("Error fetching company:", error);
    return {
      error: `Error! Failed to save logo ${error}`,
    };
  }
}

export async function GetCompanyStats(companyId: string) {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  try {
    const companyStats = await getCompanyStatsData(companyId);
    return companyStats;
  } catch (error) {
    return null;
  }
}

export async function GetCompanyInfo(companyId: string, userId?: string) {
  const session = await auth();
  if (!session?.user) {
    return redirect("/auth/signin");
  }

  // Arcjet protection
  await ensureServerActionProtection();
 
  try {
    const companyStats = await getCompanyInfoData(companyId, userId);
    return companyStats;
  } catch (error) {
    return null;
  }
}
