'use client';

import { title } from 'process';
import { useEffect } from 'react';
import { toast } from "sonner";

declare global {
  interface Window {
    paypal?: any;
  }
}

interface Props {
  planID: string;
  containerId: string;
  onSuccess?: (subscriptionID: string) => void;
}

export default function PayPalSubscribeButton({ planID, containerId, onSuccess }: Props) {
    console.log({PAYPAL: planID, containerId});
    useEffect(() => {
        const interval = setInterval(() => {
            if (window.paypal) {
            clearInterval(interval)

            const alreadyRendered = document.getElementById(containerId)?.childElementCount
            if (alreadyRendered) return

            window.paypal.Buttons({
                style: { shape: 'pill', color: 'gold', layout: 'horizontal', label: 'subscribe', fundingicons: true, tagline: false },
                createSubscription: (data: any, actions: any) => {
                    return actions.subscription.create({ plan_id: planID })
                },
                onApprove: function (data: any, actions: any) {
                    console.log('Subscription approved:', data.subscriptionID)
                    toast.success('PayPal is processing your subscription (ID: ' + data.subscriptionID + ')');

                    if (onSuccess) {
                        onSuccess(data.subscriptionID)
                    }
                },
            }).render(`#${containerId}`)
            }
        }, 200)

        return () => clearInterval(interval)
    }, [planID, containerId]);

  return <div id={containerId} />;
}