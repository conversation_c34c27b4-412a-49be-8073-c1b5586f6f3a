"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import UsernameEditorView from "./UsernameEditorView";
import { cn } from "@/lib/utils";
import { getSiteUrl } from "@/utils/getSiteUrl";
import Image from "next/image";
import { PublishStatus } from "@prisma/client";

export default function PreviewActionbar({
  resumeId,
  initialUsername = "",
  prefix = "athena.edisonaix.com/",
  status,
  onStatusChange,
  isChangingStatus,
}: {
  resumeId: string;
  initialUsername: string;
  prefix?: string;
  status?: PublishStatus;
  onStatusChange?: (newStatus: PublishStatus) => Promise<void>;
  isChangingStatus?: boolean;
}) {
    
  const [isEditorOpen, setIsEditorOpen] = useState(false);

  const handleStatusChange = async () => {
    if (onStatusChange) {
      // Toggle the status
      const newStatus =
        status === PublishStatus.DRAFT
          ? PublishStatus.LIVE
          : PublishStatus.DRAFT;
      await onStatusChange(newStatus);
    }
  };

  return (
    <>
      <div className="w-full rounded-lg bg-[#fcfcfc] border-[0.5px] border-neutral-300 flex items-center justify-between py-3 px-5 sm:px-4 sm:py-2.5 sm:flex-row gap-4">
        <div className="flex sm:flex-row items-center gap-4 w-full">
          <div className="flex items-center gap-1 mr-1">
            <Image
              alt="link-icon"
              src="/icons/link-icon.png"
              width={16} // Added width
              height={16} // Added height
              className={cn(
                "w-4 h-4 text-design-black ",
                status === PublishStatus.LIVE && "cursor-pointer"
              )}
              onClick={() => {
                if (!initialUsername || status !== PublishStatus.LIVE) return;
                const portofolioUrl = getSiteUrl(initialUsername);
                navigator.clipboard.writeText(portofolioUrl);
                toast.success("Copied link to your website");
              }}
            />
            <p className="text-sm text-design-black">{prefix}</p>
          </div>

          <div className="overflow-hidden rounded bg-white border-[0.5px] border-neutral-300 flex flex-row w-80">
            <span className="flex-1 p-3 text-sm text-[#5d5d5d] border-none outline-none focus:ring-0 bg-transparent w-fit truncate">
              {initialUsername}
            </span>

            <Button
              variant="ghost"
              size="icon"
              className="size-[44px] flex items-center justify-center border-l-[0.5px] cursor-pointer"
              onClick={() => setIsEditorOpen(true)}
            >
              <Pencil className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <div
                className="size-1.5 rounded-full"
                style={{
                  backgroundColor:
                    status === PublishStatus.DRAFT ? "#B98900" : "#009505",
                }}
              />
              <p
                className={cn(
                  "text-[10px] font-bold uppercase",
                  status === PublishStatus.DRAFT
                    ? "text-[#B98900]"
                    : "text-[#009505]"
                )}
              >
                {status}
              </p>
            </div>

            <Button
              key={status}
              variant="default"
              disabled={isChangingStatus}
              onClick={handleStatusChange}
              className="flex items-center bg-primary min-w-[100px] min-h-8 gap-1.5 px-3 py-1.5 h-auto cursor-pointer"
            >
              {isChangingStatus ? (
                <>
                  <span className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                </>
              ) : (
                <span className="text-sm">
                  {status === PublishStatus.DRAFT ? "Publish" : "Unpublish"}
                </span>
              )}
            </Button>
            {status === PublishStatus.LIVE && (
              <Button className="flex items-center min-w-[100px] min-h-8 gap-1.5 px-3 py-1.5 h-auto">
                <a
                  href={`${getSiteUrl(initialUsername)}`}
                  target="_blank"
                  rel="noreferrer"
                >
                  Visit Site
                </a>
              </Button>
            )}
          </div>
        </div>
      </div>

      <UsernameEditorView
        resumeId={resumeId}
        initialUsername={initialUsername}
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        prefix={prefix}
      />
    </>
  );
}
