import styles, { layout } from "@/styles/style";
import Image from "next/image";
import { features } from "@/data/constants";
import { FeatredCardProps } from "@/types";

const FeaturesCard: React.FC<FeatredCardProps> = ({ icon, title, content, index }) => (
  <div
    className={`flex flex-row p-4 rounded-[20px] ${index !== features.length - 1 ? "mb-0" : "mb-0"
      } feature-card`}
  >
    <div
      className={`w-[64px] h-[64px] rounded-full bg-dimBlue ${styles.flexCenter}`}
    >
      <Image src={icon} alt="icon" className="w-[50%] h-[50%] object-contain" />
    </div>
    <div className="flex-1 flex flex-col ml-3">
      <h4 className="font-poppins font-semibold text-white text-[18px] leading-[24px]">
        {title}
      </h4>
      <p className="font-poppins font-normal text-dimWhite text-[16px] leading-[24px]">
        {content}
      </p>
    </div>
  </div>
);
const Business: React.FC = () => {

  return (
    <section id="features" className={`${layout.section}`}>
      <div className={layout.sectionInfo}>
        <h2 className={styles.heading2}>
          Focus on growing your business — our AI handles the candidate matching, so you get quality hires without the hassle.
        </h2>
        <p className={`${styles.paragraph} max-w-[470px] text-md text-muted-foreground mt-5`}>
          Fire up your hiring. AI-matched talent, no fluff — just results.
        </p>
      </div>
      <div className={`${layout.sectionImg} flex-col items-start justify-start text-muted-foreground`}>
        {features.map((feature, index) => (
          <FeaturesCard key={feature.id} {...feature} index={index} />
        ))}
      </div>
    </section>
  );
};

export default Business;
