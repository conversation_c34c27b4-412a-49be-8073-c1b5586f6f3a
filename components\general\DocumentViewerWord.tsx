"use client";

import React from 'react';
import { DocumentViewer } from 'react-documents';

interface ObjectDocumentViewerProps {
  documentUrl: string;
  title?: string;
  width?: string;
  height?: string;
}

export const DocumentViewerWord = ({
  documentUrl,
  title = "Word Document Viewer",
  width = "100%",
  height = "1000px"
}: ObjectDocumentViewerProps) => {
  // The DocumentViewer component itself might not directly accept width/height props.
  // We'll wrap it in a div and apply the dimensions there.
  // Convert empty string to null to prevent the iframe src="" warning
    const urlToPass = documentUrl || undefined;
  return (
    <div style={{ width, height, backgroundColor: 'white' }} aria-label={title}>
      <DocumentViewer
        queryParams="en-US"
        url={urlToPass}
        overrideLocalhost="" 
      />
    </div>
  );
};