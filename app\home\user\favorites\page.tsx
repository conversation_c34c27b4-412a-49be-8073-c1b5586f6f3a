import { getFavoriteJobs } from "@/actions/job/getJob";
import { EmptyState } from "@/components/general/EmptyState";
import { MainPagination } from "@/components/general/MainPagination";
import { UserJobFavorites } from "@/components/user/UserJobFavorites";
import { auth } from "@/lib/auth/auth";

const FAVORITES_PAGE_SIZE = 10; // Define page size

export default async function FavoritesPage(props: {
  // If this page also used route params, they would be typed similarly:
  // params: Promise<{ [key: string]: string | string[] | undefined }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const session = await auth();
  const resolvedSearchParams = await props.searchParams;
  const currentPage = Number(resolvedSearchParams?.page) || 1;

  if (!session?.user?.id) {
    return <div>User not authenticated.</div>;
  }

  const result = await getFavoriteJobs(session.user.id, currentPage, FAVORITES_PAGE_SIZE);

  if (result?.error || !result?.favorites || result.favorites.length === 0) {
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <h1 className="text-2xl font-semibold">Favorite Jobs</h1>
        <EmptyState
          title="No Favorites Yet"
          description={result?.error || "You haven't saved any jobs as favorites yet."}
          buttonText="Find a Job"
          href="/public/job/list"
        />
      </div>
    );
  }

  return (
      <div className="grid grid-cols-1 mt-5 gap-4">
          <UserJobFavorites data={result.favorites} />
          {result.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <MainPagination totalPages={result.totalPages} currentPage={currentPage} />
            </div>
          )}
      </div>
  );
}
