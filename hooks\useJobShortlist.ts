import { getJobShortlistedApplicants, getUserJobShortlist } from "@/actions/job/getJob";
import { useQuery } from "@tanstack/react-query";

const fetchJobShortlist = async (jobId: string): Promise<any[]> => {
  try {
    const data = await getJobShortlistedApplicants(jobId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

const fetchUserJobShortlist = async (jobId?: string, resumeId?: string): Promise<any[]> => {
  try {
    const data = await getUserJobShortlist(jobId as string, resumeId as string);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

export function useUserJobShortlist(jobId?: string, resumeId?: string) {

  const jobShortlistQuery = useQuery({
    queryKey: ["jobShortlist", jobId],
    queryFn: () => {
      if (!jobId) return null;
      return fetchJobShortlist(jobId as string);
    },
    enabled: !!jobId,
    staleTime: 30000,
  });

    const userShortlistQuery = useQuery({
    queryKey: ["userShortlist", jobId, resumeId],
    queryFn: () => {
      if (!resumeId) return null;
      return fetchUserJobShortlist(jobId as string, resumeId as string);
    },
    enabled: !!jobId && !!resumeId,
    staleTime: 30000,
  });

  return {
    jobShortlistQuery,
    userShortlistQuery
  };
}
