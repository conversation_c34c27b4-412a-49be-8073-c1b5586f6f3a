
import pdf from 'pdf-parse-new';
import mammoth from 'mammoth';

export async function handleFileExtraction(userFile: { fileType: string; url: string }) {
  const fileType = userFile.fileType.toLowerCase();
  let extractedText = '';

  if (!userFile.url) {
    console.error('File URL is missing');
    return '';
  }

  switch (fileType) {
    case 'pdf':
      extractedText = await extractPdfFromUrl(userFile.url);
    //   console.log({ extractedTextPDF: extractedText });
      break;

    case 'docx':
      extractedText = await extractDocxFromUrl(userFile.url);
    //   console.log({ extractedTextDOCX: extractedText });
      break;

    case 'doc':
      console.warn('.doc files are not supported with current parser');
      break;

    default:
      console.warn('Unsupported file type:', fileType);
  }

  return extractedText;
};

export async function extractPdfFromUrl(url: string) {
  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Failed to fetch PDF: ${res.statusText}`);
    const buffer = await res.arrayBuffer();
    const data = await pdf(Buffer.from(buffer));
    return data.text;
  } catch (error) {
    console.error('PDF extraction error:', error);
    return '';
  }
};

export async function extractDocxFromUrl(url: string) {
  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Failed to fetch DOCX: ${res.statusText}`);
    const buffer = await res.arrayBuffer();
    const result = await mammoth.extractRawText({ buffer: Buffer.from(buffer) });
    return result.value;
  } catch (error) {
    console.error('DOCX extraction error:', error);
    return '';
  }
};

export async function getResumeHeaderChunk(text: string, maxLines = 40): Promise<string> {
  return text
    .split('\n')
    .map(l => l.trim())
    .filter(Boolean)
    .slice(0, maxLines)
    .join('\n');
};

export async function flattenText(raw: string): Promise<string> {
    //This collapses all whitespace (including \n, \t, etc.) into single spaces and trims both ends.
  return raw.replace(/\s+/g, ' ').trim();
};

export async function lightlyFlatten (raw: string): Promise<string> {
    // when you want readable structure without all the jagged formatting from doc extraction.
  return  raw
    .split('\n')
    .map(line => line.trim())
    .filter(Boolean)
    .join('\n'); // preserves soft breaks but removes noise
};



