import { JobFilter } from "@/components/job/JobFilters";
import { JobListingLoading } from "@/components/job/JobListingLoading";
import { JobListings } from "@/components/job/JobListings";
import { Suspense } from "react";
import { ensurePageProtection } from "@/lib/arcjet/protection";
import { auth } from "@/lib/auth/auth";

type SearchParams = {
  searchParams: Promise<{
    page?: string;
    jobTypes?: string;
    location?: string;
  }>;
};

export default async function JobListPage({ searchParams }: SearchParams) {
  const params = await searchParams;
  const currentPage = params.page ? parseInt(params.page) : 1;
  const jobTypes = params.jobTypes?.split(",") || [];
  const location = params.location || "";
  const filterKey = `page=${currentPage};types=${jobTypes.join(",")};location=${location}`;
  
  const session = await auth();

  await ensurePageProtection(!!session, { requested: 10 });

  return (
    <div className="flex flex-col md:flex-row gap-8 p-6">
      <div className="w-full md:w-[300px] shrink-0">
        <JobFilter />
      </div>
      <div className="flex-1 flex flex-col gap-6">
        <Suspense fallback={<JobListingLoading />} key={filterKey}>
          <JobListings currentPage={currentPage} jobTypes={jobTypes} location={location} />
        </Suspense>
      </div>
    </div>
  );
}
