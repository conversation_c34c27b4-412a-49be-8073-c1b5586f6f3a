"use client";

import { useCompanyActions } from "@/hooks/useCompanyActions";
import { searchResume } from "@/actions/resume/searchResume";
import { useSession } from "next-auth/react";
import { useEffect, useState, ChangeEvent, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import PriceSelectTable from "@/components/general/PriceSelect";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "@/components/ui/dialog";
import { LoadingStateHorizontal } from "@/components/general/LoadingStateHorizontal";
import { CurrencyData } from "@/utils/currencyUtils";
import { UserPlan } from "@/types/customTypes";
import { MainPagination } from "@/components/general/MainPagination";
import { setUserPlan } from "@/actions/user/userActions";
import { toast } from "sonner";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { Building2, FileUser, <PERSON>Check, PenBoxIcon } from "lucide-react";
import { normalizeHeaders } from "@/utils/stringHelpters";
import { ResumeData } from "@/data/zod/resumeZod";
import { verifyEmail } from "@/actions/user/verifyEmail";
import { Button, buttonVariants } from "@/components/ui/button";

// Define a type for resume search results
interface ResumeSearchResult {
  id: string;
  name: string;
  picture: string | null; // Add the picture property
  resume: ResumeData; // The parsed resume data object
  createdAt: string; // Added createdAt for upload date
}

interface CompanyDashboardCardProps {
  title: string;
  description: string;
  link: string;
  linkText: string;
  icon?: React.ReactNode;
}

const CompanyDashboardCard = ({
  title,
  description,
  link,
  linkText,
  icon,
}: CompanyDashboardCardProps) => (
  <div className="bg-white shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 flex flex-col">
    <div className="flex items-center mb-3">
      {icon && <span className="mr-3 text-blue-600 text-2xl">{icon}</span>}
      <h2 className="text-xl font-semibold text-gray-700">{title}</h2>
    </div>
    <p className="text-gray-600 mb-4 flex-grow">{description}</p>
    <Link
      href={link}
      className="mt-auto text-blue-600 hover:text-blue-800 font-medium hover:underline self-start"
    >
      {linkText} &rarr;
    </Link>
  </div>
);

export default function CompanyDashboardPage() {
  const { data: session, status: sessionStatus, update } = useSession();
  const [isPriceDialogForcedOpen, setIsPriceDialogForcedOpen] = useState(false);
  const [isPlanUpdating, setIsPlanUpdating] = useState(false);
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<ResumeSearchResult[]>([]);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isLoadingSearch, setIsLoadingSearch] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  //   console.log({DASHBOARD: session, sessionStatus});
  const user = session?.user;
  // console.log({DASHBOARDuser: user});

  useEffect(() => {
    // Log current state when effect runs for testing/debugging
    console.log(
      "Dashboard useEffect triggered. Status:",
      sessionStatus,
      "Session User:",
      session?.user,
      "Current planSet:",
      session?.user?.planSet
    );

    if (sessionStatus === "loading") {
      // Session is still loading, so defer any decision about the dialog
      console.log(
        "Dashboard useEffect: Session loading, deferring dialog state."
      );
      // setIsPriceDialogForcedOpen(false); // Optionally ensure it's closed while loading
      return;
    }

    if (sessionStatus === "authenticated" && session?.user) {
      // Session is loaded and authenticated, now check planSet
      const shouldBeOpen = !session.user.planSet;
      console.log(
        "Dashboard useEffect: Authenticated. planSet:",
        session.user.planSet,
        "Setting dialog open to:",
        shouldBeOpen
      );
      setIsPriceDialogForcedOpen(shouldBeOpen);
    }
    // If sessionStatus is "unauthenticated", the layout's useSession hook should handle redirection.
  }, [session, sessionStatus, setIsPriceDialogForcedOpen]); // Dependencies now include sessionStatus

  // Debounce search term
  useEffect(() => {
    const timerId = setTimeout(() => {
      // Do not set debounced search term if the price dialog is open,
      // as search is not relevant then.
      if (isPriceDialogForcedOpen) return;

      // Only trigger search if term is empty (to clear results) or >= 2 chars
      if (searchTerm.trim().length >= 2 || searchTerm.trim().length === 0) {
        setDebouncedSearchTerm(searchTerm.trim());
      } else if (searchTerm.trim().length < 2 && searchTerm.trim().length > 0) {
        // If less than 2 chars but not empty, clear results and show a message or do nothing
        setSearchResults([]);
        setSearchError(null); // Or "Type at least 2 characters"
        setTotalPages(0); // Clear pagination
        setDebouncedSearchTerm(""); // Prevent search with <2 chars
      }
    }, 500); // 500ms debounce delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm, isPriceDialogForcedOpen]);

  const [totalPages, setTotalPages] = useState(0);

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const companyIdForStats =
    session?.user?.planSet && session?.user?.companyId
      ? session.user.companyId
      : undefined;

  const { stats, isStatsLoading, isStatsError } = useCompanyActions(
    companyIdForStats as string
  );
  const SEARCH_PAGE_SIZE = 10; // Define page size for search results

  // Effect to perform search when debouncedSearchTerm changes and meets criteria
  const performSearch = useCallback(
    async (termToSearch: string, page: number) => {
      // Only perform search if authenticated and companyId is available
      if (!session?.user?.companyId) {
        setSearchError("Authentication or company information missing.");
        setIsLoadingSearch(false);
        return;
      }

      if (!termToSearch || termToSearch.length < 2) {
        setSearchResults([]);
        setSearchError(null);
        setTotalPages(0);
        // setCurrentPage(1); // Already handled by handleSearchChange or initial state
        setIsLoadingSearch(false);
        return;
      }

      setIsLoadingSearch(true);
      setSearchError(null);

      try {
        const response = await searchResume(
          termToSearch,
          page,
          SEARCH_PAGE_SIZE
        );
        if (response.error) {
          setSearchError(response.error);
          setSearchResults([]);
          setTotalPages(0);
        } else if (response.resumes) {
          setSearchResults(response.resumes);
          setTotalPages(response.totalPages || 0);
        }
      } catch (error) {
        setSearchError("An unexpected error occurred during search.");
        setSearchResults([]);
        setTotalPages(0);
      } finally {
        setIsLoadingSearch(false);
      }
    },
    [
      SEARCH_PAGE_SIZE,
      session?.user?.companyId,
      setIsLoadingSearch,
      setSearchError,
      setSearchResults,
      setTotalPages,
    ]
  ); // Add dependencies

  // Effect to trigger search when debounced term or page changes
  useEffect(() => {
    // Only trigger search if authenticated and companyId is available
    if (session?.user?.companyId) {
      if (debouncedSearchTerm.length >= 2) {
        performSearch(debouncedSearchTerm, currentPage);
      } else {
        // Clear results if search term is less than 2 chars
        setSearchResults([]);
        setTotalPages(0);
        setSearchError(null);
        // currentPage is not reset here, allowing it to persist if user types again
      }
    } else {
      // Clear results if not authenticated or no companyId
      setSearchResults([]);
      setTotalPages(0);
      setSearchError(null);
    }
  }, [
    debouncedSearchTerm,
    currentPage,
    performSearch,
    session?.user?.companyId,
    setSearchResults,
    setTotalPages,
    setSearchError,
  ]); // Add dependencies

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  async function onPlanSelect(
    planId: string,
    price: number,
    isAnnual: boolean,
    currencyData: CurrencyData
  ) {
    console.log({ selectedPlan: planId, isAnnual, currencyData });
    setIsPriceDialogForcedOpen(false);

    const userId = session?.user?.id as string;
    const companyId = session?.user?.companyId as string;

    if (!companyId) {
      setIsPlanUpdating(false);
      // This error message assumes plans are tied to companies.
      setError(
        "Company association is required to set a plan. Please ensure your user profile is linked to a company."
      );
      toast.error("Company association is required to set a plan.");
      return;
    }

    setIsPlanUpdating(true);

    try {
      const userPlan: UserPlan = {
        id: "",
        userId: userId,
        companyId: companyId,
        planId: planId,
        amount: price,
        paymentFrequency: isAnnual ? "Annual" : "Monthly",
        exchangeRate: currencyData.exchangeRate,
        exchangeCurrencyCode: currencyData.currencyCode,
        exchangeCurrencySymbol: currencyData.currencySymbol,
        userLocale: currencyData.locale,
        baseCurrencyCode: "USD",
        baseCurrencySymbol: "$",
      };

      const response = await setUserPlan(userPlan);
      if (response?.success) {
        setSuccess(response.success);
        toast.success(response.success);
        if (session?.user) {
          await update({
            ...session,
            user: {
              ...session.user,
              planSet: true,
            },
          });
        }
      } else if (response?.error) {
        setError(response.error);
        toast.error(response.error);
      } else {
        setError("An unexpected response was received from the server.");
        toast.error("An unexpected response was received from the server.");
      }
    } catch (error) {
      console.error("Error in onPlanSelect:", error);
      setError("An error occurred while updating the plan. Please try again.");
      toast.error(
        "An error occurred while updating the plan. Please try again."
      );
    } finally {
      setIsPlanUpdating(false);
    }
  }

  if (isPriceDialogForcedOpen) {
    return (
      <Dialog
        open={isPriceDialogForcedOpen}
        onOpenChange={(openStatusFromRadix) => {
          // If Radix suggests closing, but we are in a forced open state, ignore the suggestion.
          if (isPriceDialogForcedOpen && !openStatusFromRadix) {
            return;
          }
          setIsPriceDialogForcedOpen(openStatusFromRadix);
        }}
      >
        <DialogTitle>Select a Plan</DialogTitle>
        <DialogPortal>
          <DialogOverlay className="z-[9999] bg-black/80" />{" "}
          {/* Explicit high z-index and background */}
          {/* This div is the flex container that centers the content */}
          <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4 overflow-auto">
            {" "}
            {/* Higher z-index and overflow for the wrapper */}
            <DialogPrimitive.Content
              onEscapeKeyDown={(e) => {
                if (isPriceDialogForcedOpen) e.preventDefault(); // Prevent closing with Escape key
              }}
              onInteractOutside={(e) => {
                // Radix primitive uses onInteractOutside (mapped from onPointerDownOutside)
                if (isPriceDialogForcedOpen) e.preventDefault(); // Prevent closing on outside click
              }}
              // Style this like a modal box, but position it as a relative block for flex centering
              className="relative bg-background border shadow-lg rounded-lg w-full max-w-5xl max-h-[calc(100vh-2rem)] overflow-y-auto p-0" // max-h accounts for p-4 on wrapper
            >
              <PriceSelectTable onPlanSelect={onPlanSelect} />
            </DialogPrimitive.Content>
          </div>
        </DialogPortal>
      </Dialog>
    );
  }

  const companyDisplayName =
    stats?.companyName?.name || session?.user?.name || "";

  // Adjust the `link` paths based on your actual routing structure.
  const dashboardSections: CompanyDashboardCardProps[] = [
    {
      title: "Post a New Job",
      description: "Create and publish new job listings to attract top talent.",
      link: "/home/<USER>/job/post",
      linkText: "Create Job Posting",
      icon: <PenBoxIcon className="h-6 w-6" />,
    },
    {
      title: "Manage Job Listings",
      description:
        "View, edit, or unpublish your active and past job postings.",
      link: "/home/<USER>/job/list",
      linkText: "View My Jobs",
      icon: <ListCheck className="h-6 w-6" />,
    },
    {
      title: "View Applicants",
      description:
        "Review and manage applications received for your job postings.",
      link: "/home/<USER>/applications",
      linkText: "See All Applicants",
      icon: <FileUser className="h-6 w-6" />,
    },
    {
      title: "Company Account",
      description: "Update your company's information, logo, and branding.",
      link: "/home/<USER>/account",
      linkText: "Edit Account",
      icon: <Building2 className="h-6 w-6" />,
    },
  ];

  async function handleVerifyEmail() {
    try {
      setIsVerifyingEmail(true);
      const response = await verifyEmail(session?.user?.id as string);
      if (response?.success) {
        setIsEmailSent(true);
        toast.success(response.success);
      } else if (response?.error) {
        setIsEmailSent(false);
        toast.error(response.error);
      }
    } catch (error) {
      toast.error(
        `An error occurred while verifying your email. Please try again.`
      );
    } finally {
      setIsVerifyingEmail(false);
    }
  }

  return (
    <div className="container mx-auto p-4 md:p-4 bg-gray-50 min-h-screen">
      {!session?.user?.verified &&
        (!isEmailSent ? (
          <div className="flex w-full items-center justify-left bg-rose-50 border rounded-lg p-2 mb-4">
            Your email remains unverified. Please verify now! &nbsp;&nbsp;
            <Button
              onClick={handleVerifyEmail}
              variant={"outline"}
              className="cursor-pointer h-[25px]"
            >
              {isVerifyingEmail ? "Sending Email..." : "Verify Email"}
            </Button>
          </div>
        ) : (
          <div className="flex w-full items-center justify-left bg-green-50 border rounded-lg p-2 mb-4">
            Email sent! Please check your email and verify your registration.
          </div>
        ))}

      <header className="mb-8 md:mb-12">
        <h1 className="text-3xl md:text-3xl font-bold text-gray-800">
          Welcome, {companyDisplayName}!
        </h1>
        <p className="text-md text-gray-600 mt-2">
          Manage your recruitment efforts and find the best candidates for your
          team.
        </p>
        {isPlanUpdating && (
          <LoadingStateHorizontal text="Updating your plan, please wait..." />
        )}
        <ErrorMessage message={error} />
        <SuccessMessage message={success} />
      </header>

      {/* Future Enhancements: Statistics, Graphs, Notifications, Search */}
      <section className="mt-10 md:mt-16">
        <div className="flex w-full items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-700 mb-6">
            Hiring Insights & Activity
            </h2>
                <Link 
                className={buttonVariants({ size: "lg" })} 
                href="/home/<USER>/upload"
              >
                Upload Jobs and Resumes
              </Link>
        </div>

        {/* Placeholder for Applicant Search/Filter */}
        <div className="p-6 mb-10">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">
            Find & Manage Candidates
          </h3>
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2">
            {/* Search Input */}
            <input
              type="text"
              placeholder="Search applicants by name, skill, or job title..."
              className="flex-grow w-full sm:w-auto p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={handleSearchChange}
              aria-label="Search resumes"
            />
          </div>

          {searchTerm.trim().length > 0 && searchTerm.trim().length < 2 && (
            <p className="mt-2 text-sm text-gray-500">
              Please type at least 2 characters to search.
            </p>
          )}

          {/* Display Search Results */}
          {isLoadingSearch && debouncedSearchTerm.length >= 2 && (
            <p className="mt-4 text-gray-600">Searching for resumes...</p>
          )}
          {searchError && <p className="mt-4 text-red-500">{searchError}</p>}
          {!isLoadingSearch && searchResults.length > 0 && (
            <div className="mt-6">
              <h4 className="text-md font-semibold text-gray-700 mb-3">
                Search Results:
              </h4>
              <ul className="space-y-2">
                {searchResults.map((result) => (
                  <li
                    key={result.id}
                    className="p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <Link
                      href={`/home/<USER>/resumedoc/${result.id}`}
                      target="_blank"
                      className="grid grid-cols-[40px_1fr] gap-x-3 items-start"
                    >
                      <div className="w-10 h-10">
                        <Image
                          src={result.picture || "/icons/profile.png"}
                          alt={result.name || "Resume"}
                          width={40}
                          height={40}
                          className="rounded-md object-cover"
                        />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium text-blue-700 hover:underline block">
                          {normalizeHeaders(result.name) || "Unnamed"}
                        </span>
                        <span className="text-xs text-gray-500 block mt-1">
                          Created:{" "}
                          {new Date(result.createdAt).toLocaleDateString()}
                        </span>
                        {result.resume.standardFields?.summary?.content && (
                          <p
                            className="text-xs text-gray-500 block mt-1"
                            title={
                              result.resume?.standardFields?.summary?.content
                            }
                          >
                            <b>Summary:</b>{" "}
                            {result.resume?.standardFields?.summary?.content}
                          </p>
                        )}
                        {result.resume?.standardFields?.skills &&
                          Array.isArray(
                            result.resume?.standardFields?.skills
                          ) &&
                          result.resume?.standardFields.skills.length > 0 && (
                            <p
                              className="text-xs text-gray-500 block mt-1"
                              title={result.resume?.standardFields?.skills.join(
                                ", "
                              )}
                            >
                              <b>Skills:</b>{" "}
                              {result.resume?.standardFields?.skills.join(", ")}{" "}
                            </p>
                          )}
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {!isLoadingSearch &&
            !searchError &&
            searchResults.length === 0 &&
            debouncedSearchTerm.length >= 2 && (
              <p className="mt-4 text-gray-600">
                No resumes found matching &quot;{debouncedSearchTerm}&quot;.
              </p>
            )}
        </div>

        <div className="mb-8 p-6 bg-white shadow-md rounded-lg">
            <h3 className="text-lg font-semibold text-gray-700">
                Key Metrics
            </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            <div className="p-4 bg-pink-50 rounded-lg">
              <p className="text-3xl font-bold text-pink-600">
                {stats?.totalJobs || "-"}
              </p>
              <p className="text-sm text-gray-600">Total Job Posts</p>
            </div>
            <div className="p-4 bg-indigo-50 rounded-lg">
              <p className="text-3xl font-bold text-indigo-600">
                {stats?.totalActiveJobs || "-"}
              </p>
              <p className="text-sm text-gray-600">Active Job Posts</p>
            </div>
            <div className="p-4 bg-teal-50 rounded-lg">
              <p className="text-3xl font-bold text-teal-600">
                {stats?.totalApplicants || "-"}
              </p>
              <p className="text-sm text-gray-600">Total Applicants</p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <p className="text-3xl font-bold text-yellow-600">
                {stats?.newApplicants || "-"}
              </p>
              <p className="text-sm text-gray-600">New Applicants (24h)</p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center mt-4">
            <div className="p-4 bg-slate-50 rounded-lg">
              <p className="text-3xl font-bold text-slate-600">
                {stats?.totalResumes || "-"}
              </p>
              <p className="text-sm text-gray-600">Total Resumes</p>
            </div>
            <div className="p-4 bg-sky-50 rounded-lg">
              <p className="text-3xl font-bold text-sky-600">
                {stats?.totalMatches || "-"}
              </p>
              <p className="text-sm text-gray-600">Total Job Matches</p>
            </div>
            <div className="p-4 bg-lime-50 rounded-lg">
              <p className="text-3xl font-bold text-lime-600">
                {stats?.totalFiles || "-"}
              </p>
              <p className="text-sm text-gray-600">Total Files</p>
            </div>
            <div className="p-4 bg-violet-50 rounded-lg">
              <p className="text-3xl font-bold text-violet-600">
                {stats?.newShortlist || "-"}
              </p>
              <p className="text-sm text-gray-600">Shorlists (24h)</p>
            </div>
          </div>
        </div>

        {/* Placeholder for Recent Applicants / Notifications */}
        {/* <div className="mb-8 p-6 bg-white shadow-md rounded-lg">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Recent Activity</h3>
          <ul className="space-y-3">
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">New applicant for "Software Engineer" - John Doe</li>
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">"Marketing Specialist" job posting expiring in 3 days.</li>
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">Message received from candidate Jane Smith regarding "Product Manager" role.</li>
          </ul>
          <p className="text-xs text-gray-500 mt-4 text-center italic">(Dynamic list of recent applicants, job status updates, or messages.)</p>
        </div> */}
      </section>

      {/* Pagination for Search Results */}
      {!isLoadingSearch && searchResults.length > 0 && totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <MainPagination
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 mb-10 mt-10 md:mb-16">
        {dashboardSections.map((section) => (
          <CompanyDashboardCard
            key={section.title}
            title={section.title}
            description={section.description}
            link={section.link}
            linkText={section.linkText}
            icon={section.icon}
          />
        ))}
      </div>
    </div>
  );
}
