import { Inngest } from "inngest";

/**
 * A custom fetch wrapper to debug non-JSON responses from Inngest during registration.
 * This is useful in production where we can't easily modify node_modules.
 */
const customFetch: typeof fetch = async (url, options) => {
  const response = await fetch(url, options);

  // The registration URL is the one that's failing.
  // We check the response here to see what's actually being returned.
  if (String(url).includes("/fn/register")) {
    // We must read the body to inspect it, but it can only be read once.
    // We read it as text, then return a new Response with that text so the SDK can also read it.
    const rawText = await response.text();

    try 
        {console.log(
          `[Inngest Debug] Parsed registration response as JSON. Raw HTML response (truncated):\n---\n${rawText.substring(
            0,
            1000
          )}\n---`
        );
      // If this doesn't throw, the response was valid JSON.
      JSON.parse(rawText);
    } catch (error) {
      // If JSON parsing fails, it's likely an HTML error page.
      if (error instanceof SyntaxError) {
        console.error(
          `[Inngest Debug] Failed to parse registration response as JSON. Raw HTML response (truncated):\n---\n${rawText.substring(
            0,
            1000
          )}\n---`
        );
      }
    }

    // IMPORTANT: Because we consumed the original response body with `response.text()`,
    // we MUST return a new Response object that the Inngest SDK can then consume.
    return new Response(rawText, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
  }

  return response;
};

export const inngest = new Inngest({
  id: "athena",
  signingKey: process.env.INNGEST_SIGNING_KEY,
//   baseUrl: process.env.INNGEST_BASE_URL,
  fetch: customFetch,
});
