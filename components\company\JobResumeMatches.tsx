import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { EyeIcon } from "lucide-react";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import { getJobResumeMatches } from "@/actions/job/getJob";
import Image from "next/image";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { normalizeHeaders } from "@/utils/stringHelpters";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export async function JobResumeMatches({ jobId, userId }: { jobId: string, userId?: string }) {
  const data = await getJobResumeMatches(jobId);
  let userUrl = "";

  if (userId && userId.length > 0) {
    userUrl = "/home/<USER>/resume/";
  } else {
    userUrl = "/home/<USER>/resume/";
  }
  
  return (
    <>
      <div className="w-full">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead style={{ width: "45%" }}>Name</TableHead>
              <TableHead style={{ width: "15%" }}>Match</TableHead>
              <TableHead style={{ width: "15%" }}>Evaluation</TableHead>
              <TableHead style={{ width: "15%" }}>Resume</TableHead>
              <TableHead style={{ width: "10%" }}>Shortlist</TableHead>
            </TableRow>
          </TableHeader>
        </Table>

        <ScrollArea className={`w-full ${data.length > 5 ? "h-[200px]" : ""}`}>
          <Table>
            <TableBody>
              {data.map((match) => (
                <TableRow key={match.id}>
                  <TableCell
                    style={{ width: "45%" }}
                    className="flex items-center gap-2"
                  >
                    <Image
                      src={match.resume?.picture || "/icons/profile.png"}
                      alt={`${match.resume?.name || ""}`}
                      width={28}
                      height={28}
                      className="rounded-full"
                    />
                    {normalizeHeaders(match?.resume?.name as string) || ""}
                  </TableCell>
                  <TableCell style={{ width: "15%" }}>
                    <MatchScoreCircle score={match.overall_score} />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-4">
                    <ResumeMatchDialog
                      resumeMatch={match}
                      applicantName={match.resume?.name ? match.resume?.name : `${match.resume?.user?.firstName} ${match.resume?.user?.lastName}` || ""}
                    />
                  </TableCell>
                  <TableCell style={{ width: "15%" }} className="pl-4">
                    <a
                      href={`${APP_URL}${userUrl}${match.resume?.id}`}
                      target="_blank"
                      className="text-primary hover:underline cursor-pointer"
                      title="View Resume"
                    >
                      <Button
                        variant="ghost"
                        className="text-primary hover:underline cursor-pointer"
                        title="View Resume"
                      >
                        <EyeIcon className="w-6 h-6" />
                      </Button>
                    </a>
                  </TableCell>
                  <TableCell
                    style={{ width: "10%" }}
                    className="pl-4 cursor-pointer"
                  >
                    {userId && userId.length > 0 ? (
                        match.resume?.isShortlisted ? "Yes" : "No"
                    ) : (
                        <ShortlistCheckbox
                        jobId={jobId}
                        resumeId={match.resumeId}
                        initialShortlisted={match.resume?.isShortlisted || false}
                        />
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    </>
  );
}
