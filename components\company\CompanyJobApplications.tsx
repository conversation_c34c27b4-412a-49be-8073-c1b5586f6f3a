import {
  <PERSON>,
  CardTitle,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MainPagination } from "@/components/general/MainPagination";
import { Suspense } from "react";
import { getJobApplicants } from "@/actions/job/getJob";
import { JobApplicantsInline } from "./JobApplicantsInline";

interface CompanyJobApplicationsProps {
  paginatedJobs: any[];
  currentPage: number;
  totalPages: number;
}

export function CompanyJobApplications({ paginatedJobs, currentPage, totalPages }: CompanyJobApplicationsProps) {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Job Applications</CardTitle>
          <CardDescription>
            Manage your job applicants here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader className="font-bold">
              <TableRow>
                <TableHead style={{ width: '10%' }}>Posted</TableHead>
                <TableHead style={{ width: '32.5%' }}>Job</TableHead>
                <TableHead style={{ width: '20%' }}>Name</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Match</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Evaluation</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Resume</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Shortlist</TableHead>
                <TableHead className="text-center" style={{ width: '7.5%' }}>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedJobs.map((job, index) => (
                <TableRow 
                  key={job.id} 
                  className={index % 2 === 1 ? "bg-muted/30" : ""}
                >
                  <TableCell style={{ width: '10%', verticalAlign: 'top' }}>{job.createdAt.toLocaleDateString()}</TableCell>
                  <TableCell className="font-medium overflow-hidden text-ellipsis whitespace-nowrap min-w-0" style={{ width: '32.5%', verticalAlign: 'top' }}>{job.jobTitle}</TableCell>
                  <TableCell colSpan={6} style={{ padding: '0' }}>
                    <Suspense fallback={<div className="p-2">Loading job applicants...</div>}>
                      <JobApplicantsWrapper jobId={job.id} rowIndex={index} />
                    </Suspense>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <MainPagination totalPages={totalPages} currentPage={currentPage} />
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}

// This wrapper component fetches the shortlisted applicants using the server action
async function JobApplicantsWrapper({ jobId, rowIndex }: { jobId: string, rowIndex: number }) {
  const jobdApplicants = await getJobApplicants(jobId);
  
  return (
    <JobApplicantsInline 
      jobId={jobId} 
      jobTitle={jobdApplicants[0]?.jobPost?.jobTitle || ""}
      jobApplicants={jobdApplicants}
      isOddRow={rowIndex % 2 === 1}
    />
  );
}
