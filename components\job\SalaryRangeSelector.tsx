import { Control, useController } from "react-hook-form";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { useState, useEffect } from "react";
import { formatCurrency } from "../../utils/formatCurrency";
import { Input } from "../ui/input";

interface SalaryRangeSelectorProps {
  control: Control;
  minSalary: number;
  maxSalary: number;
  step: number;
  currency: string;
}

export function SalaryRangeSelector({ control, minSalary, maxSalary, step, currency }: SalaryRangeSelectorProps) {
  const { field: fromField } = useController({
    name: "salaryFrom",
    control,
    defaultValue: minSalary,
  });

  const { field: toField } = useController({
    name: "salaryTo",
    control,
    defaultValue: Math.min(maxSalary / 2, minSalary + step * 20),
  });

  const defaultTo = Math.min(maxSalary / 2, minSalary + step * 20);

  const [range, setRange] = useState<[number, number]>([
    Math.max(fromField.value ?? minSalary, minSalary),
    Math.max(toField.value ?? defaultTo, (fromField.value ?? minSalary) + step), // Ensure 'to' is at least 'from' + step
  ]);

  const [minInputFocused, setMinInputFocused] = useState(false);
  const [maxInputFocused, setMaxInputFocused] = useState(false);

  // Add state for input values
  const [inputValues, setInputValues] = useState<[string, string]>([
    formatCurrency(range[0], currency),
    formatCurrency(range[1], currency)
  ]);

  useEffect(() => {
    const formValFrom = fromField.value ?? minSalary;
    const formValTo = toField.value ?? defaultTo;

    // Calculate initial candidates for new range values, respecting overall bounds and step for 'from'.
    let candidateFrom = Math.max(minSalary, Math.min(formValFrom, maxSalary - step));
    // Calculate 'to', respecting overall bounds and ensuring it's at least 'from' + step.
    let candidateTo = Math.min(maxSalary, Math.max(formValTo, candidateFrom + step));

    // If candidateTo was adjusted upwards significantly, candidateFrom might now be invalid relative to it.
    // Re-clamp candidateFrom to ensure it's at most candidateTo - step.
    candidateFrom = Math.max(minSalary, Math.min(candidateFrom, candidateTo - step));

    // Final pass to ensure candidateTo is still valid if candidateFrom was adjusted down.
    candidateTo = Math.min(maxSalary, Math.max(candidateTo, candidateFrom + step));

    // Ensure final values are strictly within overall min/max and consistent with each other.
    const finalFrom = Math.max(minSalary, Math.min(candidateFrom, maxSalary - step));
    const finalTo = Math.min(maxSalary, Math.max(candidateTo, finalFrom + step));

    if (finalFrom !== range[0] || finalTo !== range[1]) {
      setRange([finalFrom, finalTo]);
    }
    // This effect derives `range` from form field values and props.
    // It should not depend on `range` itself to prevent update loops.
  }, [fromField.value, toField.value, minSalary, maxSalary, step, defaultTo]);

  useEffect(() => {
    // Update inputValues when range or currency changes, respecting focus
    setInputValues(current => [minInputFocused ? current[0] : formatCurrency(range[0], currency), maxInputFocused ? current[1] : formatCurrency(range[1], currency)]);
  }, [range, currency, minInputFocused, maxInputFocused]);

  function handleSliderChange(newSliderValues: number[]) {
    if (newSliderValues.length === 2) {
      const newFrom = newSliderValues[0];
      const newTo = newSliderValues[1];

      setRange([newFrom, newTo]);
      // setInputValues will be handled by the useEffect listening to `range`
      // but to ensure immediate reflection if not focused:
      if (!minInputFocused) setInputValues(prev => [formatCurrency(newFrom, currency), prev[1]]);
      if (!maxInputFocused) setInputValues(prev => [prev[0], formatCurrency(newTo, currency)]);
      fromField.onChange(newFrom);
      toField.onChange(newTo);
      fromField.onBlur();
      toField.onBlur();
    }
  }

  function handleInputChange(inputValue: string, fieldIndex: 0 | 1) {
    const newRawInputValues = [...inputValues] as [string, string];
    newRawInputValues[fieldIndex] = inputValue;
    // For the other input, keep it formatted based on the current range
    if (fieldIndex === 0) newRawInputValues[1] = formatCurrency(range[1], currency);
    else newRawInputValues[0] = formatCurrency(range[0], currency);
    setInputValues(newRawInputValues);

    const numericValue = parseInt(inputValue.replace(/[^0-9]/g, ''), 10);

    if (!isNaN(numericValue)) {
      // Clamp the numeric value within the overall min/max bounds
      const clampedValue = Math.max(minSalary, Math.min(numericValue, maxSalary));

      if (fieldIndex === 0) { // Editing Min input
        // Update only the 'from' field in react-hook-form
        fromField.onChange(clampedValue);
      }  else { // Editing Max input
        // Update only the 'to' field in react-hook-form
        toField.onChange(clampedValue);
      }
      // The useEffect listening to fromField.value/toField.value will update the `range` state,
      // which in turn moves the slider thumbs.
    }
  }

  function handleInputBlur(index: number) {
    if (index === 0) setMinInputFocused(false);
    else setMaxInputFocused(false);

    // Reformat the specific blurred input based on the current range
    const newFormattedValue = formatCurrency(range[index], currency);
    setInputValues(current => index === 0 ? [newFormattedValue, current[1]] : [current[0], newFormattedValue]);

    // Ensure RHF also gets the onBlur
    if (index === 0) fromField.onBlur(); else toField.onBlur();
  }

  return (
    <div className="w-full max-w-md space-y-0">
      <SliderPrimitive.Root
        className="relative flex items-center select-none touch-none w-full h-5"
        value={range}
        onValueChange={handleSliderChange}
        min={minSalary}
        max={maxSalary}
        step={step}
        minStepsBetweenThumbs={1} // Ensure thumbs are at least 1 step apart
      >
        <SliderPrimitive.Track className="relative bg-gray-300 grow h-1 rounded">
          <SliderPrimitive.Range className="absolute bg-blue-500 h-full rounded" />
        </SliderPrimitive.Track>
        {range.map((_, i) => (
          <SliderPrimitive.Thumb
            key={i}
            className="block w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        ))}
      </SliderPrimitive.Root>
      
      <div className="flex justify-between gap-x-1">
        <div className="flex-1">
          <label className="text-xs text-gray-500">Min</label>
          <Input
            type="text"
            value={inputValues[0]}
            onFocus={() => setMinInputFocused(true)}
            onChange={(e) => handleInputChange(e.target.value, 0)} // Pass 0 for min input
            onBlur={() => handleInputBlur(0)}
            className="mt-1"
          />
          <label className="text-xs text-gray-500"><em>Equal or lower than max</em></label>
        </div>
        <div className="flex-1">
          <label className="text-xs text-gray-500">Max</label>
          <Input
            type="text"
            value={inputValues[1]}
            onFocus={() => setMaxInputFocused(true)}
            onChange={(e) => handleInputChange(e.target.value, 1)} // Pass 1 for max input
            onBlur={() => handleInputBlur(1)}
            className="mt-1"
          />
          <label className="text-xs text-gray-500"><em>Equal or higher than min</em></label>
        </div>
      </div>
    </div>
  );
}
