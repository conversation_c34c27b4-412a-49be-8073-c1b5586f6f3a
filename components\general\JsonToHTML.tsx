import React from "react";

interface JsonToHTMLProps {
  json: any;
  hideSkills?: boolean;
}

export function JsonToHTML({ json, hideSkills = false }: JsonToHTMLProps) {
  // Check if this is a Tiptap/ProseMirror format (manually added)
  if (json.type === "doc" && Array.isArray(json.content)) {
    return renderTiptapContent(json);
  }
  
  // Otherwise, assume it's AI-processed format
  return renderAIProcessedContent(json, hideSkills);
}

function renderTiptapContent(json: any) {
  return ( // Removed "prose max-w-none" as it's applied by the parent
    <div className="space-y-4">
      {json.content.map((node: any, index: number) => {
        if (node.type === "paragraph") {
          return (
            <p key={index} className={`mb-4 ${node.attrs?.textAlign ? `text-${node.attrs.textAlign}` : ""}`}>
              {node.content?.map((inline: any, i: number) => {
                if (inline.type === "text") {
                  let content = inline.text;
                  if (inline.marks) {
                    for (const mark of inline.marks) {
                      if (mark.type === "bold") {
                        content = <strong key={i}>{content}</strong>;
                      } else if (mark.type === "italic") {
                        content = <em key={i}>{content}</em>;
                      } else if (mark.type === "underline") {
                        content = <u key={i}>{content}</u>;
                      }
                    }
                  }
                  return <React.Fragment key={i}>{content}</React.Fragment>;
                }
                return null;
              })}
            </p>
          );
        } else if (node.type === "heading") {
          const level = node.attrs.level || 1;
          const headingContent = node.content?.map((inline: any, i: number) => {
            if (inline.type === "text") {
              return <React.Fragment key={i}>{inline.text}</React.Fragment>;
            }
            return null;
          });
          
          const className = `mb-4 ${node.attrs?.textAlign ? `text-${node.attrs.textAlign}` : ""}`;
          
          switch (level) {
            case 1: return <h1 key={index} className={className}>{headingContent}</h1>;
            case 2: return <h2 key={index} className={className}>{headingContent}</h2>;
            case 3: return <h3 key={index} className={className}>{headingContent}</h3>;
            case 4: return <h4 key={index} className={className}>{headingContent}</h4>;
            case 5: return <h5 key={index} className={className}>{headingContent}</h5>;
            case 6: return <h6 key={index} className={className}>{headingContent}</h6>;
            default: return <h3 key={index} className={className}>{headingContent}</h3>;
          }
        } else if (node.type === "bulletList") {
          return (
            <ul key={index} className="mb-4">
              {node.content?.map((listItem: any, i: number) => {
                return (
                  <li key={i}>
                    {listItem.content?.map((paragraph: any, j: number) => {
                      if (paragraph.type === "paragraph") {
                        return (
                          <span key={j}>
                            {paragraph.content?.map((inline: any, k: number) => {
                              if (inline.type === "text") {
                                return <React.Fragment key={k}>{inline.text}</React.Fragment>;
                              }
                              return null;
                            })}
                          </span>
                        );
                      }
                      return null;
                    })}
                  </li>
                );
              })}
            </ul>
          );
        } else if (node.type === "hardBreak") {
          return <br key={index} />;
        }
        return null;
      })}
    </div>
  );
}

function renderAIProcessedContent(json: any, hideSkills: boolean = false) {
  // --- 1. Normalize keys ---
  const description = json.Description || json.description;
  const requirements = json.requirements || json["Requirements:"] || json["Requirements"];
  const responsibilities = json.responsibilities || json["Job Responsibilities:"] || json["Job Responsibilities"];
  const skills = json.skills || json.Skills;
  const additionalSectionsData = json.additionalSections;

  const sections: { title: string; content: string | string[] }[] = [];

  // --- 2. Build Sections Array from different data structures ---

  const hasStructuredData = 
    (requirements && Array.isArray(requirements)) ||
    (responsibilities && Array.isArray(responsibilities)) ||
    (skills && Array.isArray(skills)) ||
    (additionalSectionsData && Array.isArray(additionalSectionsData));

  if (hasStructuredData) {
    // Case 1: Fully structured data
    if (description) {
      const content = Array.isArray(description) ? description.join('\n') : String(description);
      if (content.trim()) sections.push({ title: "Description", content });
    }
    if (requirements && Array.isArray(requirements) && requirements.length > 0) {
      sections.push({ title: "Requirements", content: requirements });
    }
    if (responsibilities && Array.isArray(responsibilities) && responsibilities.length > 0) {
      sections.push({ title: "Responsibilities", content: responsibilities });
    }
    if (!hideSkills && skills && Array.isArray(skills) && skills.length > 0) {
      sections.push({ title: "Skills", content: skills });
    }
    if (additionalSectionsData && Array.isArray(additionalSectionsData)) {
        additionalSectionsData.forEach((section: any) => {
            if (section.section_name && section.details) {
              const items = section.details.map((detail: any) => 
                typeof detail === 'object' && detail !== null
                  ? Object.entries(detail).map(([key, value]) => `${key}: ${value}`).join('; ')
                  : String(detail)
              );
              if (items.length > 0) {
                sections.push({ title: section.section_name, content: items });
              }
            }
        });
    }
  } else if (description) {
    // Case 2 & 3: Description is a single string or an array of strings
    const lines = Array.isArray(description) ? description : description.split('\n');
    
    let currentTitle: string | null = "Description"; // Default section for content before the first header
    let currentItems: string[] = [];

    const commitSection = () => {
      if (currentTitle && currentItems.length > 0) {
        // If the title is "Description", join items as a paragraph. Otherwise, it's a list.
        const isList = currentTitle.toLowerCase() !== 'description';
        const content = isList ? currentItems : currentItems.join('\n');
        sections.push({ title: currentTitle, content });
      }
      currentItems = [];
    };

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // A header is a short line ending with a colon.
      const match = trimmedLine.match(/^([^:]{1,50}):$/);
      if (match) {
        commitSection(); // Save the previous section
        currentTitle = match[1].trim(); // Set the new section's title
      } else {
        currentItems.push(trimmedLine);
      }
    }
    commitSection(); // Commit the very last section
  }

  // --- 3. Render the standardized sections array ---
  if (sections.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      {sections.map((section, index) => (
        <div key={`${section.title}-${index}`}>
          <h3 className="font-bold mb-2 capitalize">{section.title}</h3>
          {Array.isArray(section.content) ? (
            <ul className="list-disc pl-5 space-y-1">
              {section.content.map((item, i) => (
                <li key={`item-${index}-${i}`}>{item}</li>
              ))}
            </ul>
          ) : (
            section.content.split('\n').map((paragraph, i) => (
              <p key={`p-${index}-${i}`}>{paragraph}</p>
            ))
          )}
        </div>
      ))}
    </div>
  );
}
