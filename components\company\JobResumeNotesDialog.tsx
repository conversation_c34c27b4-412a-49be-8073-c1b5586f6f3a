"use client";

import { Dialog, DialogTitle, DialogTrigger, DialogPortal, DialogOverlay, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { PenBoxIcon, X } from "lucide-react";
import { DialogDescription } from "@radix-ui/react-dialog";
import { JobResumeNotes } from "./JobResumeNotes";
import { normalizeHeaders } from "@/utils/stringHelpters";

export function JobResumeNotesDialog({ 
  jobId,
  resumeId, 
  jobTitle,
  resumeName 
}: { 
  resumeName: string;
  jobId: string;
  resumeId: string;
  jobTitle: string;
}) {

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="text-primary hover:underline cursor-pointer" title="View Notes">
            <PenBoxIcon className="w-5 h-5" />
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-[1200px] max-w-[1400px] max-h-[90vh]">
            <div className="flex items-center justify-between pl-6 pr-6 pt-6">
              <DialogTitle className="text-lg font-semibold">Talent Notes</DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 cursor-pointer">
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
              <DialogDescription asChild>
                <div className="flex flex-col w-full pl-6 pr-6">
                    <div className="flex items-center justify-between">
                        <span>{normalizeHeaders(resumeName)}</span>
                        <span>{normalizeHeaders(jobTitle)}</span>
                    </div>
                    <div>
                    <em className="text-sm text-gray-500">Notes or remarks by you and your team for this talent and specific job position.</em>
                    </div>
                </div>
              </DialogDescription>
                <Separator />
            <div className="overflow-y-auto pl-6 pr-6">
              <JobResumeNotes 
                jobId={jobId}
                resumeId={resumeId}
              />
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
