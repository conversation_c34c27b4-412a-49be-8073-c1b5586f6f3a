"use server";

import { searchJobData } from "@/data/job/job";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
// Assuming JobSearchResult is defined somewhere accessible, e.g., a shared types file
// or you can redefine it here if it's specific to this action's return.
interface JobSearchResult {
  id: string;
  jobTitle: string;
  company: {
    name: string;
    logo?: string | null; // Added logo
  } | null;
  createdAt: string; // Added createdAt
}

export async function searchJob(
  searchTerm: string,
  page: number = 1,
  pageSize: number = 10
): Promise<{ jobs?: JobSearchResult[], totalPages?: number, error?: string }> {
  const session = await auth();

   if (!session?.user?.id) {
    // Returning an error object is often preferred for client-side handling
    // over throwing an error that might not be caught gracefully by the client.
    return { error: "User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const searchData = await searchJobData(searchTerm, page, pageSize);

    if (searchData.error) {
      return { error: searchData.error };
    }

    // Convert createdAt to string to match JobSearchResult interface
    const jobs: JobSearchResult[] = searchData.jobs.map(job => ({
      ...job,
      createdAt: job.createdAt.toISOString(), // Convert Date to ISO string
      // Ensure company structure matches, especially if logo can be undefined vs null
      company: job.company ? { name: job.company.name, logo: job.company.logo || null } : null,
    }));
    return { jobs: jobs, totalPages: searchData.totalPages };
  } catch (error) {
    console.error("Error fetching job:", error);
    return { error: "Failed to search for jobs. Please try again." };
  }
}