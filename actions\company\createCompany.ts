"use server";

import { z } from "zod";
import { CompanySchema } from "@/data/zod/zodSchema";
import { prisma } from "@/lib/prisma/prismaClient";
import { v4 as uuidv4 } from "uuid";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function CreateCompany(data: z.infer<typeof CompanySchema>) {
  const session = await auth();
  
  if (!session?.user?.id) {
    // Or handle as per your middleware strategy, e.g., throw new Error("Unauthorized");
    // This assumes middleware might not cover this specific action directly,
    // or as a defense-in-depth. If covered by middleware, this error indicates an issue.
    return { error: "Error! User not authenticated." };
  }
  
  await ensureServerActionProtection();

  try {
    const validateData = CompanySchema.parse(data);

    if (validateData) {
      // Create a new company record
      const company = await prisma.company.create({
        data: {
          id: uuidv4(),
          name: validateData.name,
          location: validateData.location,
          about: validateData.about,
          description: validateData.description,
          logo: validateData.logo,
          website: validateData.website,
          xAccount: validateData.xAccount,
          linkedIn: validateData.linkedIn,
          tin: validateData.tin,
          foreignerRatio: validateData.foreignerRatio,
          englishUsageRatio: validateData.englishUsageRatio,
          benefits: validateData.benefits,
          userId: session?.user?.id as string,
        },
      });

      if (company) {
        // Then, update the user with the companyId
        await prisma.user.update({
          where: {
            id: session?.user?.id as string,
          },
          data: {
            onboarded: true,
            role: "ADMIN",
            userType: "COMPANY",
            companyId: company.id as string,
          },
        });        

        return {
            data: company,
            success: "Success! Company information was saved.",
        };
      } else {
        return {
            data: null,
          error: "Error! Failed to save company information.",
        };
      }
    }
  } catch (error) {
    return { data: null, error: `Error was encountered, please try again. ${error}` };
  }
}
