export type City = {
  name: string;
  country: string;
};

export const cityList: { [key: string]: City[] } = {
  "Argentina": [
    { name: "Nationwide", country: "Argentina" },
    { name: "Overseas", country: "Argentina" },
    { name: "Buenos Aires", country: "Argentina" },
    { name: "Cordoba", country: "Argentina" },
    { name: "Rosario", country: "Argentina" }
  ],
  "Australia": [
    { name: "Nationwide", country: "Australia" },
    { name: "Overseas", country: "Australia" },
    { name: "Adelaide", country: "Australia" },
    { name: "Brisbane", country: "Australia" },
    { name: "Canberra", country: "Australia" },
    { name: "Darwin", country: "Australia" },
    { name: "Gold Coast", country: "Australia" },
    { name: "Hobart", country: "Australia" },
    { name: "Melbourne", country: "Australia" },
    { name: "Perth", country: "Australia" },
    { name: "Sydney", country: "Australia" }
  ],
  "Austria": [
    { name: "Nationwide", country: "Austria" },
    { name: "Overseas", country: "Austria" },
    { name: "Vienna", country: "Austria" },
    { name: "Graz", country: "Austria" },
    { name: "Salzburg", country: "Austria" }
  ],
  "Bangladesh": [
    { name: "Nationwide", country: "Bangladesh" },
    { name: "Overseas", country: "Bangladesh" },
    { name: "Dhaka", country: "Bangladesh" },
    { name: "Chittagong", country: "Bangladesh" }
  ],
  "Belgium": [
    { name: "Nationwide", country: "Belgium" },
    { name: "Overseas", country: "Belgium" },
    { name: "Brussels", country: "Belgium" },
    { name: "Antwerp", country: "Belgium" },
    { name: "Ghent", country: "Belgium" }
  ],
  "Brazil": [
    { name: "Nationwide", country: "Brazil" },
    { name: "Overseas", country: "Brazil" },
    { name: "Belo Horizonte", country: "Brazil" },
    { name: "Brasília", country: "Brazil" },
    { name: "Curitiba", country: "Brazil" },
    { name: "Fortaleza", country: "Brazil" },
    { name: "Manaus", country: "Brazil" },
    { name: "Recife", country: "Brazil" },
    { name: "Rio de Janeiro", country: "Brazil" },
    { name: "Salvador", country: "Brazil" },
    { name: "São Paulo", country: "Brazil" }
  ],
  "Cambodia": [
    { name: "Nationwide", country: "Cambodia" },
    { name: "Overseas", country: "Cambodia" },
    { name: "Phnom Penh", country: "Cambodia" },
    { name: "Siem Reap", country: "Cambodia" },
    { name: "Battambang", country: "Cambodia" }
  ],
  "Canada": [
    { name: "Nationwide", country: "Canada" },
    { name: "Overseas", country: "Canada" },
    { name: "Calgary", country: "Canada" },
    { name: "Edmonton", country: "Canada" },
    { name: "Halifax", country: "Canada" },
    { name: "Montreal", country: "Canada" },
    { name: "Ottawa", country: "Canada" },
    { name: "Quebec City", country: "Canada" },
    { name: "Toronto", country: "Canada" },
    { name: "Vancouver", country: "Canada" },
    { name: "Winnipeg", country: "Canada" }
  ],
  "Chile": [
    { name: "Nationwide", country: "Chile" },
    { name: "Overseas", country: "Chile" },
    { name: "Santiago", country: "Chile" },
    { name: "Valparaiso", country: "Chile" }
  ],
  "China": [
    { name: "Nationwide", country: "China" },
    { name: "Overseas", country: "China" },
    { name: "Beijing", country: "China" },
    { name: "Chengdu", country: "China" },
    { name: "Chongqing", country: "China" },
    { name: "Guangzhou", country: "China" },
    { name: "Hangzhou", country: "China" },
    { name: "Nanjing", country: "China" },
    { name: "Shanghai", country: "China" },
    { name: "Shenzhen", country: "China" },
    { name: "Tianjin", country: "China" },
    { name: "Wuhan", country: "China" },
    { name: "Xi'an", country: "China" }
  ],
  "Colombia": [
    { name: "Nationwide", country: "Colombia" },
    { name: "Overseas", country: "Colombia" },
    { name: "Bogota", country: "Colombia" },
    { name: "Medellin", country: "Colombia" }
  ],
  "Costa Rica": [
    { name: "Nationwide", country: "Costa Rica" },
    { name: "Overseas", country: "Costa Rica" },
    { name: "San Jose", country: "Costa Rica" }
  ],
  "Croatia": [
    { name: "Nationwide", country: "Croatia" },
    { name: "Overseas", country: "Croatia" },
    { name: "Zagreb", country: "Croatia" },
    { name: "Split", country: "Croatia" }
  ],
  "Czech Republic": [
    { name: "Nationwide", country: "Czech Republic" },
    { name: "Overseas", country: "Czech Republic" },
    { name: "Prague", country: "Czech Republic" },
    { name: "Brno", country: "Czech Republic" }
  ],
  "Denmark": [
    { name: "Nationwide", country: "Denmark" },
    { name: "Overseas", country: "Denmark" },
    { name: "Copenhagen", country: "Denmark" },
    { name: "Aarhus", country: "Denmark" }
  ],
  "Dominican Republic": [
    { name: "Nationwide", country: "Dominican Republic" },
    { name: "Overseas", country: "Dominican Republic" },
    { name: "Santo Domingo", country: "Dominican Republic" }
  ],
  "Ecuador": [
    { name: "Nationwide", country: "Ecuador" },
    { name: "Overseas", country: "Ecuador" },
    { name: "Quito", country: "Ecuador" },
    { name: "Guayaquil", country: "Ecuador" }
  ],
  "Egypt": [
    { name: "Nationwide", country: "Egypt" },
    { name: "Overseas", country: "Egypt" },
    { name: "Cairo", country: "Egypt" },
    { name: "Alexandria", country: "Egypt" }
  ],
  "Ethiopia": [
    { name: "Nationwide", country: "Ethiopia" },
    { name: "Overseas", country: "Ethiopia" },
    { name: "Addis Ababa", country: "Ethiopia" }
  ],
  "Finland": [
    { name: "Nationwide", country: "Finland" },
    { name: "Overseas", country: "Finland" },
    { name: "Helsinki", country: "Finland" },
    { name: "Espoo", country: "Finland" }
  ],
  "France": [
    { name: "Nationwide", country: "France" },
    { name: "Overseas", country: "France" },
    { name: "Bordeaux", country: "France" },
    { name: "Lille", country: "France" },
    { name: "Lyon", country: "France" },
    { name: "Marseille", country: "France" },
    { name: "Nantes", country: "France" },
    { name: "Nice", country: "France" },
    { name: "Paris", country: "France" },
    { name: "Strasbourg", country: "France" },
    { name: "Toulouse", country: "France" }
  ],
  "Germany": [
  { name: "Nationwide", country: "Germany" },
  { name: "Overseas", country: "Germany" },
  { name: "Aachen", country: "Germany" },
  { name: "Berlin", country: "Germany" },
  { name: "Bochum", country: "Germany" },
  { name: "Bonn", country: "Germany" },
  { name: "Bremen", country: "Germany" },
  { name: "Cologne", country: "Germany" },
  { name: "Dortmund", country: "Germany" },
  { name: "Dresden", country: "Germany" },
  { name: "Duisburg", country: "Germany" },
  { name: "Düsseldorf", country: "Germany" },
  { name: "Essen", country: "Germany" },
  { name: "Frankfurt", country: "Germany" },
  { name: "Gelsenkirchen", country: "Germany" },
  { name: "Hamburg", country: "Germany" },
  { name: "Hannover", country: "Germany" },
  { name: "Karlsruhe", country: "Germany" },
  { name: "Leipzig", country: "Germany" },
  { name: "Mannheim", country: "Germany" },
  { name: "Munich", country: "Germany" },
  { name: "Münster", country: "Germany" },
  { name: "Nuremberg", country: "Germany" },
  { name: "Stuttgart", country: "Germany" },
  { name: "Wiesbaden", country: "Germany" },
  { name: "Wuppertal", country: "Germany" }
 ],
  "Greece": [
    { name: "Nationwide", country: "Greece" },
    { name: "Overseas", country: "Greece" },
    { name: "Athens", country: "Greece" },
    { name: "Thessaloniki", country: "Greece" }
  ],
  "Hong Kong": [
    { name: "Nationwide", country: "Hong Kong" },
    { name: "Overseas", country: "Hong Kong" },
    { name: "Hong Kong", country: "Hong Kong" }
  ],
  "Hungary": [
    { name: "Nationwide", country: "Hungary" },
    { name: "Overseas", country: "Hungary" },
    { name: "Budapest", country: "Hungary" }
  ],
  "India": [
    { name: "Nationwide", country: "India" },
    { name: "Overseas", country: "India" },
    { name: "Ahmedabad", country: "India" },
    { name: "Bangalore", country: "India" },
    { name: "Bengaluru", country: "India" },
    { name: "Chennai", country: "India" },
    { name: "Delhi", country: "India" },
    { name: "Hyderabad", country: "India" },
    { name: "Jaipur", country: "India" },
    { name: "Kolkata", country: "India" },
    { name: "Lucknow", country: "India" },
    { name: "Mumbai", country: "India" },
    { name: "Pune", country: "India" }
  ],
  "Indonesia": [
    { name: "Nationwide", country: "Indonesia" },
    { name: "Overseas", country: "Indonesia" },
    { name: "Bandung", country: "Indonesia" },
    { name: "Jakarta", country: "Indonesia" },
    { name: "Medan", country: "Indonesia" },
    { name: "Surabaya", country: "Indonesia" }
  ],
  "Iran": [
    { name: "Nationwide", country: "Iran" },
    { name: "Overseas", country: "Iran" },
    { name: "Tehran", country: "Iran" },
    { name: "Mashhad", country: "Iran" }
  ],
  "Ireland": [
    { name: "Nationwide", country: "Ireland" },
    { name: "Overseas", country: "Ireland" },
    { name: "Dublin", country: "Ireland" },
    { name: "Cork", country: "Ireland" }
  ],
  "Israel": [
    { name: "Nationwide", country: "Israel" },
    { name: "Overseas", country: "Israel" },
    { name: "Jerusalem", country: "Israel" },
    { name: "Tel Aviv", country: "Israel" }
  ],
  "Italy": [
    { name: "Nationwide", country: "Italy" },
    { name: "Overseas", country: "Italy" },
    { name: "Bologna", country: "Italy" },
    { name: "Florence", country: "Italy" },
    { name: "Genoa", country: "Italy" },
    { name: "Milan", country: "Italy" },
    { name: "Naples", country: "Italy" },
    { name: "Palermo", country: "Italy" },
    { name: "Rome", country: "Italy" },
    { name: "Turin", country: "Italy" },
    { name: "Venice", country: "Italy" }
  ],
  "Japan": [
    { name: "Nationwide", country: "Japan" },
    { name: "Overseas", country: "Japan" },
    { name: "Aichi", country: "Japan" },
    { name: "Akita", country: "Japan" },
    { name: "Aomori", country: "Japan" },
    { name: "Chiba", country: "Japan" },
    { name: "Ehime", country: "Japan" },
    { name: "Fukui", country: "Japan" },
    { name: "Fukuoka", country: "Japan" },
    { name: "Fukushima", country: "Japan" },
    { name: "Gifu", country: "Japan" },
    { name: "Gunma", country: "Japan" },
    { name: "Hiroshima", country: "Japan" },
    { name: "Hokkaido", country: "Japan" },
    { name: "Hyogo", country: "Japan" },
    { name: "Ibaraki", country: "Japan" },
    { name: "Ishikawa", country: "Japan" },
    { name: "Iwate", country: "Japan" },
    { name: "Kagawa", country: "Japan" },
    { name: "Kagoshima", country: "Japan" },
    { name: "Kanagawa", country: "Japan" },
    { name: "Kochi", country: "Japan" },
    { name: "Kumamoto", country: "Japan" },
    { name: "Kyoto", country: "Japan" },
    { name: "Mie", country: "Japan" },
    { name: "Miyagi", country: "Japan" },
    { name: "Miyazaki", country: "Japan" },
    { name: "Nagano", country: "Japan" },
    { name: "Nagasaki", country: "Japan" },
    { name: "Nara", country: "Japan" },
    { name: "Niigata", country: "Japan" },
    { name: "Oita", country: "Japan" },
    { name: "Okayama", country: "Japan" },
    { name: "Okinawa", country: "Japan" },
    { name: "Osaka", country: "Japan" },
    { name: "Saga", country: "Japan" },
    { name: "Saitama", country: "Japan" },
    { name: "Shiga", country: "Japan" },
    { name: "Shimane", country: "Japan" },
    { name: "Shizuoka", country: "Japan" },
    { name: "Tochigi", country: "Japan" },
    { name: "Tokushima", country: "Japan" },
    { name: "Tokyo", country: "Japan" },
    { name: "Tottori", country: "Japan" },
    { name: "Toyama", country: "Japan" },
    { name: "Wakayama", country: "Japan" },
    { name: "Yamagata", country: "Japan" },
    { name: "Yamaguchi", country: "Japan" },
    { name: "Yamanashi", country: "Japan" }
  ],
  "Kazakhstan": [
    { name: "Nationwide", country: "Kazakhstan" },
    { name: "Overseas", country: "Kazakhstan" },
    { name: "Astana", country: "Kazakhstan" },
    { name: "Almaty", country: "Kazakhstan" }
  ],
  "Kenya": [
    { name: "Nationwide", country: "Kenya" },
    { name: "Overseas", country: "Kenya" },
    { name: "Nairobi", country: "Kenya" },
    { name: "Mombasa", country: "Kenya" }
  ],
  "Kuwait": [
    { name: "Nationwide", country: "Kuwait" },
    { name: "Overseas", country: "Kuwait" },
    { name: "Kuwait City", country: "Kuwait" }
  ],
  "Malaysia": [
    { name: "Nationwide", country: "Malaysia" },
    { name: "Overseas", country: "Malaysia" },
    { name: "Johor Bahru", country: "Malaysia" },
    { name: "Kuala Lumpur", country: "Malaysia" },
    { name: "Penang", country: "Malaysia" }
  ],
  "Mexico": [
    { name: "Nationwide", country: "Mexico" },
    { name: "Overseas", "country": "Mexico" },
    { name: "Guadalajara", country: "Mexico" },
    { name: "Mexico City", country: "Mexico" },
    { name: "Monterrey", country: "Mexico" }
  ],
  "Morocco": [
    { name: "Nationwide", country: "Morocco" },
    { name: "Overseas", country: "Morocco" },
    { name: "Casablanca", country: "Morocco" },
    { name: "Rabat", country: "Morocco" }
  ],
  "Myanmar": [
    { name: "Nationwide", country: "Myanmar" },
    { name: "Overseas", country: "Myanmar" },
    { name: "Yangon", country: "Myanmar" },
    { name: "Mandalay", country: "Myanmar" }
  ],
  "Netherlands": [
    { name: "Nationwide", country: "Netherlands" },
    { name: "Overseas", country: "Netherlands" },
    { name: "Amsterdam", country: "Netherlands" },
    { name: "Rotterdam", country: "Netherlands" },
    { name: "The Hague", country: "Netherlands" }
  ],
  "New Zealand": [
    { name: "Nationwide", country: "New Zealand" },
    { name: "Overseas", country: "New Zealand" },
    { name: "Auckland", country: "New Zealand" },
    { name: "Christchurch", country: "New Zealand" },
    { name: "Wellington", country: "New Zealand" }
  ],
  "Nigeria": [
    { name: "Nationwide", country: "Nigeria" },
    { name: "Overseas", country: "Nigeria" },
    { name: "Lagos", country: "Nigeria" },
    { name: "Abuja", country: "Nigeria" }
  ],
  "Norway": [
    { name: "Nationwide", country: "Norway" },
    { name: "Overseas", country: "Norway" },
    { name: "Oslo", country: "Norway" },
    { name: "Bergen", country: "Norway" }
  ],
  "Pakistan": [
    { name: "Nationwide", country: "Pakistan" },
    { name: "Overseas", country: "Pakistan" },
    { name: "Karachi", country: "Pakistan" },
    { name: "Lahore", country: "Pakistan" },
    { name: "Islamabad", country: "Pakistan" }
  ],
  "Peru": [
    { name: "Nationwide", country: "Peru" },
    { name: "Overseas", country: "Peru" },
    { name: "Lima", country: "Peru" }
  ],
  "Philippines": [
    { name: "Nationwide", country: "Philippines" },
    { name: "Overseas", country: "Philippines" },
    { name: "Baguio", country: "Philippines" },
    { name: "Cagayan de Oro", country: "Philippines" },
    { name: "Caloocan", country: "Philippines" },
    { name: "Cebu", country: "Philippines" },
    { name: "Davao", country: "Philippines" },
    { name: "Iloilo City", country: "Philippines" },
    { name: "Las Pinas", country: "Philippines" },
    { name: "Makati", country: "Philippines" },
    { name: "Mandaluyong", country: "Philippines" },
    { name: "Manila", country: "Philippines" },
    { name: "Marikina", country: "Philippines" },
    { name: "Pasay", country: "Philippines" },
    { name: "Pasig", country: "Philippines" },
    { name: "Quezon City", country: "Philippines" },
    { name: "San Juan", country: "Philippines" },
    { name: "Taguig", country: "Philippines" },
    { name: "Zamboanga City", country: "Philippines" }
  ],
  "Poland": [
    { name: "Nationwide", country: "Poland" },
    { name: "Overseas", country: "Poland" },
    { name: "Warsaw", country: "Poland" },
    { name: "Krakow", country: "Poland" }
  ],
  "Portugal": [
    { name: "Nationwide", country: "Portugal" },
    { name: "Overseas", country: "Portugal" },
    { name: "Lisbon", country: "Portugal" },
    { name: "Porto", country: "Portugal" }
  ],
  "Qatar": [
    { name: "Nationwide", country: "Qatar" },
    { name: "Overseas", country: "Qatar" },
    { name: "Doha", country: "Qatar" }
  ],
  "Romania": [
    { name: "Nationwide", country: "Romania" },
    { name: "Overseas", country: "Romania" },
    { name: "Bucharest", country: "Romania" }
  ],
  "Russia": [
    { name: "Nationwide", country: "Russia" },
    { name: "Overseas", country: "Russia" },
    { name: "Moscow", country: "Russia" },
    { name: "Saint Petersburg", country: "Russia" }
  ],
  "Saudi Arabia": [
    { name: "Nationwide", country: "Saudi Arabia" },
    { name: "Overseas", country: "Saudi Arabia" },
    { name: "Dammam", country: "Saudi Arabia" },
    { name: "Khobar", country: "Saudi Arabia" },
    { name: "Jeddah", country: "Saudi Arabia" },
    { name: "Mecca", country: "Saudi Arabia" },
    { name: "Medina", country: "Saudi Arabia" },
    { name: "Riyadh", country: "Saudi Arabia" },
    { name: "Taif", country: "Saudi Arabia" },
    { name: "Yanbu", country: "Saudi Arabia" }
  ],
  "Singapore": [
    { name: "Nationwide", country: "Singapore" },
    { name: "Overseas", country: "Singapore" },
    { name: "Singapore", country: "Singapore" }
  ],
  "South Africa": [
    { name: "Nationwide", country: "South Africa" },
    { name: "Overseas", country: "South Africa" },
    { name: "Cape Town", country: "South Africa" },
    { name: "Durban", country: "South Africa" },
    { name: "Johannesburg", country: "South Africa" },
    { name: "Pretoria", country: "South Africa" }
  ],
  "South Korea": [
    { name: "Nationwide", country: "South Korea" },
    { name: "Overseas", country: "South Korea" },
    { name: "Busan", country: "South Korea" },
    { name: "Daegu", country: "South Korea" },
    { name: "Daejeon", country: "South Korea" },
    { name: "Gwangju", country: "South Korea" },
    { name: "Incheon", country: "South Korea" },
    { name: "Seoul", country: "South Korea" },
    { name: "Ulsan", country: "South Korea" },
    { name: "Yangju", country: "South Korea" }
  ],
  "Spain": [
    { name: "Nationwide", country: "Spain" },
    { name: "Overseas", country: "Spain" },
    { name: "Barcelona", country: "Spain" },
    { name: "Bilbao", country: "Spain" },
    { name: "Burgos", country: "Spain" },
    { name: "Cadiz", country: "Spain" },
    { name: "Cantabria", country: "Spain" },
    { name: "Castellon", country: "Spain" },
    { name: "Cordoba", country: "Spain" },
    { name: "Cuenca", country: "Spain" },
    { name: "Girona", country: "Spain" },
    { name: "Granada", country: "Spain" },
    { name: "Huelva", country: "Spain" },
    { name: "Jaen", country: "Spain" },
    { name: "La Rioja", country: "Spain" },
    { name: "Las Palmas", country: "Spain" },
    { name: "Leon", country: "Spain" },
    { name: "Lleida", country: "Spain" },
    { name: "Madrid", country: "Spain" },
    { name: "Malaga", country: "Spain" },
    { name: "Murcia", country: "Spain" },
    { name: "Navarra", country: "Spain" },
    { name: "Ourense", country: "Spain" },
    { name: "Palma de Mallorca", country: "Spain" },
    { name: "Pontevedra", country: "Spain" },
    { name: "Seville", country: "Spain" },
    { name: "Valencia", country: "Spain" },
    { name: "Vigo", country: "Spain" },
    { name: "Zaragoza", country: "Spain" }
  ],
  "Sudan": [
    { name: "Nationwide", country: "Sudan" },
    { name: "Overseas", country: "Sudan" },
    { name: "Khartoum", country: "Sudan" }
  ],
  "Sweden": [
    { name: "Nationwide", country: "Sweden" },
    { name: "Overseas", country: "Sweden" },
    { name: "Gothenburg", country: "Sweden" },
    { name: "Malmo", country: "Sweden" },
    { name: "Stockholm", country: "Sweden" },
    { name: "Uppsala", country: "Sweden" },
    { name: "Växjö", country: "Sweden" },
    { name: "Västerås", country: "Sweden" },
    { name: "Västervik", country: "Sweden" }
  ],
  "Switzerland": [
    { name: "Nationwide", country: "Switzerland" },
    { name: "Overseas", country: "Switzerland" },
    { name: "Basel", country: "Switzerland" },
    { name: "Bern", country: "Switzerland" },
    { name: "Geneva", country: "Switzerland" },
    { name: "Zurich", country: "Switzerland" }
  ],
  "Taiwan": [
    { name: "Nationwide", country: "Taiwan" },
    { name: "Overseas", country: "Taiwan" },
    { name: "Taipei", country: "Taiwan" },
    { name: "Kaohsiung", country: "Taiwan" },
    { name: "Taichung", country: "Taiwan" },
    { name: "Tainan", country: "Taiwan" }
  ],
  "Tanzania": [
    { name: "Nationwide", country: "Tanzania" },
    { name: "Overseas", country: "Tanzania" },
    { name: "Dar es Salaam", country: "Tanzania" }
  ],
  "Thailand": [
    { name: "Nationwide", country: "Thailand" },
    { name: "Overseas", country: "Thailand" },
    { name: "Bangkok", country: "Thailand" },
    { name: "Chiang Mai", country: "Thailand" },
    { name: "Pattaya", country: "Thailand" },
    { name: "Phuket", country: "Thailand" },
    { name: "Samut Prakan", country: "Thailand" },
    { name: "Surat Thani", country: "Thailand" },
    { name: "Trang", country: "Thailand" },
    { name: "Udon Thani", country: "Thailand" },
    { name: "Yala", country: "Thailand" }
  ],
  "Turkey": [
    { name: "Nationwide", country: "Turkey" },
    { name: "Overseas", country: "Turkey" },
    { name: "Ankara", country: "Turkey" },
    { name: "Antalya", country: "Turkey" },
    { name: "Balikesir", country: "Turkey" },
    { name: "Bursa", country: "Turkey" },
    { name: "Denizli", country: "Turkey" },
    { name: "Diyarbakir", country: "Turkey" },
    { name: "Edirne", country: "Turkey" },
    { name: "Erzurum", country: "Turkey" },
    { name: "Gaziantep", country: "Turkey" },
    { name: "Hatay", country: "Turkey" },
    { name: "Istanbul", country: "Turkey" },
    { name: "Izmir", country: "Turkey" },
    { name: "Kayseri", country: "Turkey" },
    { name: "Kilis", country: "Turkey" },
    { name: "Malatya", country: "Turkey" },
    { name: "Manisa", country: "Turkey" },
    { name: "Mersin", country: "Turkey" },
    { name: "Mugla", country: "Turkey" },
    { name: "Samsun", country: "Turkey" },
    { name: "Sinop", country: "Turkey" },
    { name: "Sivas", country: "Turkey" },
    { name: "Trabzon", country: "Turkey" },
    { name: "Tunceli", country: "Turkey" },
    { name: "Usak", country: "Turkey" },
    { name: "Van", country: "Turkey" },
    { name: "Yozgat", country: "Turkey" }
  ],
  "Uganda": [
    { name: "Nationwide", country: "Uganda" },
    { name: "Overseas", country: "Uganda" },
    { name: "Entebbe", country: "Uganda" },
    { name: "Jinja", country: "Uganda" },
    { name: "Kampala", country: "Uganda" },
    { name: "Kigali", country: "Uganda" },
    { name: "Kinshasa", country: "Uganda" },
    { name: "Lubumbashi", country: "Uganda" },
    { name: "Lusaka", country: "Uganda" },
    { name: "Maseru", country: "Uganda" },
    { name: "Mbabane", country: "Uganda" }
  ],
  "Ukraine": [
    { name: "Nationwide", country: "Ukraine" },
    { name: "Overseas", country: "Ukraine" },
    { name: "Kyiv", country: "Ukraine" },
    { name: "Kharkiv", country: "Ukraine" }
  ],
  "United Arab Emirates": [
    { name: "Nationwide", country: "United Arab Emirates" },
    { name: "Overseas", country: "United Arab Emirates" },
    { name: "Abu Dhabi", country: "United Arab Emirates" },
    { name: "Dubai", country: "United Arab Emirates" },
    { name: "Sharjah", country: "United Arab Emirates" }
  ],
  "United Kingdom": [
    { name: "Nationwide", country: "United Kingdom" },
    { name: "Overseas", country: "United Kingdom" },
    { name: "Birmingham", country: "United Kingdom" },
    { name: "Bristol", country: "United Kingdom" },
    { name: "Edinburgh", country: "United Kingdom" },
    { name: "Glasgow", country: "United Kingdom" },
    { name: "Leeds", country: "United Kingdom" },
    { name: "Liverpool", country: "United Kingdom" },
    { name: "London", country: "United Kingdom" },
    { name: "Manchester", country: "United Kingdom" },
    { name: "Newcastle", country: "United Kingdom" },
    { name: "Nottingham", country: "United Kingdom" },
    { name: "Oxford", country: "United Kingdom" },
    { name: "Sheffield", country: "United Kingdom"}
  ],
  "United States": [
    { name: "Nationwide", country: "United States" },
    { name: "Overseas", country: "United States" },
    { name: "Atlanta", country: "United States" },
    { name: "Boston", country: "United States" },
    { name: "Chicago", country: "United States" },
    { name: "Dallas", country: "United States" },
    { name: "Houston", country: "United States" },
    { name: "Los Angeles", country: "United States" },
    { name: "Miami", country: "United States" },
    { name: "New York", country: "United States" },
    { name: "Philadelphia", country: "United States" },
    { name: "Phoenix", country: "United States" },
    { name: "San Francisco", country: "United States" },
    { name: "Seattle", country: "United States" },
    { name: "Washington D.C.", country: "United States" }
  ],
  "Uruguay": [
    { name: "Nationwide", country: "Uruguay" },
    { name: "Overseas", country: "Uruguay" },
    { name: "Montevideo", country: "Uruguay" },
    { name: "Salto", country: "Uruguay" },
    { name: "Paysandu", country: "Uruguay" },
    { name: "Rivera", country: "Uruguay" },
    { name: "Rivera Maya", country: "Uruguay" }
  ],
  "Venezuela": [
    { name: "Nationwide", country: "Venezuela" },
    { name: "Overseas", country: "Venezuela" },
    { name: "Barquisimeto", country: "Venezuela" },
    { name: "Caracas", country: "Venezuela" },
    { name: "Maracaibo", country: "Venezuela" },
    { name: "Valencia", country: "Venezuela" }
  ],
  "Vietnam": [
    { name: "Nationwide", country: "Vietnam" },
    { name: "Overseas", country: "Vietnam" },
    { name: "Da Nang", country: "Vietnam" },
    { name: "Hanoi", country: "Vietnam" },
    { name: "Ho Chi Minh City", country: "Vietnam" }
  ]
};

// Function to add a new city
export async function addCity(
  countryName: string,
  cityName: string
): Promise<void> {
  const formattedCityName = cityName.trim();

  try {
    // First update the in-memory list
    if (!cityList[countryName]) {
      cityList[countryName] = [];
    }

    const cityExists = cityList[countryName].some(
      (city) => city.name.toLowerCase() === formattedCityName.toLowerCase()
    );

    if (!cityExists) {
      // Add to in-memory list
      cityList[countryName].push({
        name: formattedCityName,
        country: countryName,
      });

      cityList[countryName].sort((a, b) => a.name.localeCompare(b.name));

      // Call API to persist to file
      const response = await fetch("/api/location/city", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          countryName,
          cityName: formattedCityName,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to persist city");
      }
    }
  } catch (error) {
    console.error("Error adding city:", error);
    throw error;
  }
}

