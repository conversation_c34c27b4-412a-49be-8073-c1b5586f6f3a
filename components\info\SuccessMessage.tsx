import { FaRegCheckCircle } from "react-icons/fa";

interface SuccessMessageProps {
    message?: string;
};

export const SuccessMessage = ({message}: SuccessMessageProps) => {

    if (!message) return null;

    return (
        <div className="bg-emerald-500/15 p-3 rounded-md flex
        items-center gap-x-2 text-sm text-bg-emerald-500">
            <FaRegCheckCircle className="h-4 w-4" />
            <p>{message}</p>
        </div>
    );
}