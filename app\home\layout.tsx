"use client";

import { UserFooter } from "@/components/footer/UserFooter";
import { UserTopbar } from "@/components/header/UserTopbar";
import { Sidebar } from "@/components/sidebar/UserSideBar"; // Assuming UserSideBar is the correct import
import { useSession } from "next-auth/react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Script from 'next/script'

export default function HomeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
    const [collapsed, setCollapsed] = useState(false);
    const router = useRouter();
    const { data: session, status: sessionStatus } = useSession({required: true});

    // console.log({HOME: session, sessionStatus});


    return (
        <div className="flex w-full flex-col min-h-screen bg-primary-foreground">
            <div className="flex flex-1 transition-all duration-300 ease-in-out">
              <Sidebar collapsed={collapsed} setCollapsed={setCollapsed} />
              <div
                className={`flex-1 flex flex-col transition-all duration-300 ease-in-out pt-14 ${
                  collapsed ? "ml-16" : "ml-56"
                }`}
              > 
                <UserTopbar collapsed={collapsed} />
                <main className="flex-1 p-6">{children}</main>
                <UserFooter />
              </div>
            </div>
            <Script 
                src={`https://www.paypal.com/sdk/js?client-id=${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID}&vault=true&intent=subscription`}
                strategy="afterInteractive"
            />
        </div>
    );
}