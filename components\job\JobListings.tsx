import { prisma } from "@/lib/prisma/prismaClient";
import { EmptyState } from "../general/EmptyState";
import { JobCard } from "./JobCard";
import { MainPagination } from "../general/MainPagination";
import { JobPostStatus, UserRole } from "@prisma/client";
import { auth } from "@/lib/auth/auth";

async function getData({
  page = 1,
  pageSize = 20,
  jobTypes = [],
  location = "",
  isAdmin = false,
}: {
  page: number;
  pageSize: number;
  jobTypes: string[];
  location: string;
  isAdmin?: boolean;
}) {
  const skip = (page - 1) * pageSize;

  const where = {
    ...(!isAdmin && { status: JobPostStatus.ACTIVE }),
    ...(jobTypes.length > 0 && {
      employmentType: {
        in: jobTypes,
      },
    }),
    ...(location &&
      location !== "worldwide" && {
        location: location,
      }),
  };

  const [data, totalCount] = await Promise.all([
    prisma.jobPost.findMany({
      where: where,
      take: pageSize,
      skip: skip,
      select: {
        id: true,
        jobTitle: true,
        employmentType: true,
        experienceLevel: true,
        location: true,
        country: true,
        salaryCurrency: true,
        salaryFrom: true,
        salaryTo: true,
        jobDescription: true,
        listingDuration: true,
        interviewType: true,
        localRemoteWork: true,
        overseasRemoteWork: true,
        skills: true,
        languageRequirements: true,
        tags: true,
        status: true,
        createdAt: true,
        company: {
          select: {
            name: true,
            logo: true,
            location: true,
            about: true,
            website: true,
            xAccount: true,
            linkedIn: true,
            tin: true,
            benefits: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    }),

    prisma.jobPost.count({
      where: where,
    }),
  ]);

  return {
    jobs: data,
    totalPages: Math.ceil(totalCount / pageSize),
  };
}

export async function JobListings({
  currentPage,
  jobTypes,
  location,
}: {
  currentPage: number;
  jobTypes: string[];
  location: string;
}) {
  const session = await auth();
  let isAdmin = false;

  if (session?.user?.role === UserRole.ADMIN) {
    isAdmin = true;
  }

  const { jobs, totalPages } = await getData({
    page: currentPage,
    pageSize: 30, // Changed to 30 to better fit 3 columns with 10 items each
    jobTypes: jobTypes,
    location: location,
    isAdmin: isAdmin,
  });

  return (
    <>
      {jobs.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {jobs.map((job) => (
            <JobCard key={job.id} job={job} />
          ))}
        </div>
      ) : (
        <EmptyState
          title="No Jobs Found!"
          description="Try searching for a different job title or location."
          buttonText="Clear all filters"
          href="/"
        />
      )}

      <div className="flex justify-center mt-6">
        <MainPagination totalPages={totalPages} currentPage={currentPage} />
      </div>
    </>
  );
}
