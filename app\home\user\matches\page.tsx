import { getUserJobMatches } from "@/actions/job/getJob";
import { GetUserResumeId } from "@/actions/resume/userResumeActions";
import { EmptyState } from "@/components/general/EmptyState";
import { MainPagination } from "@/components/general/MainPagination";
import { UserJobMatches } from "@/components/user/UserJobMatches";
import { auth } from "@/lib/auth/auth";

const MATCHES_PAGE_SIZE = 20; // Define page size

export default async function JobMatchesPage(props: {
  // If this page also used route params, they would be typed similarly:
  // params: Promise<{ [key: string]: string | string[] | undefined }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const session = await auth();
  const resolvedSearchParams = await props.searchParams;
  const currentPage = Number(resolvedSearchParams?.page) || 1;

  if (!session?.user?.id) {
    return <div>User not authenticated.</div>;
  }

  const resume = await GetUserResumeId(session?.user?.id as string);
  if (!resume?.id) {
    // Handle case where resume ID is not found (e.g., user has no resume)
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <h1 className="text-2xl font-semibold">Job Matches</h1>
        <EmptyState
          title="No Resume Found"
          description="Please upload or create a resume to see job matches."
          buttonText="Go to Profile"
          href="/home/<USER>/profile"
        />
      </div>
    );
  }

  const result = await getUserJobMatches(
    resume.id,
    currentPage,
    MATCHES_PAGE_SIZE
  );

  if (result?.error || !result?.matches || result.matches.length === 0) {
    return (
      <div className="grid grid-cols-1 mt-5 gap-4">
        <h1 className="text-2xl font-semibold">Job Matches</h1>
        <EmptyState
          title="No Job Matches Yet"
          description={
            result?.error ||
            "We found no job matches for you yet. Try broadening your profile or check back later."
          }
          buttonText="Find a Job"
          href="/public/job/list"
        />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 mt-5 gap-4">
      <UserJobMatches data={result.matches} />
      {result.totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <MainPagination
            totalPages={result.totalPages}
            currentPage={currentPage}
          />
        </div>
      )}
    </div>
  );
}
