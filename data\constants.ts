import { people01, people02, people03, facebook, instagram, linkedin, airbnb, binance, coinbase, dropbox, send, shield, star, x, doc, match, locked } from "@/public/assets";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export const navLinks = [
    {
        id: "home",
        title: "Home",
        link: `${APP_URL}`,
    },
    {
        id: "about",
        title: "About",
        link: `${APP_URL}/public/about`,
    },
    {
        id: "jobs",
        title: "Jobs",
        link: `${APP_URL}/public/job/list`,
    },
    {
        id: "features",
        title: "Features",
        link: `${APP_URL}/public#features`,
    },
    {
        id: "signup",
        title: "Signup",
        link: `${APP_URL}/auth/signup`,
    },
];

export const features = [
    {
        id: "feature-1",
        icon: star,
        title: "Rewards",
        content:
            "Share and invite others, you'll get tokens in exchange.",
    },
    {
        id: "feature-2",
        icon: doc,
        title: "Unlimited Resume",
        content:
            "Upload as many resumes as you like — our AI will analyze and match each one to the best-fit jobs.",
    },
    {
        id: "feature-3",
        icon: match,
        title: "Unlimited Job Matching",
        content:
            "No limits, no guesswork. Upload all your jobs and resumes and let our AI match each one to the perfect job — fast, accurate, and always working for you.",
    },
    {
        id: "feature-4",
        icon: shield,
        title: "100% Secured",
        content:
            "We take proactive steps to make sure your information and transactions are secure.",
    },
    {
        id: "feature-5",
        icon: locked,
        title: "Your Data Encrypted",
        content:
            "To comply with global privacy regulations, all personal and sensitive information is securely encrypted in our database.",
    },
];

export const feedback = [
    {
        id: "feedback-1",
        content:
            "Money is only a tool. It will take you wherever you wish, but it will not replace you as the driver.",
        name: "Herman Jensen",
        title: "Founder & Leader",
        img: people01,
    },
    {
        id: "feedback-2",
        content:
            "Money makes your life easier. If you're lucky to have it, you're lucky.",
        name: "Steve Mark",
        title: "Founder & Leader",
        img: people02,
    },
    {
        id: "feedback-3",
        content:
            "It is usually people in the money business, finance, and international trade that are really rich.",
        name: "Kenn Gallagher",
        title: "Founder & Leader",
        img: people03,
    },
];

export const stats = [
    {
        id: "stats-1",
        title: "User Active",
        value: "3800+",
    },
    {
        id: "stats-2",
        title: "Trusted by Company",
        value: "230+",
    },
    {
        id: "stats-3",
        title: "Transaction",
        value: "$230M+",
    },
];

export const footerLinks = [
    {
        id: "footerLinks-1",
        title: "Company",
        links: [
            {
                name: "About",
                link: `${APP_URL}/public/about`,
            },
            {
                name: "Pricing",
                link: `${APP_URL}/public/pricing`,
            },
            {
                name: "Signup",
                link: `${APP_URL}/auth/signup`,
            },
        ],
    },
    {
        id: "footerLinks-2",
        title: "Legal",
        links: [
            {
                name: "Privacy",
                link: `${APP_URL}/public/privacy`,
            },
            {
                name: "Terms of Services",
                link: `${APP_URL}/public/terms`,
            },
        ],
    },
    // {
    //     id: "footerLinks-2",
    //     title: "Community",
    //     links: [
    //         {
    //             name: "Help Center",
    //             // link: "https://athena.edisonaix.com/help",
    //             link: "#",
    //         },
    //         {
    //             name: "Feedback",
    //             // link: "https://www.athena.edisonaix.com/feedback",
    //             link: "#",
    //         },
    //         {
    //             name: "Blog",
    //             // link: "https://athena.edisonaix.com/blog/",
    //             link: "#",
    //         },
    //         {
    //             name: "Newsletters",
    //             // link: "https://athena.edisonaix.com/newsletters",
    //             link: "#",
    //         },
    //     ],
    // },
    // {
    //     id: "footerLinks-3",
    //     title: "Partner",
    //     links: [
    //         {
    //             name: "Our Partners",
    //             // link: "https://athena.edisonaix.com/partner",
    //             link: "#",
    //         },
    //         {
    //             name: "Become a Partner",
    //             // link: "https://athena.edisonaix.com/becomepartner",
    //             link: "#",
    //         },
    //     ],
    // },
];

export const socialMedia = [
    // {
    //     id: "social-facebook",
    //     icon: facebook,
    //     link: "https://www.facebook.com/edisonaix",
    // },
    // {
    //     id: "social-x",
    //     icon: x,
    //     link: "https://www.x.com/edisonaix",
    // },
    {
        id: "social-linkedin",
        icon: linkedin,
        link: "https://www.linkedin.com/company/edisonaixcom/",
    },
];

export const clients = [
    {
        id: "client-1",
        logo: airbnb,
    },
    {
        id: "client-2",
        logo: binance,
    },
    {
        id: "client-3",
        logo: coinbase,
    },
    {
        id: "client-4",
        logo: dropbox,
    },
];