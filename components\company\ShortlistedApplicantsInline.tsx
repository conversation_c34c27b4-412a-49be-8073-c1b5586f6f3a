"use client";

import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Table, TableBody, TableCell, TableRow } from "../ui/table";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { ChevronLeftCircle, ChevronRightCircle, EyeIcon } from "lucide-react";
import Image from "next/image";
import { Pagination } from "../ui/pagination";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { normalizeHeaders } from "@/utils/stringHelpters";
import { JobResumeNotesDialog } from "./JobResumeNotesDialog";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

interface ShortlistedApplicantsInlineProps {
  jobId: string;
  jobTitle: string;
  shortlistedApplicants: any[];
  isOddRow?: boolean;
}

export function ShortlistedApplicantsInline({
  jobId,
  jobTitle,
  shortlistedApplicants,
  isOddRow = false,
}: ShortlistedApplicantsInlineProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  if (!shortlistedApplicants || shortlistedApplicants.length === 0) {
    return (
      <div
        className={`text-center py-4 text-muted-foreground ${isOddRow ? "bg-muted/30" : ""}`}
      >
        No shortlisted applicants yet
      </div>
    );
  }

  // Pagination logic
  const totalPages = Math.ceil(shortlistedApplicants.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentApplicants = shortlistedApplicants.slice(startIndex, endIndex);

  return (
    <>
      <Table className="w-full table-fixed">
        <TableBody>
          {currentApplicants.map((applicant) => (
            <TableRow
              key={applicant.id}
              className={`border-b-0 ${isOddRow ? "bg-muted/30" : ""}`}
            >
              <TableCell style={{ width: "34.78%" }} className="py-0">
                <div className="flex items-center gap-2 w-full">
                  <Image
                    src={applicant.resume?.picture || "/icons/profile.png"}
                    alt={`${applicant.resume?.name || ""}`}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="text-sm block overflow-hidden text-ellipsis whitespace-nowrap min-w-0 flex-1">
                    {(normalizeHeaders(applicant.resume?.name) || "").trim()}
                  </span>
                </div>
              </TableCell>
              <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <MatchScoreCircle
                  score={applicant.resume?.resumeMatches?.[0]?.overall_score}
                />
              </TableCell>
                <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <ResumeMatchDialog
                  resumeMatch={applicant.resume?.resumeMatches?.[0]}
                  applicantName={`${applicant.resume?.name || ""}`}
                />
              </TableCell>
                <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <a
                  href={`/home/<USER>/resumedoc/${applicant.resume?.id}`}
                  target="_blank"
                  className="text-primary hover:underline cursor-pointer inline-flex justify-center"
                  title="View Resume"
                >
                  <Button
                    variant="ghost"
                    className="text-primary hover:underline cursor-pointer"
                    title="View Resume"
                  >
                    <EyeIcon className="w-5 h-5" />
                  </Button>
                </a>
              </TableCell>
                <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                <div className="flex justify-center">
                  <ShortlistCheckbox
                    jobId={jobId}
                    resumeId={applicant.resume?.id || ""}
                    initialShortlisted={true}
                  />
                </div>
              </TableCell>
                <TableCell style={{ width: "13.04%" }} className="text-center py-0">
                    <div className="flex justify-center">
                        <JobResumeNotesDialog
                            jobId={jobId || ""}
                            resumeId={applicant.resume?.id || ""}
                            jobTitle={jobTitle}
                            resumeName={applicant.resume?.name ? applicant.resume?.name : `${applicant.resume?.user?.firstName} ${applicant.resume?.lastName}` || ""}
                        />
                    </div>
                </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {totalPages > 1 && (
        <div className="flex justify-center mt-2 pb-2">
          <Pagination className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              title="Previous Page"
              className="cursor-pointer"
            >
              <ChevronLeftCircle className="h-5 w-5" />
            </Button>
            <span className="mx-4 text-xs">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              title="Next Page"
              className="cursor-pointer"
            >
              <ChevronRightCircle className="h-5 w-5" />
            </Button>
          </Pagination>
        </div>
      )}
    </>
  );
}
