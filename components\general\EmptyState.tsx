import { Ban, ChevronLeftCircle } from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "../ui/button";

interface EmptyStateProps {
  title: string;
  description: string;
  buttonText: string;
  href: string;
}

export function EmptyState( { title, description, buttonText, href }: EmptyStateProps ) {
  return (
    <div className="flex flex-col flex-1 h-full items-center justify-center rounded-md border border-dashed p-8 gap-2">
      <div className="flex size-20 items-center justify-center rounded-full bg-primary/10">
        <Ban className="size-10 text-primary" />
      </div>
        <h2 className="text-lg font-semibold text-gray-200">{title}</h2>
        <p className="text-sm text-center leading-tight text-muted-foreground max-w-sm text-balance mb-8">{description}</p>

        <Link href={href} className={buttonVariants()}>
            <ChevronLeftCircle /> {buttonText}
        </Link>
    </div>
  );
}
