// Helper function to determine color based on score
const getScoreColor = (score: number) => {
  if (score >= 90) return "bg-emerald-500";
  if (score >= 80) return "bg-green-500";
  if (score >= 70) return "bg-yellow-500";
  if (score >= 60) return "bg-orange-500";
  return "bg-red-500";
};

// Score circle component
export const MatchScoreCircle = ({ score }: { score: number | null | undefined }) => {
  // Default to 0 if score is undefined or null
  const displayScore = score ?? 0;

  return (
    <div className="flex items-center justify-center">
      <div
        className={`${getScoreColor(displayScore)} text-white rounded-full w-6 h-6 flex items-center justify-center text-xs`}
      >
        {displayScore}
      </div>
    </div>
  );
};