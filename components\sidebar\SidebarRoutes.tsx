"use client";

import { useSession } from "next-auth/react";
import { 
    LayoutDashboard, 
    UserCircle, 
    Heart, 
    Briefcase, 
    Building2, 
    Users, 
    Building,
    UserCog,
    GraduationCap,
    ListCheck,
    FolderArchive,
    PenBoxIcon,
    FileUser,
    FilePenLine,
    FileInputIcon,
    FileTextIcon,
    SquareUser,
    Warehouse,
    Banknote,
    TicketCheck,
    MessageSquareText
} from "lucide-react";
import { SidebarItem } from "@/components/sidebar/SidebarItem";
import { JSX, useEffect, useState } from "react";

const userRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: FileInputIcon,        
        label: "Job Matches",
        href: "/home/<USER>/matches",
        color: "text-emerald-500"
    },
    {
        icon: FilePenLine,        
        label: "Applications",
        href: "/home/<USER>/applications",
        color: "text-amber-500"
    },    
    {
        icon: Heart,
        label: "Favorites",
        href: "/home/<USER>/favorites",
        color: "text-rose-500"  
    },
    {
        icon: FileTextIcon,        
        label: "Resume",
        href: "/home/<USER>/resume",
        color: "text-teal-500"
    },
    {
        icon: UserCircle,        
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-purple-500"
    }
]

const companyRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: PenBoxIcon,         
        label: "Job List",
        href: "/home/<USER>/job/list",
        color: "text-amber-500"
    },
    {
        icon: FileInputIcon,        
        label: "Job Matches",
        href: "/home/<USER>/matches",
        color: "text-emerald-500"
    },
    {
        icon: FileUser,
        label: "Applications",
        href: "/home/<USER>/applications",
        color: "text-rose-500"
    },
    {
        icon: ListCheck,
        label: "Shortlist",
        href: "/home/<USER>/shortlist",
        color: "text-teal-500"
    },
    {
        icon: Users,
        label: "Resumes",
        href: "/home/<USER>/resume/list",
        color: "text-green-500"
    },
    {
        icon: FolderArchive,
        label: "Uploads",
        href: "/home/<USER>/files",
        color: "text-yellow-400"
    },
    {
        icon: Building2,         
        label: "Company",
        href: "/home/<USER>/account",
        color: "text-indigo-500"
    },
    {
        icon: UserCircle,
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-purple-500"
    },
]

const adminRoutes = [
    {
        icon: LayoutDashboard,
        label: "Dashboard",
        href: "/home/<USER>/dashboard",
        color: "text-sky-500"
    },
    {
        icon: Briefcase,
        label: "Job List",
        href: "/home/<USER>/company/job/list",
        color: "text-amber-500"
    },
    {
        icon: Building,
        label: "Companies",
        href: "/home/<USER>/company/list",
        color: "text-indigo-500"
    },
    {
        icon: UserCog,
        label: "HR Managers",
        href: "/home/<USER>/company/manager/list",
        color: "text-fuchsia-500"
    },
    {
        icon: GraduationCap,
        label: "Job Seekers",
        href: "/home/<USER>/jobseeker/list",
        color: "text-blue-500"
    },
    {
        icon: SquareUser,
        label: "Recruiters",
        href: "/home/<USER>/recruiter/list",
        color: "text-red-300"
    },
    {
        icon: Warehouse,
        label: "Tenants",
        href: "/home/<USER>/tenant/list",
        color: "text-slate-500"
    },
    {
        icon: TicketCheck,
        label: "Subscriptions",
        href: "/home/<USER>/subscription/list",
        color: "text-violet-500"
    },
    {
        icon: Banknote,
        label: "Payments",
        href: "/home/<USER>/payment/list",
        color: "text-green-500"
    },
    {
        icon: UserCircle,
        label: "Profile",
        href: "/home/<USER>/profile",
        color: "text-blue-700"
    },
    {
        icon: MessageSquareText,
        label: "Feedback",
        href: "/home/<USER>/feedback/list",
        color: "text-gray-700"
    },
]

// const supportRoutes = [
//     {
//         icon: Cog,
//         label: "Settings",
//         href: "/settings",
//         color: "text-yellow-500"
//     },
//     {
//         icon: Speech,
//         label: "Support",
//         href: "/support",
//         color: "text-emerald-500"
//     }
// ]

export function SidebarRoutes({ collapsed }: { collapsed: boolean }) {
    const { data: session, status } = useSession();
    const [routes, setRoutes] = useState<JSX.Element[]>([]);

    useEffect(() => {
      let isMounted = true;

      // Only attempt to set routes if the component is still mounted
      // and the session is authenticated with user data.
      if (status === "authenticated" && session?.user && isMounted) {
        const newRoutes = [];

        if (session.user.role === "SUPERADMIN") {
          newRoutes.push(...adminRoutes.map(route => (
            <SidebarItem id={""} key={route.href} {...route} collapsed={collapsed} />
          )));
        }

        if (session.user.userType === "COMPANY") {
          newRoutes.push(...companyRoutes.map(route => (
            <SidebarItem id={""} key={route.href} {...route} collapsed={collapsed} />
          )));
        }

        if (session.user.userType === "JOB_SEEKER") {
          newRoutes.push(...userRoutes.map(route => (
            <SidebarItem id={""} key={route.href} {...route} collapsed={collapsed} />
          )));
        }
        setRoutes(newRoutes);
      } else if (status === "unauthenticated" && isMounted) {
        // If unauthenticated (and not loading), clear routes.
        // The layout should handle redirection.
        setRoutes([]);
      }
      // If status is "loading", routes will remain empty or as previously set
      // until the status resolves.

      return () => {
        isMounted = false;
      };
    }, [session, status, collapsed]);

    if (status === "loading") {
      return <div className="p-4 text-sm text-muted-foreground">Loading navigation...</div>;
    }
  
    return <div className="flex flex-col w-full">{routes}</div>;
  }
