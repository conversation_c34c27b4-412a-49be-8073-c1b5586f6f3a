import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import ArcjetLogo from "@/public/logo1.png";
import InngestLogo from "@/public/logo2.png";

const companies = [
  { id: 0, name: "ArcJet", logo: ArcjetLogo },
  { id: 1, name: "Inngest", logo: InngestLogo },
  { id: 2, name: "ArcJet", logo: ArcjetLogo },
  { id: 3, name: "Inngest", logo: InngestLogo },
  { id: 4, name: "ArcJet", logo: ArcjetLogo },
  { id: 5, name: "<PERSON>ges<PERSON>", logo: InngestLogo },
];

const testimonials = [
  {
    quote:
      "We found our ideal candidate within 48 hours of posting. The quality of applicants was exceptional!",
    author: "Sarah <PERSON>",
    company: "TechCorp",
  },
  {
    quote:
      "The platform made hiring remote talent incredibly simple. Highly recommended!",
    author: "<PERSON>",
    company: "StartupX",
  },
];

const stats = [
  { id: 0, value: "10k+", label: "Monthly active job seekers" },
  { id: 1, value: "48h", label: "Average time to hire" },
  { id: 2, value: "95%", label: "Employer satisfaction rate" },
  { id: 3, value: "500+", label: "Companies hiring remotely" },
];

export function IndustryInfo() {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Trusted by Industry Leaders</CardTitle>

          <CardDescription>
            Join thousands of copmaines hiring top talent
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Company logos */}
          <div className="grid grid-cols-3 gap-4">
            {companies.map((company) => (
              <div key={company.id}>
                <Image
                  src={company.logo}
                  alt={company.name}
                  width={80}
                  height={80}
                  className="rounded-lg opacity-75 transition-opacity hover:opacity-100 cursor-pointer"
                />
              </div>
            ))}
          </div>

          {/* Testimonials */}
          <div className="space-y-4">
            {testimonials.map((testimonial, index) => (
              <blockquote
                key={index}
                className="border-l-2 border-primary pl-4"
              >
                <p className="text-sm text-muted-foreground italic">
                  {testimonial.quote}
                </p>
                <footer className="mt-2 text-sm font-medium">
                  - {testimonial.author}, {testimonial.company}
                </footer>
              </blockquote>
            ))}
          </div>

          {/* We will render stats here */}
          <div className="grid grid-cols-2 gap-4">
            {stats.map((stat) => (
              <div key={stat.id} className="">
                <h4 className="text-2xl font-bold">{stat.value}</h4>
                <p className="text-sm text-muted-foreground">{stat.label}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
}
