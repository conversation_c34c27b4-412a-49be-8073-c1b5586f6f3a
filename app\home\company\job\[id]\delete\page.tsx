"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect } from "react";
import { useParams } from 'next/navigation'
import { useJobActions } from "@/hooks/useJobActions";

export default function JobDeletePage() {
    const params = useParams<{ id: string }>()
    const jobId = params.id;
    const router = useRouter();
    const { job, deleteResult, isLoading, isError, deleteMutation, isDeleting, deleteError } = useJobActions(jobId as string);

    useEffect(() => {
        if (deleteResult && deleteResult.success) {
            toast.success("Job deleted successfully");
            router.push("/home/<USER>/job/list");
        }
    }, [deleteResult, router]);

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (isError || !job) {
        return (
            <div>
                <h1>Job Not Found</h1>
                <p>This job could not be found.</p>
            </div>
        );
    }

    const handleDeleteAction = () => {
        deleteMutation.mutate(jobId);
    };

    return (
        <>
            <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between p-6">
                <h1 className="text-2xl font-bold pt-10">Delete Job Post</h1>
                <p className="text-sm text-muted-foreground pt-2">Are you sure you want to delete job post <b>{job?.jobTitle}</b>?</p>
                <p className="text-sm text-muted-foreground">This job post may have applications and matches with resumes that will also be deleted.</p>
                <p className="text-sm text-muted-foreground pt-2">This action cannot be undone.</p>
                <Button
                    variant="destructive"
                    size="sm"
                    className="mt-4 cursor-pointer"
                    onClick={handleDeleteAction}
                    disabled={isDeleting}
                >
                    {isDeleting ? "Deleting Job Post..." : "Delete Job Post"}
                </Button>
                {deleteError && (
                    <p className="text-red-500 mt-2">Error deleting job. Please try again.</p>
                )}
            </div>
        </>
    );
}
