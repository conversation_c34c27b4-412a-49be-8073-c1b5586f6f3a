import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface SendEmailProps {
    sendTo: string;
    subject: string;
    html: string;
}

export async function SendEmail({ sendTo, subject, html }: SendEmailProps) {
  try {
    const { data, error } = await resend.emails.send({
      from: 'Edison AIX Jobs <<EMAIL>>',
      to: [sendTo],
      subject: subject,
      html: html,
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    return Response.json({ data });
    
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}