"use client";

import { useEffect, useState } from "react";
import { CreateJobFormGeneric } from "@/components/job/CreateJobFormGeneric";
import { Separator } from "@/components/ui/separator";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { UserFile } from "@/types/customTypes";
import { SaveUserFile } from "@/actions/file/saveFile";
import "@/styles/uploadthing.css";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { ErrorMessage } from "@/components/info/ErrorMessage";

export default function PostJobPage() {
  const [postType, setPostType] = useState(2);
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex">
        <h1 className="text-2xl font-bold">Post a Job</h1>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-3/5 border-1 border-dashed p-4">
          <div className="flex items-center justify-center font-bold bg-primary/10 text-primary">
            Enter Job Details
          </div>
          <div className="flex flex-col gap-4 pt-6">
            <CreateJobFormGeneric userRole="USER" />
          </div>
        </div>
        <div className="flex-col w-full md:w-2/5 border-1 border-dashed p-4">
          <div className="flex items-center justify-center font-bold bg-primary/10 text-primary">
            OR just upload your PDF/Word Document
          </div>
          <div className="text-muted-foreground text-start pt-2">
            <div className="flex flex-col space-y-4">
              <p>
                Just drag and drop your job description file here and our AI
                system will take care of the rest.
              </p>
            </div>
          </div>
          <div className="text-sm pt-2">
            <div className="flex flex-col space-y-2 rounded-lg border p-4">
                <label className="flex items-center space-x-2">
                    <input
                    type="radio"
                    name="postType"
                    value={1}
                    onChange={(e) => setPostType(Number(e.target.value))}
                    className="w-4 h-4"
                    />
                    <span>AI processing and post job immediately (default 30 days)</span>
                </label>
                <label className="flex items-center space-x-2">
                    <input
                    type="radio"
                    name="postType"
                    value={2}
                    defaultChecked
                    onChange={(e) => setPostType(Number(e.target.value))}
                    className="w-4 h-4"
                    />
                    <span>AI processing only (job matching and evaluation). I will manually post.</span>
                </label>
            </div>
          </div>
          <div className="flex justify-center pt-4 pb-4">
            <Separator />
          </div>
          <div>
            <div className="flex flex-col space-y-4">
              <UploadDropzone
                endpoint="docUploader"
                className="custom-class h-60 cursor-pointer w-full"
                onClientUploadComplete={(res) => {
                  // Do something with the response
                  toast.success("Job document uploaded successfully.");
                  setSuccess("Job document uploaded successfully.");

                  // Create the file data directly from the response
                  const data = res;
                  if (data && data[0]) {
                    const resumeFile: UserFile = {
                      id: "",
                      userId: "userId",
                      fileUse: "JOB",
                      fileType: data[0].name.split(".")[1].toUpperCase(),
                      fileName: data[0].name,
                      description: "User Job Post",
                      key: data[0].key,
                      url: data[0].ufsUrl,
                      fileSize: data[0].size,
                      postType: postType
                    };

                    SaveUserFile(resumeFile)
                      .then(() => {
                        toast.success("Job document saved successfully.");
                        setSuccess(
                          "Job document is now being processed by AI. Check your Job List in awhile."
                        );
                      })
                      .catch((error) => {
                        toast.error("Failed to save file information");
                        setError("Failed to save file information");
                      });
                  }
                }}
                onUploadError={(error: Error) => {
                  toast.error(
                    `ERROR! A problem was encountered. ${error.message}`
                  );
                  setError(
                    `ERROR! A problem was encountered. ${error.message}`
                  );
                }}
                onUploadBegin={(name) => {
                  // Do something once upload begins
                  toast.info(`Uploading: ${name}`);
                }}
                onChange={(acceptedFiles) => {
                  // Do something with the accepted files
                }}
                content={{
                  allowedContent({ ready, isUploading }) {
                    if (!ready) return "Checking what you allow";
                    if (isUploading) return "Uploading your document...";
                    return `PDF or Word Document (max size of 1MB)`;
                  },
                }}
              />
            </div>
            <div className="flex flex-col space-y-4 pt-10">
              <ErrorMessage message={error} />
              <SuccessMessage message={success} />
            </div>
          </div>          
            <div className="flex flex-col w-full mt-10 p-4 border rounded-lg bg-slate-100 text-center">
                Your uploaded job file(s) will be processed by AI. This may take a little while, so please check back later in the job postings or matches list.
            </div>
        </div>
      </div>
    </div>
  );
}
