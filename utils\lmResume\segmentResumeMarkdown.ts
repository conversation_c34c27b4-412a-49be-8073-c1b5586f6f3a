interface ResumeSegmented {
  segments: Record<string, string>;
  metadata: Record<string, string>;
  unknownHeaders: string[];
}

export function segmentResumeMarkdown(raw: string): ResumeSegmented {
  const knownSections = [
    'Profile',
    'Summary',
    'Skills',
    'Languages',
    'Interests',
    'Education',
    'Experience',
    'Academic Positions Held',
    'Editorial Boards',
    'Academic Honors and Grants',
    'Membership in Academic Organizations',
    'Professional Employment / Consulting Experience',
    'Research Interests',
    'Publications',
    'Articles in Refereed Journals',
  ];

  const lines = raw
    .replace(/\r/g, '')
    .split('\n')
    .map(l => l.trim());

  let current = 'General';
  const segments: Record<string, string> = {};
  const metadata: Record<string, string> = {};
  const unknownHeaders: string[] = [];

  segments[current] = '';

  for (const line of lines) {
    if (!line) continue;

    if (line.startsWith('###')) {
      const header = line.replace(/^#+/, '').trim();

      if (knownSections.includes(header)) {
        current = header;
        segments[current] = segments[current] || '';
        continue;
      }

      if (/tel|fax|email|birth|address|location/i.test(header)) {
        metadata[header.split(':')[0]] = header;
        continue;
      }

      if (/^[A-Z][a-z]+( [A-Z][a-z]+)*$/.test(header)) {
        current = header;
        segments[current] = '';
        unknownHeaders.push(header);
        continue;
      }
    }

    segments[current] += (segments[current] ? '\n' : '') + line;
  }

  // Final cleanup
  Object.keys(segments).forEach(k => {
    segments[k] = segments[k].trim();
  });

  return {
    segments,
    metadata,
    unknownHeaders: [...new Set(unknownHeaders)],
  };
}
