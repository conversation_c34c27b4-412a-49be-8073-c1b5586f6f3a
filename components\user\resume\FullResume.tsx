import LoadingFallback from "@/components/general/LoadingFallback";
import { Education } from "./Education";
import { Header } from "./Header";
import { Skills } from "./Skills";
import { Summary } from "./Summary";
import { WorkExperience } from "./WorkExperience";
import { ResumeData } from "@/lib/resume/resumeActions";
import { AdditionalSections } from "./AdditionalSections";

export const FullResume = ({
  username,
  resumeId,
  resume,
  profilePicture,
}: {
  username: string;
  resumeId?: string;
  resume?: ResumeData | null;
  profilePicture?: string;
}) => {

  if (!resume) {
    return (
      <div className="flex justify-center items-center w-full">
        <LoadingFallback message="Loading Resume..." />
      </div>
    );
  } else if (!resume) {
    return (
        <section
        className="mx-auto w-full max-w-2xl space-y-8 bg-white print:space-y-4 my-8 px-4"
        aria-label="Resume Content"
      >
        <p className="text-center">No resume data available. Upload your resume.</p>
        </section>
    );
  }

  return (
    <section
      className="mx-auto w-full max-w-2xl space-y-8 bg-white print:space-y-4 my-8 px-4"
      aria-label="Resume Content"
    >
      <Header header={resume?.standardFields?.header} picture={profilePicture} resumeId={resumeId} username={username} />

      <div className="flex flex-col gap-6">
        {resume?.standardFields?.summary?.content && (
          <Summary summary={resume.standardFields.summary.content} />
        )}

        {resume?.standardFields?.experience && resume.standardFields.experience.length > 0 && (
          <WorkExperience work={resume.standardFields.experience} />
        )}

        {resume?.standardFields?.education && resume.standardFields.education.length > 0 && (
          <Education educations={resume.standardFields.education} />
        )}

        {resume?.additionalSections && resume.additionalSections.length > 0 && (
            <AdditionalSections sections={resume.additionalSections} />
        )}

        {resume?.standardFields?.skills && resume.standardFields.skills.length > 0 && (
          <Skills skills={resume.standardFields.skills} />
        )}
      </div>
    </section>
  );
};

