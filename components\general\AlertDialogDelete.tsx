import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { XCircleIcon } from "lucide-react";

interface AlertDialogDeleteProps {
  id: string;
  title: string;
  label?: string;
  description?: string;
  onDelete: (id: string) => void;
}

export function AlertDialogDelete({id, title, description, label, onDelete}: AlertDialogDeleteProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <div className="flex w-full justify-center">                             
            <Button
                variant="ghost"
                size="icon"
                className="cursor-pointer"
                title={`Delete ${title}`}
            >
                <XCircleIcon className="w-5 h-5 text-red-700" /> {label}
            </Button>
        </div>   
      </AlertDialogTrigger>
      <AlertDialogContent className="z-[12000]">
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="cursor-pointer">Cancel</AlertDialogCancel>
          <AlertDialogAction       
           className="cursor-pointer"      
            onClick={()=>{
                onDelete(id as string);
            }}
          >
            Continue
        </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
