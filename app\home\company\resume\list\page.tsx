import { EmptyState } from "@/components/general/EmptyState";
import { auth } from "@/lib/auth/auth";
import { GetCompanyResumes } from "@/actions/company/getCompany";
import { CompanyResumeList } from "@/components/company/CompanyResumes";
import { UploadCompanyResume } from "@/components/company/UploadCompanyResume";

export default async function CompanyResumeBankPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>;
}) {
  const session = await auth();
  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50;

  const resumeListResponse = await GetCompanyResumes(
    session?.user?.companyId as string
  );
  const data = resumeListResponse || [];

  // Pagination logic
  const totalPages = Math.ceil(data.length / pageSize);
  const paginatedResumes = data.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <>
      {data?.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
            <div className="flex items-center justify-between gap-2 pb-4">
                <h1 className="text-2xl font-semibold">Resume Bank</h1>
                <UploadCompanyResume companyId={session?.user?.companyId as string} />
            </div>
          <EmptyState
            title="No resumes found"
            description="You don't have resumes in your company bank yet."
            buttonText="Upload Resumes"
            href="#"
          />
          <em>Wait for AI to finish processing your uploaded resumes. All resumes are added to the bank automatically.</em>
        </div>
      ) : (
        <div className="space-y-6">
          <CompanyResumeList
            paginatedResumes={paginatedResumes}
            currentPage={currentPage}
            totalPages={totalPages}
          />
        </div>
      )}
    </>
  );
}
