"use client";

import { <PERSON><PERSON>rapper } from "@/components/info/CardWrapper";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { NewPasswordSchema } from "@/data/zod/zodSchema";
import { useSearchParams } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { NewPassword } from "@/actions/user/newPassword";
import { useState, useTransition } from "react";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export const ResetPasswordForm = () => {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [isPending, startTransition] = useTransition();

  const form = useForm<z.infer<typeof NewPasswordSchema>>({
    resolver: zodResolver(NewPasswordSchema),
    defaultValues: {
      password: "",
    },
  });

  const onSubmit = (values: z.infer<typeof NewPasswordSchema>) => {
    setError("");
    setSuccess("");

    startTransition(() => {
      NewPassword(values, token).then((data) => {
        setError(data?.error);
        setSuccess(data?.success);
      });
    });
  };

  return (
    <CardWrapper
      captionLabel="Reset Password"
      headerLabel="Enter a new password"
      backButtonLabel="Back to sign-in"
      backButtonHref="/auth/signin"
      customClassName="w-[400px] shadow-md border-1"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="********"
                      type="password"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <ErrorMessage message={error} />
          <SuccessMessage message={success} />
          <Button disabled={isPending} type="submit" className="w-full cursor-pointer">
            Reset password
          </Button>
        </form>
      </Form>
    </CardWrapper>
  );
};
