"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { GetUserByHashedEmail } from "@/data/user/user";
import { GetVerificationTokenByToken } from "@/data/site/verificationToken";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const newVerification = async (atoken: string) => {
  
    // Arcjet protection
    await ensureServerActionProtection();

    try {
        const existingToken = await GetVerificationTokenByToken(atoken);
        if (!existingToken?.token) {
        return { error: "Token does not exist or invalid!" };
        }

        const hasExpired = new Date(existingToken.expires) < new Date();
        if (hasExpired) {
        return { error: "Token has expired!" };
        }

        const existingUser = await GetUserByHashedEmail(existingToken.emailHash);
        if (!existingUser) {
        return { error: "Email does not exist!" };
        }

        await prisma.user.update({
        where: { id: existingUser.id },
        data: {
            emailVerified: new Date(),
        },
        });

        //Delete the token
        await prisma.userVerificationToken.delete({
        where: { emailHash: existingToken.emailHash },
        });

        return { success: "Your email has been verified." };
      } catch (error) {
        return { error: `Error verifying email. ${error}`};
      }
};
