export function formatStructuredToMarkdown(
  sections: Record<string, string | string[] | object[]>,
  headingLevel: number = 3
): string {
  const prefix = '#'.repeat(headingLevel);

  return Object.entries(sections)
    .map(([section, content]) => {
      const body = Array.isArray(content)
        ? content
            .map(item => {
              if (typeof item === 'string') return `- ${item}`;
              if (typeof item === 'object' && item !== null) {
                return Object.entries(item)
                  .map(([key, value]) => `**${key}**: ${String(value)}`)
                  .join('\n');
              }
              return '';
            })
            .join('\n\n')
        : content; // string

      return `${prefix} ${section}\n${body}`;
    })
    .join('\n\n');
}
