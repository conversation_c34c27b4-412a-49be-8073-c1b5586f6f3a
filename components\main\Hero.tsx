"use client";

import { useState, useEffect } from 'react';
import styles from "@/styles/style";
import { robot } from "@/public/assets";
import Image from "next/image";
import { Button } from '@/components/ui/button';
import { VideoPlayerDialog } from '@/components/general/VideoPlayerDialog';


const quotes = [
    {
        text: "“Recruiters spend only 6–8 seconds scanning a resume before deciding whether to keep reading.”",
        source: "— Ladders Eye-Tracking Study, 2023"
    },
    {
        text: "“83% of recruiters say they’re more likely to hire candidates whose resumes are tailored to the specific job.”",
        source: "— Jobvite Recruiter Nation Report 2024"
    },
    {
        text: "“On average, each corporate job offer attracts 250 resumes. Of those, only 4 to 6 will get called for an interview, and only one will get the job.”",
        source: "— Glassdoor / Jobvite recruiting benchmarks"
    },
    {
        text: "“Applicants with a professionally written resume are 32% more likely to land the job than those with a DIY resume.”",
        source: "— TopResume survey 2021"
    },
    {
        text: "“Job seekers who apply within the first 24 hours of a posting are 4x more likely to be reviewed.”",
        source: "— Indeed Hiring Lab, 2022"
    },
    {
        text: "“Candidates who follow up after applying increase their chance of a response by 21%.”",
        source: "— Zety Application Follow-up Survey, 2023"
    }
];

const Hero = () => {
    const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
    const [isVideoDialogOpen, setIsVideoDialogOpen] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setCurrentQuoteIndex((prevIndex) => (prevIndex + 1) % quotes.length);
        }, 5000); // Change quote every 5 seconds

        return () => clearTimeout(timer); // Cleanup timer on component unmount
    }, [currentQuoteIndex]);

    return (
        <section id="Hero" className="flex w-full">
            <div className="w-full flex-1 flex flex-col md:flex-row justify-between items-center">
                <div className="w-full md:w-[60%] flex flex-col md:flex-row ml-10 md:ml-20">
                    {/* Text section */}
                    <div className="flex-1 flex items-start w-full md:w-[60%] flex-col xl:px-0 sm:px-16 px-6 md:pr-10">
                    <div className="flex flex-row items-start py-[6px] px-4 bg-discount-gradient rounded-[10px] mb-2">
                        <Image src="/assets/signup.png" alt="discount" className="w-[28px] h-[28px]" width={28} height={28} />
                        <p className={`${styles.paragraph} ml-2`}>
                            <a href="/auth/signup"><span className="text-white">It's FREE. Sign up NOW!</span></a> 
                        </p>
                    </div>
                        <div className="flex flex-col justify-between items-start w-full ">
                            <h1 className="flex-1 font-poppins font-semibold ss:text-[62px] text-[52px] text-white ss:leading-[100px] leading-[70px]">
                                The Next {" "}
                                <span className="text-gradient">Generation</span>{" "} <br /> AI-powered Job and<br /> Talent matching.
                            </h1>
                            <p className={`${styles.paragraph} mt-5 text-muted-foreground`}>
                                Smarter Matches. Faster Hires.
                            </p>
                        </div>
                    </div>
                    {/* Quotes section */}
                    <div className="w-full md:w-[40%] flex flex-col justify-start text-muted-foreground px-6 md:px-0 md:pl-10 mt-8 md:mt-0 pb-4 min-h-[250px] md:min-h-[300px] relative"> {/* Added min-height and relative positioning */}
                        <div className="flex pt-10 justify-center">                            
                            <p className="text-md text-[#5ce1e6] mb-4">Job Seekers Have Six Seconds To Succeed</p>
                        </div>
                        {/* Quotes Container: Made relative, given height, and w-full. This div now takes space in the flex column. */}
                        <div className="relative w-full h-[140px] md:h-[160px]">
                            {quotes.map((quote, index) => (
                                // Individual Quote: Absolutely positioned within the new relative parent above.
                                // `inset-0` makes it fill the parent. Flex properties center the text.
                                <div
                                    key={index}
                                    className={`transition-opacity duration-400 ease-in-out absolute inset-0 flex flex-col items-center justify-center text-center p-2 ${index === currentQuoteIndex ? 'opacity-100' : 'opacity-0'}`}
                                >
                                    <p><em>{quote.text}</em></p>
                                    <p className="text-xs mt-1">{quote.source}</p>
                                </div>
                            ))}
                        </div>
                        {/* Button Container: Added margin-top for spacing and self-start for alignment. */}
                        <div className="mt-6 self-center justify-center">
                           <Button 
                            variant='ghost'
                            className='cursor-pointer'
                            onClick={() => setIsVideoDialogOpen(true)}
                            title="Watch Athena Video"
                           >
                                <Image
                                    src="/assets/watchvideo.png"
                                    alt="watch video"
                                    className="w-[250px] h-[90px]"
                                    width={250}
                                    height={90}
                                />
                           </Button>
                        </div>
                    </div>
                </div>
                {/* Robot section */}
                <div className="hidden w-full md:w-[40%] flex-1 md:flex md:my-0 sticky my-10 top-0 right-0 justify-end">
                    <Image
                        src={robot}
                        alt="robot"
                        className="w-[500px] h-[505px] relative z-[5]"
                    />
                    <div className="absolute z-[0] w-[40%] h-[35%] top-0 pink__gradient" />
                    <div className="absolute z-[1] w-[80%] h-[80%] rounded-full bottom-40 white__gradient" />
                    <div className="absolute z-[0] w-[50%] h-[50%] right-20 bottom-20 blue__gradient" />
                </div>
            </div>
            {/* Render the VideoPlayerDialog component */}
            <VideoPlayerDialog
                isOpen={isVideoDialogOpen}
                onClose={() => setIsVideoDialogOpen(false)}
                videoId="8RoFxffPLpk"
            />
        </section>
    );
};

export default Hero;
