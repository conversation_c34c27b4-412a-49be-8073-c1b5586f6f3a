"use server";

import { GoogleGenerativeAI, GenerateContentResponse } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function ExtractJobData(userId: string, jobId: string, jobText: string) {

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro-latest"});

    const systemInstructionText = `You are a meticulous and infallible job posting parser. Your primary directive is to extract **every single piece of text** from the provided job posting and structure it into a valid JSON object. You must not omit, summarize, or alter any part of the original text.

**CRITICAL RULES - FOLLOW THESE STRICTLY:**
1.  **NO OMISSIONS:** Every word, sentence, and paragraph from the input 'jobText' MUST be present in the final JSON output. There are no exceptions. If you leave out any text, you have failed.
2.  **EXACT REPLICATION:** Preserve the original text exactly as it appears. Do not rephrase, summarize, or interpret the content.
3.  **JSON VALIDITY:** The output MUST be a single, complete, and valid JSON object. Do not add any text, explanations, or markdown before or after the JSON object.
4.  **CATCH-ALL FOR UNSTRUCTURED TEXT:** Any text that does not belong to a clearly titled section (like introductory paragraphs or floating sentences) MUST be placed in the 'jobDescription' object under a key named "Description".`;

    const userPromptParts = [
        { text: `**Input:**

The job posting text to parse is enclosed in triple angle brackets:

<<<
${jobText}
>>>` },
        { text: `---
**Output Instructions:**

1.  **Basic Information:** Extract **exactly** the following keys. If a value is not present, use null.
    - jobTitle: (Required) The full job title (e.g., "Barista - Catering Assistant", "Product Supply Manager").
    - company: The full organization name. If not specified, use null.
    - country: The 2-letter ISO country code (e.g., "US", "CA", "UK"). Infer from location if necessary. If not possible, use null. If not specified, use null.
    - location: The full work location, address, city, state, etc. If not specified, use null.
    - department: The department or industry (e.g., "Marketing", "Finance", "Transport & Logistics"). If not specified, use null.
    - employmentType: Must be one of: "FULL_TIME", "PART_TIME", "CONTRACT", "INTERNSHIP", "TEMPORARY", "POOLING". If not specified, use null.
    - experienceLevel: The required experience level (e.g., "1-2 years", "Entry Level"). If not specified, use null.
    - interviewType: Must be one of: "ONSITE", "ONLINE", "HYBRID". If not specified, use null.
    - localRemoteWork: true if remote work is offered within the country, otherwise false.
    - overseasRemoteWork: true if remote work is offered outside the country, otherwise false. If not specified, use null.
    - salaryCurrency: The 3-letter ISO 4217 currency code (e.g., "USD"). If not specified, use null.
    - salaryFrom: The minimum salary, as a number. If not specified, use null.
    - salaryTo: The maximum salary, as a number. If not specified, use null.` },
        { text: `2.  **Job Description Sections (jobDescription):** Extract ALL remaining data from the job posting into this object.
    *   **Content Structure:** The value for each section key must be a list of strings.
        *   **For Paragraphs:** If the content is a continuous block of text (a paragraph), it MUST be a single string element in the list. Do NOT split a single paragraph into multiple sentences or smaller fragments.
            *   *Correct Example:* ["This is a complete paragraph with multiple sentences. It should not be broken up into separate sentences."]
            *   *Incorrect Example:* ["This is a complete paragraph with multiple sentences.", "It should not be broken up into separate sentences."]
        *   **For List Items:** If the content is a clear list (e.g., bullet points, numbered items), each individual bullet point or numbered item should be a separate string in the list. If a list item itself contains multiple sentences forming a paragraph, that entire list item (paragraph) should still be a single string.
   *   **Section Identification:** Identify sections by clear headings. A section title is typically a short phrase on its own line, often ending with a colon (e.g., "Responsibilities:", "Qualifications:") or written in ALL CAPS (e.g., "ABOUT US"). If a line appears to be a heading and is followed by a list (bulleted or numbered), treat that line as the section title.
    *   **Key Naming:** The JSON key for a section MUST be the exact, verbatim section title as it appears in the text.
    *   **Content Affinity:** DO NOT place content that belongs to a specific section into another section. For example, if "Occasionally checking dogs in at the front desk." belongs to "Job Responsibilities", it MUST be placed under "Job Responsibilities" and NOT under "Description".
    *   **Catch-All ("Description"):** This key is a LAST RESORT for truly introductory paragraphs or floating sentences that absolutely do not logically belong under any specific, identified section heading. Prioritize identifying and naming specific sections over using "Description".

* **Validity:** The output MUST be a complete and valid JSON object. Ensure all brackets and braces are correctly opened and closed. Use double quotes for all JSON keys and string values.` },
        { text: `**Example Output Structure:**

    \`\`\`json
    {
        "jobTitle": "Barista - Catering Assistant",
        "company": "Foodie Company Inc",
        "country": "US",
        "location": "123 Main St, Los Angeles, CA, United States",
        "department": "Catering",
        "employmentType": "FULL_TIME",
        "experienceLevel": "1-2 years",
        "interviewType": "ONSITE",
        "localRemoteWork": false,
        "overseasRemoteWork": false,
        "salaryCurrency": "USD",
        "salaryFrom": 100000,
        "salaryTo": 200000,
        "jobDescription": {
            "Description": [
                "This is an introductory paragraph about the role and company that doesn't have a specific heading."
            ],
            "Job Qualifications:": [
              "Recent Graduate in one of the following engineering majors: Chemical Engineering, Mechanical Engineering, Manufacturing Engineering, Electrical Engineering, or any relevant engineering degree with 0 - 3 years of relevant experience",
              "Background in Manufacturing and Production Systems is an advantage."
            ],
            "Responsibilities of the role:": [
              "As a Product Supply Manager, you will have the opportunity to discover P&G's technologies (Making and Packing) & standards which are worldly recognized.",
              "We will help you build your capabilities through the job experience, mentoring and training."
             ]
        }
    }
    \`\`\`` },
        { text: `**Self-Correction and Final Review:**

Before providing your final response, perform this mental check:
1.  Read through the original \`job_text\` one last time.
2.  Read through your generated JSON.
3.  Is every single word from the original text present in your JSON?
4.  If not, go back and add the missing text to the correct field. The most common mistake is forgetting text in the \`"Description"\` or another section in \`jobDescription\`.
5.  Remove duplicated text, item, section, or content.
6.  Finally, verify the JSON syntax is perfect (quotes, commas, brackets).

**Return ONLY the valid JSON object.**` }
    ];

    const request = {
        systemInstruction: {
            role: "system",
            parts: [{ text: systemInstructionText }]
        },
        contents: [{
            role: "user",
            parts: userPromptParts
        }]
    };

    const result = await model.generateContent(request);
    console.log({GEMINIExtractJob: result});

    if (!result.response) { // It's good to check if the response itself is valid
        throw new Error("Failed to get a response from the Gemini API.");
    }

    return result.response;

    // const responseText = result.text(); // Call .text() directly on the result object
    // console.log({GEMINIExtractJob: responseText});

    // try {
    //     const jsonMatch = responseText.match(/```(?:json)?([\s\S]*?)```/);
    //     const jsonString = jsonMatch ? jsonMatch[1].trim() : responseText;
    //     return JSON.parse(jsonString);
    // } catch (e) {
    //     console.error("Error parsing JSON from Gemini response:", e, "Raw text:", responseText);
    //     throw new Error("Failed to parse job data from AI response. The response was not valid JSON.");
    // }
}