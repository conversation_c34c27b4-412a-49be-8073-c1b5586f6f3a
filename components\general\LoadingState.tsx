import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  size?: number;
  text?: string;
  className?: string;
  minHeight?: string;
}

export function LoadingState({
  size = 8,
  text = "Loading...",
  className,
  minHeight = "400px",
}: LoadingStateProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center w-full gap-2",
        className
      )}
      style={{ minHeight }}
    >
      <Loader2 
        className={cn(
          "animate-spin text-muted-foreground",
          `size-${size}`
        )} 
      />
      {text && (
        <p className="text-sm text-muted-foreground">{text}</p>
      )}
    </div>
  );
}