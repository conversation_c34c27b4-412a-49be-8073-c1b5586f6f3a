import { Section } from "@/components/ui/section";
import { ResumeDataSchemaType } from "@/data/zod/resumeZod";
import { JsonToHTML } from "@/components/general/JsonToHTML";
import { normalizeHeaders } from "@/utils/stringHelpters";

/**
 * Displays the education period in a consistent format
 */
function EducationPeriod({ start, end }: { start: string; end: string }) {
  return (
    <div
      className="text-sm tabular-nums text-gray-500"
      aria-label={`Period: ${start}${end ? ` to ${end}` : ''}`}
    >
      {start}{end ? ` - ${end}` : ''}
    </div>
  );
}

/**
 * Individual education card component
 */
function EducationItem({
  education,
}: {
  education: ResumeDataSchemaType["standardFields"]["education"][0];
}) {
  const { institution, startDate, endDate, degree, details, gpa, honors } = education;

  let detailsContent: React.ReactNode = null;
  if (details && typeof details === 'string' && details.trim() !== "") {
    try {
      const parsedDetails = JSON.parse(details); // `details` is now always a Tiptap JSON string
      detailsContent = <JsonToHTML json={parsedDetails} />;
    } catch (e) {
      console.warn("Education details could not be parsed as JSON, rendering as plain text:", details, e);
      detailsContent = <div className="text-sm text-gray-600 whitespace-pre-wrap">{details}</div>; // Fallback
    }
  }

  return (
    <Section>
      <div className="space-y-1.5"> 
        <div className="flex justify-between items-baseline text-base">
            <h3 className="font-semibold leading-none">
                {normalizeHeaders(institution || '')}
            </h3>
            <EducationPeriod start={startDate as string} end={endDate as string} />
        </div>
        <div className="text-sm font-medium text-left text-[#6c737f]">
            {normalizeHeaders(degree || '')}
        </div>
        {detailsContent && (
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {detailsContent}
          </div>
        )}
        {gpa && <p className="text-xs text-gray-500">GPA: {gpa}</p>}
        {honors && honors.length > 0 && (
          <div>
            <p className="text-xs font-semibold text-gray-600">Honors:</p>
            <ul className="list-disc list-inside text-xs text-gray-500">
              {honors.map((honor, idx) => <li key={idx}>{honor}</li>)}
            </ul>
          </div>
        )}
      </div>
    </Section>
  );
}

/**
 * Main education section component
 * Renders a list of education experiences
 */
export function Education({
  educations,
}: {
  educations: ResumeDataSchemaType["standardFields"]["education"];
}) {
  return (
    <Section>
      <h2 className="text-xl font-bold" id="education-section">
        Education
      </h2>
      <div
        className="space-y-4"
        role="feed"
        aria-labelledby="education-section"
      >
        {educations.map((item, idx) => (
          <article key={idx} role="article">
            <EducationItem education={item} />
          </article>
        ))}
      </div>
    </Section>
  );
}
