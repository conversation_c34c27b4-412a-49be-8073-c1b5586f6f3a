import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { ResumeEvaluation } from "@/lib/resume/resumeActions";
import { GetUserResumeEvaluation } from "@/actions/resume/userResumeActions";

// Fetch resume evaluation data
const fetchResumeEvaluation = async (
  userId: string
): Promise<{
  evaluation: ResumeEvaluation | undefined;
}> => {
  try {
    const data = await GetUserResumeEvaluation(userId);
    return { evaluation: data };
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

export function useResumeEvaluation() {
  const queryClient = useQueryClient();
  const { data: session } = useSession();
  const userId = session?.user?.id as string;

  // Fetch user's resume evaluation
  const evaluationQuery = useQuery({
    queryKey: ["resumeEvaluation", userId],
    queryFn: () => {
      if (!userId) return null;
      return fetchResumeEvaluation(userId);
    },
    enabled: !!userId,
    staleTime: 30000,
  });

  return {
    evaluationQuery,
    isLoading: evaluationQuery.isLoading,
    isError: evaluationQuery.isError,
    evaluation: evaluationQuery.data?.evaluation,
  };
}
