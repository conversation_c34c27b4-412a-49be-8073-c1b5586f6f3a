"use server";

import * as z from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { auth } from "@/lib/auth/auth";;
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { JobNoteSchema } from "@/data/zod/zodSchema";
import { AddJobNoteData, DeleteJobNoteData } from "@/data/job/job";

export async function AddJobNote(values: z.infer<typeof JobNoteSchema>) {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

    try {

        values.userId = session.user.id as string;
        values.companyId = session.user.companyId as string;

        const newNote = await AddJobNoteData(values);    

        if (newNote) {
            return { 
                data: newNote,
                success: "Successfully added note." 
            };
        } else {
            return { 
                data: null,
                error: "Failed to add note." 
            };
        }
        
    } catch (error) {
            return { 
                data: null,
                error: `Error! Failed to add note: ${error}` 
            };   
    }
};

export async function DeleteJobNote(id: string) {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

    try {

        const note = await DeleteJobNoteData(id);    

        if (note) {
            return { 
                data: note,
                success: "Successfully deleted note." 
            };
        } else {
            return { 
                data: null,
                error: "Failed to delete note." 
            };
        }
        
    } catch (error) {
            return { 
                data: null,
                error: `Error! Failed to delete note: ${error}` 
            };   
    }
}
