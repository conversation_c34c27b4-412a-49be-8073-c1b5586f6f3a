import { getJobApplicants, getCompanyJobApplications } from "@/actions/job/getJob";
import { useQuery } from "@tanstack/react-query";

const fetchJobApplications = async (
  userId: string,
  companyId: string,
): Promise<any[]> => {
  try {
    const data = await getCompanyJobApplications(userId, companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("JobApplications fetch error:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch JobApplications"
    );
  }
};

const fetchJobApplicants = async (
    jobId: string,
  ): Promise<any[]> => {
    try {
      const data = await getJobApplicants(jobId);
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error("JobApplications fetch error:", error);
      throw new Error(
        error instanceof Error ? error.message : "Failed to fetch JobApplications"
      );
    }
  };

export function useUserJobApplications(userId?: string, companyId?: string, jobId?: string) {

  const userApplicationsQuery = useQuery({
    queryKey: ["userApplications", userId, companyId],
    queryFn: () => {
      if (!userId) return null;
      return fetchJobApplications(userId, companyId as string);
    },
    enabled: !!userId && !!companyId, 
    staleTime: 30000,
  });

  const jobApplicantsQuery = useQuery({
    queryKey: ["jobApplications", jobId as string],
    queryFn: () => {
     if (!jobId) return null;
      return fetchJobApplicants(jobId as string);
    },
    enabled: !!jobId, 
    staleTime: 30000,
  });


  return {
    userApplicationsQuery,
    jobApplicantsQuery 
  };
}
