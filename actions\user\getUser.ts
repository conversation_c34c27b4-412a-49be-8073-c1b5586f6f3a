"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { UserRole, UserType } from "@prisma/client";
import {
    GetAdminUsersData,
  GetUser,
  getUserIdByUsernameData,
  GetUserJobSeekerData,
  GetUserLocationData,
  getUserPlanData,
} from "@/data/user/user";
import { auth } from "@/lib/auth/auth";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const GetUserById = async (userId: string) => {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  if (!userId) {
    return { error: "Missing user id." };
  }

  try {
    const userProfile = await GetUser(userId);

    if (userProfile) {
      return {
        data: userProfile,
        success: "Success! Your information was retrieved.",
      };
    } else {
      return {
        data: null,
        error: "Error! There was an error retrieving your information.",
      };
    }
  } catch (error) {
    return { error: `Error fetching user. ${error}`, data: null };
  }
};

export const GetUserNameById = async (userId: string) => {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  if (!userId) {
    return { error: "Missing user id." };
  }

  try {
    const user = await prisma.user.findFirst({
      where: {
        id: userId,
      },
      select: {
        firstName: true,
        lastName: true,
      },
    });

    if (user) {
      return {
        firstName: safeDecrypt(user.firstName),
        lastName: safeDecrypt(user.lastName),
      };
    }

    return { error: "User not found" };
  } catch (error) {
    return { error: "Error encountered fetching user." };
  }
};

export const GetUserJobSeekerProfile = async (userId: string) => {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  if (!userId) {
    return { error: "Missing user id." };
  }

  try {
    const userProfile = await GetUserJobSeekerData(userId);

    if (userProfile) {
      return {
        success: "Success! User information was retrieved.",
        jobSeeker: userProfile.jobSeeker,
      };
    } else {
      return {
        error: "Error! Failed to retrieve user information.",
        jobSeeker: null,
      };
    }
  } catch (error) {
    return {
      error: `Error! Failed to retrieve user information: ${error}`,
      jobSeeker: null,
    };
  }
};

export const UpdateUserCompany = async (companyId: string) => {
      
    const session = await auth();

    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  if (!companyId) {
    return { error: "Missing company id." };
  }

  try {
    const user = await prisma.user.update({
      where: {
        id: session?.user?.id as string,
      },
      data: {
        onboarded: true,
        companyId: companyId,
        role: UserRole.USER,
        userType: UserType.COMPANY,
      },
    });

    if (user) {
      return {
        success: "Success! Company information was saved.",
      };
    }
  } catch (error) {
    return { error: `Error was encountered, please try again. ${error}` };
  }
};

export async function checkIfUserHasOnboarded(userId: string) {
      
    const session = await auth();

    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const user = await prisma.user.findUnique({
            where: {
            id: userId,
            },
            select: {
            onboarded: true,
            },
        });

        if (user?.onboarded) {
            return user;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
}

export const GetUserIdByUsername = async (username: string) => {
      
    const session = await auth();

    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const userId = await getUserIdByUsernameData(username);

         if (userId) {
            return userId;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const GetUserLocation = async () => {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const user = await GetUserLocationData(session?.user?.id as string);

    if (user) {

        let location = "";

        if (user.jobSeeker) {
            location = user.jobSeeker.countryCode ? user.jobSeeker.countryCode : "";
        } else if (user.company) {
            location = user.company.location ? user.company.location : "";
        }

      return location;
    }

    return null;
  } catch (error) {
    return { error: "Error encountered fetching user." };
  }
};

export async function GetUserPlan(userId: string, companyId?: string) {
      
    const session = await auth();

    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const plan = await getUserPlanData(userId, companyId)

        if (plan) {
            return plan;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const GetAdminUsers = async (page?: number, pageSize?: number) => {
    const session = await auth();
      
    if (!session?.user?.id) {
        throw new Error("You are not authenticated.");
    }

    // Optional: Add role check to ensure only authorized users can access this
    if (session?.user?.role !== UserRole.SUPERADMIN) {
        throw new Error("You are not authorized.");
    }

    // Arcjet protection
    await ensureServerActionProtection();


  try {
    const result = await GetAdminUsersData(page, pageSize);

    if (result) {
      return {
        data: result.data,
        totalItems: result.totalItems,
        success: "Success! Your information was retrieved.",
      };
    } else {
      return {
        data: null,
        totalItems: 0,
        error: "Error! There was an error retrieving your information.",
      };
    }
  } catch (error) {
    return { error: `Error fetching user. ${error}`, data: null };
  }
};
