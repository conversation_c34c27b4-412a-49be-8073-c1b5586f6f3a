import { NextResponse, NextRequest } from 'next/server';

interface GeoServiceResult {
  country: string | null;
  currency: string | null;
  languages: string | null; // Comma-separated string or null
  serviceName: string;
  diagnostic_message: string;
  error?: boolean;
  status?: number;
}

async function tryGeoService(
  url: string,
  serviceName: string,
  ipForService?: string // For logging or if service needs it in URL
): Promise<GeoServiceResult> {
  let diagnostic_message = `Attempting ${serviceName}`;
  if (ipForService) {
    diagnostic_message += ` for IP ${ipForService}`;
  }
  console.log(`API/GEO: ${diagnostic_message}`);

  try {
    const res = await fetch(url, {
      headers: { 'User-Agent': 'EdisonJobsGeoLookup/1.0' } // Good practice to set a User-Agent
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.warn(`API/GEO: ${serviceName} error: ${res.status} ${res.statusText}. Response: ${errorText.substring(0, 200)}`);
      return {
        country: null, currency: null, languages: null, serviceName,
        diagnostic_message: `${serviceName} failed with status ${res.status}.`,
        error: true, status: res.status
      };
    }

    const data = await res.json();
    let country: string | null = null;
    let currency: string | null = null;
    let languages: string | null = null;

    switch (serviceName) {
      case 'ipapi.co':
        country = data.country_code || data.country || null;
        currency = data.currency || null;
        languages = data.languages || null; // e.g., "tl,en,fil"
        break;
      case 'ipregistry.co':
        country = data.location?.country?.code || null;
        currency = data.currency?.code || null;
        // languages is an array of objects, e.g. [{code:"en",name:"English"},{code:"tl",name:"Tagalog"}]
        // We'll take the codes and join them.
        if (Array.isArray(data.location?.country?.languages)) {
          languages = data.location.country.languages.map((lang: { code: string }) => lang.code).join(',') || null;
        }
        break;
      case 'ipwho.is':
        country = data.country_code || null;
        currency = data.currency_code || null;
        // ipwho.is does not seem to provide a direct languages list in the same format.
        // If it's critical, you might need to infer or map, otherwise set to null.
        languages = null;
        break;
      case 'geoip.sh':
        country = data.country?.iso_code || data.registered_country?.iso_code || null;
        currency = null; // geoip.sh doesn't provide currency
        languages = null; // geoip.sh doesn't provide languages
        break;
    }

    if (country) {
      const successMsg = `Successfully fetched from ${serviceName}${ipForService ? ` for IP ${ipForService}` : ''}. Country: ${country}.`;
      console.log(`API/GEO: ${successMsg}`);
      return {
        country: country.toUpperCase(), currency, languages, serviceName,
        diagnostic_message: successMsg
      };
    } else {
      const noCountryMsg = `${serviceName} did not return a country code.`;
      console.log(`API/GEO: ${noCountryMsg} Data: ${JSON.stringify(data).substring(0,200)}`);
      return {
        country: null, currency: null, languages: null, serviceName,
        diagnostic_message: noCountryMsg,
        error: true
      };
    }
  } catch (error) {
    console.error(`API/GEO: Exception during ${serviceName} lookup:`, error);
    return {
      country: null, currency: null, languages: null, serviceName,
      diagnostic_message: `Exception during ${serviceName} lookup.`,
      error: true, status: 500
    };
  }
}

export async function GET(request: NextRequest) {
  const servicesToTry = [
    { name: 'ipapi.co', url: 'https://ipapi.co/json/', needsIp: false },
    { name: 'ipregistry.co', url: 'https://ipregistry.co/?key=tryout', needsIp: false }, // Note: 'tryout' key for testing
    { name: 'ipwho.is', url: 'https://ipwho.is/', needsIp: false },
    { name: 'geoip.sh', urlBase: 'https://geoip.sh/json/', needsIp: true },
  ];

  for (const service of servicesToTry) {
    let serviceUrl = service.url;
    let clientIpForService: string | undefined = undefined;

    if (service.needsIp) {
      const headersList: Headers = request.headers;
      const ip = (
        headersList.get('cf-connecting-ip') ||
        headersList.get('x-forwarded-for')?.split(',')[0].trim() ||
        headersList.get('x-real-ip')
      );

      if (ip && ip !== "127.0.0.1" && ip !== "::1" && !ip.startsWith("10.") && !ip.startsWith("172.16.") && !ip.startsWith("192.168.")) {
        serviceUrl = `${service.urlBase}${ip}`;
        clientIpForService = ip;
      } else {
        console.log(`API/GEO: Skipping ${service.name} as it requires a client IP, and a valid one was not found (detected: '${ip}').`);
        continue; 
      }
    }

    const result = await tryGeoService(serviceUrl!, service.name, clientIpForService);
    if (result.country && !result.error) {
      return NextResponse.json({
        country: result.country,
        currency: result.currency,
        languages: result.languages,
        diagnostic_message: result.diagnostic_message,
        service_used: result.serviceName,
      });
    }
  }

  // If all services fail
  console.warn("API/GEO: All GeoIP services failed to determine location.");
  return NextResponse.json(
    {
      country: null,
      currency: null,
      languages: null,
      error: "Failed to determine location from all GeoIP services.",
      diagnostic_message: "All GeoIP services failed.",
      service_used: null,
    },
    { status: 503 } // Service Unavailable
  );
}