"use client";

import { useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import {
  GlobeIcon,
  PhoneIcon,
  MailIcon,
  MapPinIcon,
  PenBox,
} from "lucide-react";
import { useEffect, useState } from "react"; // Ensured useState and useEffect are imported
import { FaLinkedin, FaTwitter } from "react-icons/fa";
import { GetCompany } from "@/actions/company/getCompany";
import { UploadLogo } from "./UploadLogo";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { benefits } from "../job/ListOfBenefits";
import { JsonToHTML } from "../general/JsonToHTML";
import { Separator } from "../ui/separator";
import { CompanyWorkspace } from "./CompanyWorkspace";
import { useCompanyActions } from "@/hooks/useCompanyActions";

export function CompanyAccount() {
  const { data: session } = useSession();
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Call the hook unconditionally
  const { company, isLoading: queryIsLoading, isError } = useCompanyActions(session?.user?.companyId as string, session?.user?.id);

  // Determine effective loading state: true if not mounted or query is loading
  const isLoadingEffectively = !hasMounted || queryIsLoading;

  if (isLoadingEffectively) {
    return <div>Loading account...</div>;
  }
  
  if (isError) {
    return <div>Error loading account.</div>;
  }

  let descriptionContent: React.ReactNode = null;
  if (company?.description && typeof company.description === 'string' && company.description.trim() !== "") {
    try {
      const parsedDescription = JSON.parse(company.description);
      descriptionContent = <JsonToHTML json={parsedDescription} />;
    } catch (e) {
      // Fallback for plain text or if JSON parsing fails
      console.warn("Company description could not be parsed as JSON, rendering as plain text:", company.description, e);
      // Render plain text, respecting newlines
      descriptionContent = <div className="whitespace-pre-wrap">{company.description}</div>;
    }
  }

  return (
    <Card className="p-6">
        <em className="text-sm text-muted-foreground">These are shown on your job posts. Make sure they are updated!</em>
        <div className="grid grid-cols-1 sm:grid-cols-[auto_1fr] gap-8">
            {/* Left Column - Profile Image */}
            <div className="w-48 flex-shrink-0 justify-self-center sm:justify-self-start">
                    <Avatar className="w-48 h-48 border-2">
                        <AvatarImage
                        src={company?.logo || ""}
                        alt={`${company?.name}`}
                        />
                        <AvatarFallback className="text-4xl">
                        {company?.name}
                        </AvatarFallback>
                    </Avatar>
                <div className="flex w-full items-center justify-center text-sm">
                    <UploadLogo companyId={company?.id}/>
                </div>
            </div>

            {/* Right Column - Profile Details */}
            <div className="space-y-6">
            {/* Basic Info */}
            <div className="flex justify-between items-start w-full">
                <div>
                <h1 className="text-2xl font-bold">
                    {company?.name}
                </h1>
                <p className="text-md text-muted-foreground">
                    {company?.location}
                </p>
                </div>

                <div className="flex gap-2">                
                    <Button
                        variant="default"
                        className="cursor-pointer"
                        onClick={() => {
                            window.location.href = `/home/<USER>/account/${company?.id}/update`;
                        }}
                    >
                        <PenBox className="h-4 w-4" /> Update Info
                    </Button>
                </div>
            </div>

            {/* Grid Layout */}
            <div className="grid grid-cols-1 md:grid-cols-[180px_180px_1fr] gap-x-6 gap-y-4 items-start text-sm">
                {/* Contact Info */}
                <div className="space-y-2">
                <div className="flex items-center gap-2 text-primary">
                    <MailIcon className="w-4 h-4" />
                    <span>{company?.email}</span>
                </div>
                <div className="flex items-center gap-2 text-primary">
                    <PhoneIcon className="w-4 h-4" />
                    <span>{company?.phone}</span>
                </div>
                <div className="flex items-center gap-2 text-primary">
                    <MapPinIcon className="w-4 h-4" />
                    <span>{company?.location}</span>
                </div>
                </div>

                {/* Links */}
                <div className="space-y-2">
                <a
                    href={company?.linkedIn || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                >
                    <FaLinkedin className="w-4 h-4" />
                    <span>LinkedIn</span>
                </a>
                <a
                    href={"https://x.com/" + company?.xAccount || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                >
                    <FaTwitter className="w-4 h-4" />
                    <span>X Account</span>
                </a>
                <a
                    href={company?.website || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-primary hover:underline"
                >
                    <GlobeIcon className="w-4 h-4" />
                    <span>Website</span>
                </a>
                </div>

                {/* About Section */}
                <div className="md:row-span-2 bg-muted/20 rounded-lg max-h-30 overflow-y-auto overflow-x-hidden scroll-smooth scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent hover:scrollbar-thumb-muted-foreground/50 border">
                <h2 className="text-md font-semibold mb-2 pt-2 pl-2">
                    About:
                </h2>
                <p className="text-muted-foreground whitespace-pre-wrap leading-relaxed text-sm p-2">
                    {company?.about}
                </p>
                </div>
            </div>                          
            </div>
        </div>
        <Separator />
        <div className="grid grid-cols-2 w-full gap-10">
            {/* Description Section */}
                <div>
                    <h2 className="text-md font-semibold mb-2">Description:</h2>
                    <div className="bg-muted/20 rounded-lg max-h-60 overflow-y-auto overflow-x-hidden scroll-smooth scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent hover:scrollbar-thumb-muted-foreground/50 border p-6">
                        {descriptionContent}
                    </div>
                </div>

                {/* Benefits Section */}
                <div>
                    <h2 className="text-md font-semibold mb-2">Benefits:</h2>
                    <div className="flex flex-wrap gap-1 pb-4">
                        {company?.benefits?.map((benefitId: string) => {
                            const benefit = benefits.find((b) => b.id === benefitId);
                            return benefit ? (
                                <Badge key={benefit.id}>{benefit.icon}{benefit.label}</Badge>
                            ) : null;
                        })}
                    </div>
                    <h2 className="text-md font-semibold mb-2">Tags:</h2>
                    <div className="flex flex-wrap gap-1">
                    </div>
                </div>  
        </div>
        <Separator />
        <em className="text-sm text-muted-foreground">Private information. Only visible to you and your team.</em>
        <div className="flex flex-col w-full">
            <CompanyWorkspace userId={session?.user?.id as string} companyId={session?.user?.companyId as string} />
        </div>
        <Separator />
        <div className="flex flex-col w-full">
            <div className="flex w-full">
               
            </div>
        </div>
    </Card>
  );
}
