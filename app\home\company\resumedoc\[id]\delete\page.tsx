"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useGetResume } from "@/hooks/useGetResume";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect } from "react";
import { useParams } from 'next/navigation'

export default function CandidateResumeDeletePage() {
    const params = useParams<{ id: string }>()
    const id = params.id;
    const router = useRouter();
    const { resumeQuery, isLoading, isError, resume, deleteMutation, isDeleting, deleteError, deleteResult } = useGetResume(id);

    useEffect(() => {
        if (deleteResult && deleteResult.success) {
            toast.success("Resume deleted successfully");
            router.push("/home/<USER>/resume/list");
        }
    }, [deleteResult, router]);

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (isError || !resume) {
        return (
            <div>
                <h1>Resume Not Found</h1>
                <p>This resume could not be found.</p>
            </div>
        );
    }

    const handleDeleteAction = () => {
        deleteMutation.mutate(id);
    };

    return (
        <>
            <div className="flex flex-col max-w-4xl mx-auto mt-4 w-full md:rounded-lg border-[0.5px] border-neutral-300 items-center justify-between p-6">
                <h1 className="text-2xl font-bold pt-10">Delete Resume</h1>
                <p className="text-sm text-muted-foreground pt-2">Are you sure you want to delete resume of <b>{resume?.resumeData?.standardFields?.header?.name}</b>?</p>
                <p className="text-sm text-muted-foreground">This resume may have matches with your job posts that will also be deleted.</p>
                <p className="text-sm text-muted-foreground pt-2">This action cannot be undone.</p>
                <Button
                    variant="destructive"
                    size="sm"
                    className="mt-4 cursor-pointer"
                    onClick={handleDeleteAction}
                    disabled={isDeleting}
                >
                    {isDeleting ? "Deleting Resume..." : "Delete Resume"}
                </Button>
                {deleteError && (
                    <p className="text-red-500 mt-2">Error deleting resume. Please try again.</p>
                )}
            </div>
        </>
    );
}
