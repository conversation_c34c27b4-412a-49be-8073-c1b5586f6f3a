"use client";

import { JobSchema } from "@/data/zod/zodSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Control, useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
} from "../ui/select";
import { SelectGroup } from "@radix-ui/react-select";
import { countryList } from "@/data/location/countryList";
import { SalaryRangeSelector } from "./SalaryRangeSelector";
import { useEffect, useState } from "react";
import { JobDescriptionEditor } from "./JobDescriptionEditor";
import { BenefitsSelector } from "./BenefitsSelector";
import { Button } from "../ui/button";
import { JobListingDuration } from "./JobListingDuration";
import { createJob } from "@/actions/job/createJob";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { LanguageRequirementsSection } from "../company/LanguageRequirementsSection";
import { SkillsSection } from "./SkillsSection";
import { Switch } from "../ui/switch";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { UserRole } from "@prisma/client";

interface CreateJobFormProps {
  companyId: string;
  userRole: UserRole;
}

export function CreateJobForm({ companyId, userRole }: CreateJobFormProps) {
  const router = useRouter();
  const [currency, setCurrency] = useState("USD");
  const [isPending, setIsPending] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (userRole === "ADMIN") {
      setIsAdmin(true);
    }
  }, [userRole]);

  const form = useForm<z.infer<typeof JobSchema>>({
    resolver: zodResolver(JobSchema),
    defaultValues: {
      companyId: companyId,
      employmentType: "",
      jobDescription: "",
      jobTitle: "",
      listingDuration: 30,
      location: "",
      salaryFrom: 0,
      salaryTo: 0,
      interviewType: "ONLINE",
      localRemoteWork: false,
      overseasRemoteWork: false,
      skills: [],
      languageRequirements: [],
      tags: [],
    },
  });

  async function onSubmit(data: z.infer<typeof JobSchema>) {
    try {
      setIsPending(true);
      const jobPost = await createJob(data);

      if (jobPost?.success) {
        toast.success("Job posted successfully");
        if (isAdmin) {
          router.push("/admin/company/job");
        } else {
          router.push("/company/job");
        }
      } else if (jobPost?.error) {
        toast.error(jobPost.error);
      }
    } catch (error) {
      if (error instanceof Error && error.message !== "NEXT_REDIRECT") {
        toast.error(error.message);
      }
    } finally {
      setIsPending(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="jobTitle"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Job Title<span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g. Senior Software Engineer"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="employmentType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Employment Type<span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select employment type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="FULL_TIME">
                      Full Time (Permanent)
                    </SelectItem>
                    <SelectItem value="PART_TIME">Part Time</SelectItem>
                    <SelectItem value="CONTRACT">Contract</SelectItem>
                    <SelectItem value="INTERNSHIP">Internship</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Job Location<span className="text-red-500">*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Location" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Worldwide</SelectLabel>
                      <SelectItem value="worldwide">
                        <div className="flex items-center">
                          <span className="text-xl">🌎</span>
                          <span className="pl-2">Worldwide / Remote</span>
                        </div>
                      </SelectItem>
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>Location</SelectLabel>
                      {countryList.map((country) => (
                        <SelectItem key={country.code} value={country.name}>
                          <div className="flex items-center">
                            <span className="emoji text-xl">
                              {String.fromCodePoint(
                                0x1f1e6 + country.code.charCodeAt(0) - 65,
                                0x1f1e6 + country.code.charCodeAt(1) - 65
                              )}
                            </span>
                            <span className="pl-2">{country.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormItem>
            <FormLabel>
              Salary Range<span className="text-red-500">*</span>
            </FormLabel>
            <div className="flex items-center gap-4">
              <div className="flex flex-col gap-1 w-full">
                <div className="flex justify-between items-center">
                  <Select value={currency} onValueChange={setCurrency}>
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="Currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="GBP">GBP (£)</SelectItem>
                      <SelectItem value="JPY">JPY (¥)</SelectItem>
                      <SelectItem value="PHP">PHP (₱)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <FormControl>
                  <SalaryRangeSelector
                    control={form.control as unknown as Control}
                    minSalary={1000}
                    maxSalary={100000}
                    step={1000}
                    currency={currency}
                  />
                </FormControl>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        </div>

        <FormField
          control={form.control}
          name="jobDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Job Description<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <JobDescriptionEditor field={field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="benefits"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Benefits<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <BenefitsSelector field={field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        <FormField
          control={form.control}
          name="listingDuration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Listing Duration<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <JobListingDuration field={field} plan="free" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="localRemoteWork"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Local Remote Work</FormLabel>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="overseasRemoteWork"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Overseas Remote</FormLabel>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="interviewType"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>
                Interview Type<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="ONLINE" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Online Interview
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="ONSITE" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      On-site Interview
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="HYBRID" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Hybrid Interview
                    </FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Language requirements section */}
        <LanguageRequirementsSection control={form.control} />

        {/* Skills section */}
        <SkillsSection control={form.control} />
        <div className="w-full justify-center">
          <Button
            type="submit"
            disabled={isPending}
            className="cursor-pointer w-[200px] justify-center"
          >
            {isPending ? "Posting..." : "Post Job"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
