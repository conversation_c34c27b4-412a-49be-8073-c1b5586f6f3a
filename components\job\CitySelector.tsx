"use client";

import { useState, useEffect } from "react";
import { Input } from "../ui/input";
import { cityList, City, addCity } from "@/data/location/cityList";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface CitySelectorProps {
  country: string | undefined; // Allow undefined for initial state
  value: string | undefined; // Allow undefined for initial state
  onChange: (value: string) => void;
}

export function CitySelector({ country, value, onChange }: CitySelectorProps) {
  const [open, setOpen] = useState(false);
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [filteredCities, setFilteredCities] = useState<City[]>([]);
  const [searchValue, setSearchValue] = useState("");
  const [isAddingCity, setIsAddingCity] = useState(false);

  useEffect(() => {
    if (country && country !== "worldwide") {
      setCities(cityList[country] || []);
    } else {
      setCities([]);
    }
  }, [country]);

  useEffect(() => {
    // Parse initial value string into array
    if (value) {
      setSelectedCities(value.split(",").map((city) => city.trim()));
    }
  }, [value]);

  useEffect(() => {
    if (searchValue.trim() === "") {
      setFilteredCities(cities);
      return;
    }

    const filtered = cities.filter((city) =>
      city.name.toLowerCase().includes(searchValue.toLowerCase())
    );

    setFilteredCities(filtered);
  }, [searchValue, cities]);

  const handleSelect = (cityName: string) => {
    let newSelectedCities: string[];

    if (selectedCities.includes(cityName)) {
      // Remove city if already selected
      newSelectedCities = selectedCities.filter((city) => city !== cityName);
    } else {
      // Add city if not selected
      newSelectedCities = [...selectedCities, cityName];
    }

    setSelectedCities(newSelectedCities);
    onChange(newSelectedCities.join(", "));
  };

  const handleSelectAll = () => {
    const allCityNames = cities.map((city) => city.name);
    setSelectedCities(allCityNames);
    onChange(allCityNames.join(", "));
  };

  const handleClearAll = () => {
    setSelectedCities([]);
    onChange("");
  };

  const handleAddNewCity = async () => {
    if (!searchValue.trim() || !country || country === "worldwide") return;

    setIsAddingCity(true);
    // Add the new city to the cityList
    try {
      await addCity(country, searchValue.trim());
      setSearchValue("");
      toast.success("Location added successfully!");

      // Select the new city
      const newSelectedCities = [...selectedCities, searchValue.trim()];
      setSelectedCities(newSelectedCities);
      onChange(newSelectedCities.join(", "));

      // Clear search
      setSearchValue("");
    } catch (error) {
      toast.error(`Failed to add city: ${error}`);
    } finally {
      setIsAddingCity(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="relative">
          <Input
            value={selectedCities.length > 0 ? selectedCities.join(", ") : ""}
            readOnly
            onClick={() => !open && setOpen(true)}
            disabled={!country || country === "worldwide"}
            className="cursor-pointer text-start"
          />
          <span
            className={`absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none ${selectedCities.length > 0 ? "hidden" : "block"} text-sm text-muted-foreground`}
          >
            {!country || country === "worldwide"
              ? "Please select a country first"
              : "Select or add cities..."}
          </span>
        </div>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[300px]" align="start">
        <Command>
          <div className="flex items-center gap-2 p-2 border-b">
            <CommandInput
              placeholder="Search cities or type to add..."
              value={searchValue}
              onValueChange={setSearchValue}
            />
            {searchValue.trim() !== "" && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddNewCity}
                type="button"
                className="cursor-pointer"
                disabled={isAddingCity}
              >
                {isAddingCity ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Add"
                )}
              </Button>
            )}
          </div>
          <div className="flex items-center justify-between p-2 border-b">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              type="button"
              className="cursor-pointer"
            >
              Select All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              type="button"
              className="cursor-pointer"
            >
              Clear All
            </Button>
          </div>
          <CommandEmpty>No city found.</CommandEmpty>
          <CommandGroup className="max-h-[200px] overflow-y-auto">
            {filteredCities.map((city) => (
              <CommandItem
                key={city.name}
                value={city.name}
                onSelect={() => handleSelect(city.name)}
              >
                <div className="flex items-center gap-2 w-full">
                  <Checkbox
                    checked={selectedCities.includes(city.name)}
                    className="h-4 w-4"
                  />
                  {city.name}
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

