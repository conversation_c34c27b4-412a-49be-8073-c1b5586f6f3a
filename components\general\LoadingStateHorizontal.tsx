import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  size?: number;
  text?: string;
  className?: string;
  minHeight?: string;
}

export function LoadingStateHorizontal({
  size = 4,
  text = "Loading...",
  className,
  minHeight = "30px",
}: LoadingStateProps) {
  return (
    <div
      className={cn(
        "flex items-center justify-start w-full gap-2 bg-blue-gradient-reverse p-2 rounded-lg",
        className
      )}
      style={{ minHeight }}
    >
      <Loader2 
        className={cn(
          "animate-spin text-amber-300",
          `size-${size}`
        )} 
      />
      {text && (
        <p className="text-sm text-white">{text}</p>
      )}
    </div>
  );
}