import { getJobNotes } from "@/actions/job/getJob";
import { GetUserFeedback } from "@/actions/user/userFeedback";
import { useQuery } from "@tanstack/react-query";
import { number } from "zod";

const fetchUserFeedback = async (
  currentPage: number, 
  pageSize: number
): Promise<any[]> => {
  try {
    const data = await GetUserFeedback(currentPage, pageSize);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

export function useJobNotes(
  currentPage: number, 
  pageSize: number
) {
  const userFeedbackQuery = useQuery({
    queryKey: ["feedback", currentPage, pageSize],
    queryFn: () => {
      if (!currentPage && !pageSize) return null;
      return fetchUserFeedback(currentPage, pageSize);
    },
    enabled: !!currentPage && !!pageSize,
    staleTime: 30000,
  });

  return {
    userFeedbackQuery
  };
}
