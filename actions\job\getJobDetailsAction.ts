"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { JobPostStatus } from "@prisma/client";

export async function getJobDetailsAction(jobId: string, userId?: string) {
  if (!jobId) {
    return { error: "Job ID is required." };
  }

  try {
    const [jobData, savedJob, applicationData] = await Promise.all([
      prisma.jobPost.findUnique({
        where: {
          id: jobId,
          status: JobPostStatus.ACTIVE, // Only show active jobs in public popup
        },
        select: {
            id: true,
            jobTitle: true,
            employmentType: true,
            experienceLevel: true,
            location: true,
            country: true,
            salaryCurrency: true,
            salaryFrom: true,
            salaryTo: true,
            jobDescription: true,
            listingDuration: true,
            interviewType: true,
            localRemoteWork: true,
            overseasRemoteWork: true,
            skills: true,
            languageRequirements: true,
            tags: true,
            status: true,
            createdAt: true,
            company: {
                select: {
                name: true,
                about: true,
                logo: true,
                location: true,
                address: true,
                website: true,
                benefits: true,
                tags: true,
                xAccount: true,
                linkedIn: true,
                englishUsageRatio: true,
                foreignerRatio: true,
                },
            },          
            file: {
                select: {
                    url: true,
                    fileName: true,
                    fileType: true,
                }
            },
        },
      }),
      userId
        ? prisma.savedJobPost.findUnique({
            where: {
              userId_jobPostId: {
                userId: userId,
                jobPostId: jobId,
              },
            },
            select: { id: true },
          })
        : null,
      userId
        ? prisma.appliedJobPost.findFirst({
            where: {
              userId: userId,
              jobPostId: jobId,
            },
            select: { id: true },
          })
        : null,
    ]);

    if (!jobData) {
      return { error: "Job not found or not active." };
    }

    // jobData is a single object from findUnique, not an array.
    // Directly process jobData if it exists.
    const processedJobData = {
      ...jobData,
      file: jobData.file ? { ...jobData.file, url: jobData.file.url ? safeDecrypt(jobData.file.url) : "" } : null,
    };

    console.log({ JOBDETAILS: processedJobData });

    return {
      jobData: processedJobData,
      isSaved: !!savedJob,
      hasApplied: !!applicationData,
    };
  } catch (error) {
    console.error("Error fetching job details action:", error);
    return { error: "Failed to fetch job details." };
  }
}
