import styles, { layout } from "@/styles/style";
import Image from "next/image";

export function Solution() {
    return (
    <>
        <section className={`${layout.sectionInfo} items-start justify-start border rounded-lg p-4`}> {/* Ensure layout.sectionInfo doesn't conflict with grid behavior */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-xl text-muted-foreground">
                    As discussions on X have highlighted the surge in job applications, <PERSON> addresses the challenge of manually reviewing resumes and evaluating job fit without the right tools.
                </div>
                <div>
                    <Image
                        src="/feature/post4.png"
                        key="post4_feature_img"
                        alt="Job post gathered 800+ resumes"
                        width={500}
                        height={250}
                    />
                    <Image
                        src="/feature/post1.png"
                        key="post1_feature_img"
                        alt="Job post gathered 500+ resumes"
                        width={500}
                        height={250}
                    />
                </div>
                <div>
                    <Image
                        src="/feature/post3.png"
                        key="post3_feature_img"
                        alt="Job post gathered 500+ resumes"
                        width={500}
                        height={250}
                    />
                    <Image
                        src="/feature/post2.png"
                        key="post2_feature_img"
                        alt="Job post gathered 500+ resumes"
                        width={500}
                        height={250}
                    />
                </div>
            </div>
        </section>
    </>
    );
}