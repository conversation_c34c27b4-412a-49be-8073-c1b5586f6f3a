"use client";

import * as React from "react";
import { useFieldArray, type Control } from "react-hook-form";
import { FormItem, FormLabel } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Trash2, Plus, XCircleIcon, Loader2, Search, X } from "lucide-react";
import { z } from "zod";
import { JobSchema } from "@/data/zod/zodSchema";
import { defaultSkillsData, type SkillsData } from "@/data/job/skillsData";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface SkillsSectionProps {
  control: Control<z.infer<typeof JobSchema>>;
}

export function SkillsSection({ control }: SkillsSectionProps) {
  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: "skills"
  });
  
  const [open, setOpen] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState("");
  const [searchTerm, setSearchTerm] = React.useState("");

  // Initialize skills data with deduplication
  const [skillsData] = React.useState<SkillsData>(() => {
    const deduplicatedData = { ...defaultSkillsData };
    for (const category in deduplicatedData) {
      deduplicatedData[category] = [...new Set(deduplicatedData[category])].sort();
    }
    return deduplicatedData;
  });

  // Track selected skills for the current category
  const [tempSelectedSkills, setTempSelectedSkills] = React.useState<string[]>([]);

  // Initialize temp selected skills when category changes
  React.useEffect(() => {
    if (selectedCategory) {
      const currentSkills = fields
        .filter(field => field.category === selectedCategory)
        .map(field => field.name);
      setTempSelectedSkills(currentSkills);
    } else {
      setTempSelectedSkills([]);
    }
  }, [selectedCategory, fields]);

  // Filter skills based on search term
  const filteredSkills = React.useMemo(() => {
    if (!selectedCategory) return [];
    const skills = skillsData[selectedCategory] || [];
    if (!searchTerm) return skills;
    return skills.filter(skill => 
      skill.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [selectedCategory, skillsData, searchTerm]);

  const handleSkillToggle = (skill: string) => {
    setTempSelectedSkills(prev => {
      if (prev.includes(skill)) {
        return prev.filter(s => s !== skill);
      } else {
        return [...prev, skill];
      }
    });
  };

  const handleSaveSkills = () => {
    // Keep skills from other categories
    const otherCategorySkills = fields.filter(
      field => field.category !== selectedCategory
    );
    
    // Create new skill objects for selected skills
    const newSkills = tempSelectedSkills.map(skill => ({
      category: selectedCategory,
      name: skill
    }));

    // Replace all fields with updated selection
    replace([...otherCategorySkills, ...newSkills]);
    setOpen(false);
  };

  // Group skills by category for display
  const groupedSkills = React.useMemo(() => {
    return fields.reduce((acc, field) => {
      if (!acc[field.category]) {
        acc[field.category] = [];
      }
      acc[field.category].push(field.name);
      return acc;
    }, {} as Record<string, string[]>);
  }, [fields]);

  return (
    <div className="flex flex-col w-full">
      <div className="flex justify-between items-center mb-4">
        <span className="text-sm font-bold">Required and Related Skills</span>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="cursor-pointer">Add Skills</Button>
          </DialogTrigger>
          <DialogPortal>
                <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
                <div className="fixed inset-0 flex items-center justify-center z-[12000] overflow-auto p-4">
                    <div className="bg-white rounded-lg shadow-lg w-full max-w-[300px] max-h-[90vh] overflow-y-auto">
                        <div className="flex items-center justify-between pt-6 px-6">
                            <DialogTitle className="text-lg font-semibold">
                                Add Skills
                            </DialogTitle>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 cursor-pointer"
                                onClick={() => setOpen(false)}
                            >
                                <X className="h-4 w-4" />
                                <span className="sr-only">Close</span>
                            </Button>
                        </div>
                        <div className="p-6">
                            <div className="p-2">
                                <FormItem>
                                    <FormLabel>Category</FormLabel>
                                    <Select
                                    value={selectedCategory}
                                    onValueChange={(value) => {
                                        setSelectedCategory(value);
                                        setSearchTerm("");
                                    }}
                                    >
                                    <SelectTrigger className="w-full cursor-pointer">
                                        <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent className="popper z-[12000]">
                                        {Object.keys(skillsData).map((category) => (
                                        <SelectItem 
                                            className="cursor-pointer" 
                                            key={category} 
                                            value={category}
                                        >
                                            {category}
                                        </SelectItem>
                                        ))}
                                    </SelectContent>
                                    </Select>
                                </FormItem>
                            </div>

                            <div className="p-2">
                                {selectedCategory && (
                                    <>
                                    <div className="flex items-center gap-2 pb-2">
                                        <Input
                                        placeholder="Search skills..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="flex-1"
                                        />
                                    </div>

                                    <ScrollArea className="h-[300px] border rounded-md p-4">
                                        <div className="space-y-2">
                                        {filteredSkills.map((skill) => (
                                            <div
                                            key={skill}
                                            className="flex items-center space-x-2"
                                            >
                                            <Checkbox
                                                id={skill}
                                                checked={tempSelectedSkills.includes(skill)}
                                                onCheckedChange={() => handleSkillToggle(skill)}
                                            />
                                            <label
                                                htmlFor={skill}
                                                className="text-sm cursor-pointer"
                                            >
                                                {skill}
                                            </label>
                                            </div>
                                        ))}
                                        </div>
                                    </ScrollArea>

                                    <div className="flex justify-center pt-4 gap-2">
                                        <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => {
                                            setOpen(false);
                                            setSelectedCategory("");
                                            setSearchTerm("");
                                        }}
                                        >
                                        Cancel
                                        </Button>
                                        <Button
                                        type="button"
                                        onClick={handleSaveSkills}
                                        >
                                        Save
                                        </Button>
                                    </div>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
          </DialogPortal>
        </Dialog>
      </div>

      {Object.keys(groupedSkills).length > 0 && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Category</TableHead>
              <TableHead>Skills</TableHead>
              <TableHead className="w-[100px]">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(groupedSkills).map(([category, skills]) => (
              <TableRow key={category}>
                <TableCell className="font-medium">{category}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {skills.map((skill) => (
                      <Badge 
                        key={`${category}-${skill}`}
                        variant="secondary"
                      >
                        {skill}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 hover:bg-transparent"
                          onClick={() => {
                            const index = fields.findIndex(
                              field => field.category === category && field.name === skill
                            );
                            if (index !== -1) {
                              remove(index);
                            }
                          }}
                        >
                          <XCircleIcon className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const indices = fields
                        .map((field, index) => field.category === category ? index : -1)
                        .filter(index => index !== -1)
                        .reverse();
                      indices.forEach(index => remove(index));
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}


