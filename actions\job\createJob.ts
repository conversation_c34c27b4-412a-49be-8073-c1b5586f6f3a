"use server";

import { JobSchema } from "@/data/zod/zodSchema";
import { z } from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { jobListingDurationPricing } from "@/utils/jobListingDurationPricing";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { JobDescPost } from "@/types/customTypes";
import { createJobFromFileData } from "@/data/job/job";
import { sendInngestEvent } from "@/utils/sendInngestEvent";

export async function createJob(data: z.infer<typeof JobSchema>) {
  const session = await auth();

  if (!session?.user?.id) {
    // Or handle as per your middleware strategy, e.g., throw new Error("Unauthorized");
    // This assumes middleware might not cover this specific action directly,
    // or as a defense-in-depth. If covered by middleware, this error indicates an issue.
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const validatedData = JobSchema.parse(data);
    validatedData.companyId = session?.user?.companyId as string;

    if (validatedData) {
      await prisma.jobPost.create({
        data: {
          userId: session?.user?.id as string,
          companyId: validatedData.companyId as string,
          jobTitle: validatedData.jobTitle,
          employmentType: validatedData.employmentType,
          experienceLevel: validatedData.experienceLevel,
          country: validatedData.country,
          location: validatedData.location,
          department: validatedData.department,
          salaryCurrency: validatedData.salaryCurrency,
          salaryFrom: validatedData.salaryFrom,
          salaryTo: validatedData.salaryTo,
          jobDescription: validatedData.jobDescription,
          listingDuration: validatedData.listingDuration,
          interviewType: validatedData.interviewType,
          localRemoteWork: validatedData.localRemoteWork,
          overseasRemoteWork: validatedData.overseasRemoteWork,
          skills: validatedData.skills.map((skill) =>
            JSON.stringify({
              category: skill.category,
              name: skill.name,
            })
          ),
          languageRequirements: validatedData.languageRequirements.map((lang) =>
            JSON.stringify({
              type: lang.type,
              language: lang.language,
              level: lang.level,
              certification: lang.certification,
            })
          ),
          tags: validatedData.tags,
          status: "ACTIVE",
        },
        select: {
          id: true,
        },
      });

      const pricingTier = jobListingDurationPricing.find(
        (tier) => tier.days === validatedData.listingDuration
      );

      if (!pricingTier) {
        throw new Error("Invalid listing duration");
      }

        await sendInngestEvent({
            name: "job/resume-matching",
            data: {},
          });
    }

    return {
      success: "Success! Job information was saved.",
    };
  } catch (error) {
    return { error: `Error was encountered, please try again. ${error}` };
  }
}

export async function CreateJobFromFile(jobPost: JobDescPost) {
  // Arcjet protection
  await ensureServerActionProtection();

  if (!jobPost) {
    return { error: "Missing resume data." };
  }

  try {
    const result = await createJobFromFileData(jobPost);
    if (result) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
}
