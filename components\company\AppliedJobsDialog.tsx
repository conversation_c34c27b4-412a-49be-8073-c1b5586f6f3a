"use client";

import {
    <PERSON>alog,
    DialogOverlay,
    DialogPortal,
    DialogTitle,
    DialogTrigger,
    DialogClose,
  } from "@/components/ui/dialog";
  import { ScrollArea } from "@/components/ui/scroll-area";
  import { X } from "lucide-react";
  import { <PERSON><PERSON> } from "@/components/ui/button";
  import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useUserJobApplications } from "@/hooks/useUserJobApplications";
import { ResumeMatchDialog } from "../user/resume/ResumeMatchEvaluation";
import { ShortlistCheckbox } from "./ShortlistCheckbox";
import { EyeIcon } from "lucide-react";
import Image from "next/image";
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

export function AppliedJobsDialog({ 
  count, 
  userId,
  companyId,
  jobId,
  userName,
  onClick
}: { 
  count: number; 
  userId?: string;
  companyId?: string;
  jobId?: string;
  userName?: string;
  onClick?: (e: React.MouseEvent) => void;
}) {
    const { userApplicationsQuery } = useUserJobApplications(userId, companyId);
    const { jobApplicantsQuery } = jobId ? useUserJobApplications(undefined, undefined, jobId) : { jobApplicantsQuery: { isLoading: false, data: null } };
    
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="link" 
          className="text-primary hover:underline cursor-pointer"
          onClick={onClick}
        >
          {count}
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[600px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between pt-6 pr-6 pl-6">
              <DialogTitle className="text-lg font-semibold">
              {jobId ? (
                "Job Applicants"
              ) : (
                "Applied Jobs"
              )}                
              </DialogTitle>
              <DialogClose asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (onClick) onClick(e);
                  }}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
            <div className="flex items-center px-6">                
            {userName}
            </div>
            <div className="overflow-y-auto px-6 pb-6">
              {jobId ? (
                // Show job applicants if jobId is provided
                jobApplicantsQuery.isLoading || !jobApplicantsQuery.data ? (
                  <div className="text-center py-4">Loading... {JSON.stringify({isLoading: jobApplicantsQuery.isLoading, hasData: !!jobApplicantsQuery.data})}</div>
                ) : jobApplicantsQuery.data.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    No job applicants found.
                  </div>
                ) : (
                  <ScrollArea className="w-full max-h-[400px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead style={{ width: "10%" }}>Applied</TableHead>
                          <TableHead style={{ width: "45%" }}>Name</TableHead>
                          <TableHead style={{ width: "10%" }}>Match</TableHead>
                          <TableHead style={{ width: "15%" }}>Evaluation</TableHead>
                          <TableHead style={{ width: "10%" }}>Resume</TableHead>
                          <TableHead style={{ width: "10%" }}>Shortlist</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {jobApplicantsQuery.data.map((job) => (
                          <TableRow key={job.id}>
                          <TableCell style={{ width: "10%" }}>
                            {new Date(job.createdAt).toLocaleDateString()}
                          </TableCell>
                            <TableCell
                              style={{ width: "45%" }}
                              className="flex items-center gap-2"
                            >
                              <Image
                                src={job.user.resumes?.picture || "/icons/profile.png"}
                                alt={job.user.firstName || ""}
                                width={28}
                                height={28}
                                className="rounded-full"
                              />
                              {job.user.firstName}&nbsp;{job.user.lastName}
                            </TableCell>
                            <TableCell style={{ width: "10%" }}>
                              <MatchScoreCircle
                                score={
                                  job.user.resumes?.resumeMatches?.[0]?.overall_score
                                }
                              />
                            </TableCell>
                            <TableCell style={{ width: "15%" }} className="pl-4">
                              <ResumeMatchDialog
                                resumeMatch={job.user.resumes?.resumeMatches?.[0]}
                                applicantName={`${job.user.firstName} ${job.user.lastName}`}
                              />
                            </TableCell>
                            <TableCell style={{ width: "10%" }} className="pl-4">
                              <a
                                href={`${APP_URL}/home/<USER>/resumedoc/${job.user?.resumes?.id}`}
                                target="_blank"
                                className="text-primary hover:underline cursor-pointer"
                                title="View Resume"
                              >
                                <Button
                                  variant="ghost"
                                  className="text-primary hover:underline cursor-pointer"
                                  title="View Resume"
                                >
                                  <EyeIcon className="w-6 h-6" />
                                </Button>
                              </a>
                            </TableCell>
                            <TableCell
                              style={{ width: "10%" }}
                              className="pl-4 cursor-pointer"
                            >
                              <ShortlistCheckbox
                                jobId={job.id}
                                resumeId={job.user?.resumes?.id || ""}
                                initialShortlisted={job.user?.isShortlisted || false}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                )
              ) : (
                // Show user applications if jobId is not provided
                userApplicationsQuery.isLoading || !userApplicationsQuery.data ? (
                  <div className="text-center py-4">Loading...</div>
                ) : userApplicationsQuery.data.length === 0 ? (
                  <div className="text-center py-4 text-muted-foreground">
                    No applied jobs found
                  </div>
                ) : (
                  <ScrollArea className="w-full max-h-[400px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Applied</TableHead>
                          <TableHead>Job Title</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {userApplicationsQuery.data.map((job) => (
                          <TableRow key={job.jobPost.id}>
                            <TableCell>{new Date(job.jobPost.createdAt).toLocaleDateString()}</TableCell>
                            <TableCell>{job.jobPost.jobTitle}</TableCell>
                            <TableCell>{job.jobPost.status || ""}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                )
              )}
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
