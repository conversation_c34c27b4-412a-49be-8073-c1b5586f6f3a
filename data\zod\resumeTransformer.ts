import { 
    ResumeData, 
    ExperienceSection, 
    EducationSection, 
    AdditionalSection,
    HeaderSection,
    SummarySection
  } from "@/data/zod/resumeZod";
  
  type ParsedExperience = Partial<ExperienceSection> & {
    startdate?: string; // Added to reflect lowercase key after normalizeKeys
    start_date?: string; // Keep for potential _ in original key
    enddate?: string;   // Added to reflect lowercase key after normalizeKeys
    end_date?: string;   // Keep for potential _ in original key
    employment_type?: string;
  };

  type ParsedEducation = Partial<EducationSection> & {
    startdate?: string; // Added to reflect lowercase key after normalizeKeys
    start_date?: string; // Keep for potential _ in original key
    enddate?: string;   // Added to reflect lowercase key after normalizeKeys
    end_date?: string;   // Keep for potential _ in original key
  };
  
  type ParsedAdditionalSection = Partial<AdditionalSection>;

  // Helper function to ensure a field that should be Tiptap content is a valid Tiptap JSON string.
  // If it's plain text, it wraps it. If it's an object, it stringifies it.
  export function ensureTiptapJSONString(input: any): Record<string, any> { // Changed return type to object
    if (input === null || input === undefined) {
      return { type: "doc", content: [] }; // Default empty Tiptap object
    }

    if (Array.isArray(input)) {
      const containsObjects = input.some(item => typeof item === 'object' && item !== null);

      if (containsObjects) {
        // If it contains objects, format as a bullet list for clarity.
        const listItems = input.map(item => {
          let text;
          if (typeof item === 'object' && item !== null) {
            // Create a readable string from object properties.
            text = Object.entries(item).map(([key, value]) => `${key}: ${value}`).join('; ');
          } else {
            text = String(item);
          }
          return { 
            type: 'listItem', 
            content: [{ type: 'paragraph', content: [{ type: 'text', text }] }] 
          };
        });
        return { type: 'doc', content: [{ type: 'bulletList', content: listItems }] };
      } else {
        // If it's an array of primitives, join them into a single paragraph with line breaks.
        const plainText = input.map(String).join('\n');
        return { type: "doc", content: [{ type: "paragraph", content: [{ type: "text", text: plainText }] }] };
      }
    }

    if (typeof input === 'string') {
      try {
        const parsed = JSON.parse(input); // Check if it's valid JSON
        return parsed;     // It is, so return as is (assuming it's a Tiptap object)
      } catch (e) {
        // Not valid JSON, treat as plain text to be wrapped
        return { type: "doc", content: [{ type: "paragraph", content: [{ type: "text", text: input }] }] };
      }
    }

    // If it's an object (could be a Tiptap doc object already)
    if (typeof input === 'object') {
      return input; // It's already an object, return as is
    }

    // Fallback for any other unexpected type
    return { type: "doc", content: [{ type: "paragraph", content: [{ type: "text", text: String(input) }] }] };
  }
  
  export function transformParsedDataToResumeSchema(parsed_data: any): ResumeData {
    // Normalize keys to lowercase for consistent access
    const normalizedData = normalizeKeys(parsed_data);
    const rootData = normalizedData.resume || normalizedData; // Data from LLM, possibly nested under "resume"
    // Access with fully lowercased keys as produced by normalizeKeys
    const standardFieldsInput = rootData.standardfields || rootData; 
    const headerInput = standardFieldsInput.header || standardFieldsInput; 
    
    // console.log("--- Input Data to Transformer ---");
    // console.log("Parsed Data (Original):", JSON.stringify(parsed_data, null, 2));
    // console.log("Normalized Root Data:", JSON.stringify(rootData, null, 2));
    // console.log("Standard Fields Input:", JSON.stringify(standardFieldsInput, null, 2));

    // Handle additional sections which could be an object or an array
    let additionalSectionsArray = [];
    if (rootData.additionalsections) { // Use lowercase key
      if (Array.isArray(rootData.additionalsections)) {
        additionalSectionsArray = rootData.additionalsections;
      } else if (typeof rootData.additionalsections === 'object') {
        // Convert object to array of sections
        additionalSectionsArray = Object.entries(rootData.additionalsections).map(([key, value]) => {
          if (typeof value === 'object' && value !== null) {
            // If value is already a section-like object with header and details
            const sectionValue = value as any;
            if (sectionValue.header && (Array.isArray(sectionValue.details) || typeof sectionValue.details === 'string')) {
              return sectionValue;
            }
            // Otherwise, create a section from the key and value
            return {
              header: key,
              details: Array.isArray(sectionValue) ? sectionValue : [String(sectionValue)],
              type: ''
            };
          }
          return {
            header: key,
            details: [String(value)],
            type: ''
          };
        });
      }
    }

    // Process the additionalSections to ensure details are strings
    const processedAdditionalSections = additionalSectionsArray.map((section: any) => {
      // Special handling for employment history
      if (section.header && String(section.header).toLowerCase().includes('employment')) { // Ensure header is string before toLowerCase
        // Extract employment data and add to experience array
        if (Array.isArray(section.details)) {
          const experienceItems = section.details.map((item: any) => {
            if (typeof item === 'object') {
              return {
                startDate: item.startDate || item.start_date || '',
                endDate: item.endDate || item.end_date || '',
                title: item.title || '',
                company: item.company || '',
                employmentType: item.employmenttype || item.employment_type || '', // Use lowercase key
                location: item.location || '', // Use lowercase key
                description: Array.isArray(item.description) ? item.description : [],
                highlights: Array.isArray(item.highlights) ? item.highlights : [],
                technologies: Array.isArray(item.technologies) ? item.technologies : [],
              };
            }
            return null;
          }).filter(Boolean);
          
          // Add these to the experience array
          if (!Array.isArray(standardFieldsInput.experience)) {
            standardFieldsInput.experience = []; // Initialize if not present
          }
          standardFieldsInput.experience.push(...experienceItems);
          
          // Convert the details to strings for the additional section
          return {
            ...section,
            details: section.details.map((item: any) => {
              if (typeof item === 'object') {
                const company = item.company || '';
                const title = item.title || '';
                const dates = `${item.startDate || item.start_date || ''} - ${item.endDate || item.end_date || 'Present'}`;
                return `${company} - ${title} (${dates})`;
              }
              return String(item);
            })
          };
        }
      }
      
      // For other sections, ensure all details are strings
      return {
        ...section,
        details: ensureTiptapJSONString(section.details),
      };
    });

    return {
      standardFields: {
        header: {
          name: headerInput.name || headerInput.full_name || headerInput.fullname || '', // Access with lowercase keys
          email: headerInput.email || headerInput.contact_email || headerInput.email_address || '', // Access with lowercase keys
          phone: (() => {
            const phoneVal = headerInput.phone || headerInput.phone_number || headerInput.contact_number; // Access with lowercase keys
            if (Array.isArray(phoneVal)) return phoneVal.map(String);
            if (phoneVal && typeof phoneVal === 'string') return [phoneVal];
            if (phoneVal && typeof phoneVal === 'number') return [String(phoneVal)];
            return [];
          })(),
          website: headerInput.website || headerInput.personal_website || headerInput.portfolio_url || '',
          xaccount: headerInput.xaccount || headerInput.twitter_handle || headerInput.twitter || '',
          linkedin: headerInput.linkedin || headerInput.linkedin_profile || headerInput.linkedin_url || '', // Access with lowercase keys
          github: headerInput.github || headerInput.github_profile || headerInput.github_url || '', // Access with lowercase keys
          address: headerInput.address || headerInput.location || headerInput.address_line || headerInput.physical_address || '', // Access with lowercase keys
          shortAbout: headerInput.shortabout || headerInput.headline || '', // Access with lowercase keys
        } as HeaderSection,
        summary: (() => { // IIFE for summary transformation
            let rawSummaryValue: any;
            
            // Prioritize standardFieldsInput.summary
            const summarySource = standardFieldsInput.summary;

            if (summarySource) {
                if (typeof summarySource === 'object') {
                    // Check if summarySource itself is a Tiptap doc
                    if (summarySource.type === 'doc' && Array.isArray(summarySource.content)) {
                        rawSummaryValue = summarySource;
                    // Check if summarySource has a 'content' property that might be the Tiptap doc or string
                    } else if (summarySource.hasOwnProperty('content')) {
                        rawSummaryValue = summarySource.content;
                    } else {
                    // If it's an object but not in expected Tiptap format, pass it as is
                        rawSummaryValue = summarySource;
                    }
                } else {
                    // If summarySource is a string or primitive
                    rawSummaryValue = summarySource;
                }
            }

            // Fallback logic if rawSummaryValue is still not determined or is empty
            if (rawSummaryValue === undefined || rawSummaryValue === null || (typeof rawSummaryValue === 'string' && rawSummaryValue.trim() === '')) {
              const fallbackCandidates = [
                rootData.summary, // from the root of the parsed data
                headerInput.professional_summary, // from header section
                headerInput.about_me,             // from header section
                headerInput.summary,              // from header section (another possible key)
              ];
              for (const candidate of fallbackCandidates) {
                if (candidate !== undefined && candidate !== null && (typeof candidate !== 'string' || candidate.trim() !== '')) {
                  rawSummaryValue = candidate; // Assign the first non-empty candidate
                  break;
                }
              }
            }
            // The ensureTiptapJSONString now returns an object, so stringify it here
            const transformedSummaryContent = JSON.stringify(ensureTiptapJSONString(rawSummaryValue ?? ''));
            return { content: transformedSummaryContent };
          })(),
        experience: (Array.isArray(standardFieldsInput?.experience) || Array.isArray(standardFieldsInput?.workexperience))
          ? (standardFieldsInput.experience || standardFieldsInput.workexperience).map((exp: ParsedExperience): ExperienceSection => ({
              // Correctly access lowercase keys from the normalized 'exp' object
              // and assign to the camelCase keys of ExperienceSection
              startDate: (() => {
                const date = exp?.startdate || exp?.start_date || ''; 
                // console.log(`Experience Item: ${exp?.title}, Raw StartDate from exp.startdate: ${exp?.startdate}, from exp.start_date: ${exp?.start_date}, Transformed StartDate: ${date}`);
                return date;
              })(),
              endDate: (() => {
                const date = exp?.enddate || exp?.end_date || '';
                // console.log(`Experience Item: ${exp?.title}, Raw EndDate from exp.enddate: ${exp?.enddate}, from exp.end_date: ${exp?.end_date}, Transformed EndDate: ${date}`);
                return date;
              })(),
              title: exp?.title || '',
              company: exp?.company || '',
              employmentType: exp?.employmentType || exp?.employment_type || '',
              location: exp?.location || '', // Use lowercase key
              description: JSON.stringify(ensureTiptapJSONString(exp?.description)),
              highlights: Array.isArray(exp?.highlights) ? exp.highlights : [],
              technologies: Array.isArray(exp?.technologies) ? exp.technologies : [],
            }))
          : [],
        education: Array.isArray(standardFieldsInput?.education)
          ? standardFieldsInput.education.map((edu: ParsedEducation): EducationSection => ({
              // Correctly access lowercase keys from the normalized 'edu' object
              // and assign to the camelCase keys of EducationSection
              startDate: (() => {
                const date = edu?.startdate || edu?.start_date || ''; 
                // console.log(`Education Item: ${edu?.degree}, Raw StartDate from edu.startdate: ${edu?.startdate}, from edu.start_date: ${edu?.start_date}, Transformed StartDate: ${date}`);
                return date;
              })(),
              endDate: (() => {
                const date = edu?.enddate || edu?.end_date || '';
                // console.log(`Education Item: ${edu?.degree}, Raw EndDate from edu.enddate: ${edu?.enddate}, from edu.end_date: ${edu?.end_date}, Transformed EndDate: ${date}`);
                return date;
              })(),
              degree: edu?.degree || '',
              institution: edu?.institution || '',
              location: edu?.location || '', // Use lowercase key
              details: JSON.stringify(ensureTiptapJSONString(edu?.details)),
              gpa: edu?.gpa || '',
              honors: Array.isArray(edu?.honors) ? edu.honors : [],
            }))
          : [],        
        skills: Array.isArray(standardFieldsInput?.skills) ? standardFieldsInput.skills.map(String) : [],
      },
      additionalSections: processedAdditionalSections.map((section: any): AdditionalSection => ({
        header: section?.header || '',
        details: JSON.stringify(ensureTiptapJSONString(section.details)), // Stringify the object returned by ensureTiptapJSONString
        type: section?.type || '',
      })),
    };
  }

  // Helper function to normalize object keys to lowercase
  function normalizeKeys(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(normalizeKeys);
    }

    return Object.keys(obj).reduce((acc, key) => {
      acc[key.toLowerCase()] = normalizeKeys(obj[key]);
      return acc;
    }, {} as Record<string, any>); // Ensure the accumulator is correctly typed
  }
