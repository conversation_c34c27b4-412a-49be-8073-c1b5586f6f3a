"use client";

import { SaveUserProfilePicture } from "@/actions/file/saveFile";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CameraIcon, Upload } from "lucide-react";
import { useState } from "react";
import "@/styles/uploadthing.css";
import { SaveCompanyLogo } from "@/actions/company/getCompany";

export function UploadLogo({
  companyId,
}: {
  companyId?: string;
}) {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center justify-center gap-2 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <CameraIcon className="w-4 h-4" />
          <span className="text-sm text-muted-foreground">Change</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Change Logo</DialogTitle>
          <DialogDescription>Upload your recent logo.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 justify-center">
          <UploadDropzone
            endpoint="imageUploader"
            className="custom-class h-60 cursor-pointer w-full"
            onClientUploadComplete={(res) => {
              // Do something with the response
              toast.success("Logo uploaded successfully.");

              // Create the file data directly from the response
              const data = res;
              if (data && data[0]) {
                SaveCompanyLogo(companyId as string, data[0].ufsUrl)
                  .then(() => {
                    toast.success("Logo saved successfully.");
                    // Close the dialog on successful upload
                    setOpen(false);
                  })
                  .catch((error) => {
                    console.error("Failed to save to database:", error);
                    toast.error("Failed to save file information");
                  });
              }
            }}
            onUploadError={(error: Error) => {
              toast.error(`ERROR! A problem was encountered. ${error.message}`);
            }}
            onUploadBegin={(name) => {
              // Do something once upload begins
              toast.info(`Uploading: ${name}`);
            }}
            onChange={(acceptedFiles) => {
              // Do something with the accepted files
            }}
            content={{
              allowedContent({ ready, isUploading }) {
                if (!ready) return "Checking what you allow";
                if (isUploading) return "Uploading your logo...";
                return <>Images (max size of 4MB per file)</>;
              },
            }}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
