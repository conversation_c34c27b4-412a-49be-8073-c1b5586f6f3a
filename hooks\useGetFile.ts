import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { GetFile } from "@/actions/file/getFile";
import { DeleteFile } from "@/actions/file/deleteFile";

// Fetch resume data
const fetchFile = async (id: string): Promise<any> => {
  try {
    const data = await GetFile(id);
    return data as any;
  } catch (error) {
    console.error("Resume fetch error:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to fetch resume"
    );
  }
};

export function useGetFile(id: string) {
  const queryClient = useQueryClient();

  const fileQuery = useQuery({
    queryKey: ["file", id],
    queryFn: () => {
      if (!id) return null;
      return fetchFile(id);
    },
    enabled: !!id,
    staleTime: 30000,
  });

  const deleteMutation = useMutation({
    mutationFn: async (resumeId: string) => {
      return await DeleteFile(resumeId);
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["file"] });
    },
  });

  return {
    fileQuery,
    isLoading: fileQuery.isLoading,
    isError: fileQuery.isError,
    file: fileQuery.data,
    deleteMutation,
    isDeleting: deleteMutation.isPending,
    deleteError: deleteMutation.isError,
    deleteResult: deleteMutation.data,
  };
}
