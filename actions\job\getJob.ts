"use server";

import {
  getAllJobsRawTextData,
  getAppliedJobsCompanyData,
  getCompanyJobMatchesData,
  getFavoriteJobsData,
  getJobApplicantsCountData,
  getJobApplicantsData,
  getJobData,
  getJobListData,
  getJobMatchestData,
  getJobNotesData,
  getJobResumeMatchesData,
  getJobShortlistedApplicantsData,
  getJobShortlistedCountData,
  getJobUserData,
  getUserJobApplicationsData,
  getUserJobMatchesData,
  getUserJobShortlistData,
} from "@/data/job/job";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export async function getJob(id: string) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const job = await getJobData(id);
    return job;
  } catch (error) {
    console.error("Error fetching job:", error);
    return null;
  }
}

export async function getJobUser(id: string, userId?: string) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const job = await getJobUserData(id, userId);

    if (job) {
      return {
        success: "Success! Job list was retrieved.",
        data: job,
      };
    } else {
      return {
        error: "Error! Failed to get job.",
        data: null,
      };
    }
  } catch (error) {
    return {
      error: `Error! Failed to get job: ${error}`,
      data: null,
    };
  }
}

export async function getJobList(
  userId: string,
  isAdmin: boolean,
  page?: number,
  pageSize?: number
) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const jobList = await getJobListData(userId, isAdmin);

    return getJobListData(userId, isAdmin, page, pageSize);
  } catch (error) {
    return null;
  }
}

export async function getFavoriteJobs(
  userId: string,
  page?: number,
  pageSize?: number
) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }
  if (session.user.id !== userId) {
    throw new Error("Unauthorized access to favorite jobs.");
  }

  await ensureServerActionProtection();

  try {
    const result = await getFavoriteJobsData(userId, page, pageSize);
    return result; // This now returns { favorites, totalPages, error }
  } catch (error) {
    console.error("Error in getFavoriteJobs action:", error);
    return { favorites: [], totalPages: 0, error: "Failed to fetch favorite jobs." };
  }
}

export async function getUserJobApplications(
  userId: string,
  page?: number,
  pageSize?: number
) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }
  if (session.user.id !== userId) {
    throw new Error("Unauthorized access to job applications.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const result = await getUserJobApplicationsData(userId, page, pageSize);
    return result; // This now returns { applications, totalPages, error }
  } catch (error) {
    console.error("Error in getUserJobApplications action:", error);
    return { applications: [], totalPages: 0, error: "Failed to fetch job applications." };
  }
}

export async function getUserJobMatches(
  resumeId: string,
  page?: number, // Optional page number
  pageSize?: number // Optional page size
) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    // Pass page and pageSize to the data fetching function
    // The data function already has defaults if these are undefined
    const result = await getUserJobMatchesData(resumeId, page, pageSize);
    
    // getUserJobMatchesData now returns an object { matches: [], totalPages: 0, error?: string }
    // The page component expects this structure.
    return result;
  } catch (error) {
    console.error("Error in getUserJobMatches action:", error);
    return { matches: [], totalPages: 0, error: "Failed to fetch job matches." };
  }
}

export async function getCompanyJobMatches(
  companyId: string,
  page?: number, // Optional page number
  pageSize?: number, // Optional page size
  score?: number // Optional score
) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    // Pass page and pageSize to the data fetching function
    // The data function already has defaults if these are undefined
    const result = await getCompanyJobMatchesData(companyId, page, pageSize, score);
    
    // getUserJobMatchesData now returns an object { matches: [], totalPages: 0, error?: string }
    // The page component expects this structure.
    return result;
  } catch (error) {
    console.error("Error in getUserJobMatches action:", error);
    return { matches: [], totalPages: 0, error: "Failed to fetch job matches." };
  }
}

export async function getJobApplicants(jobId: string, userId?: string) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();


  try {
    const data = await getJobApplicantsData(jobId, userId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Error in getJobApplicants:", error);
    return [];
  }
}

export async function getJobResumeMatches(jobId: string, resumeId?: string, score?: number) {
  const session = await auth();

 if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getJobResumeMatchesData(jobId, resumeId, score);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    return [];
  }
}

export async function getJobNotes(jobId: string, resumeId: string) {
  const session = await auth();

 if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getJobNotesData(jobId, resumeId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    return [];
  }
}

export async function getJobShortlistedApplicants(jobId: string) {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getJobShortlistedApplicantsData(jobId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    // console.error("Error in getJobApplicants:", error);
    return [];
  }
}

// Add this function to get the count of shortlisted applicants for a job
export async function getJobShortlistedCount(jobId: string): Promise<number> {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const count = await getJobShortlistedCountData(jobId);
    return count;
  } catch (error) {
    console.error("Error getting shortlisted count:", error);
    return 0;
  }
}

export async function getJobApplicantsCount(jobId: string): Promise<number> {
  const session = await auth();

 if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const count = await getJobApplicantsCountData(jobId);
    return count;
  } catch (error) {
    console.error("Error getting shortlisted count:", error);
    return 0;
  }
}

export const getCompanyJobApplications = async (
  userId: string,
  companyId: string
) => {
  const session = await auth();

   if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getAppliedJobsCompanyData(userId, companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    return {
      error: `Error! Failed to fetch user job applications: ${error}`,
      data: null,
    };
  }
};

export const GetAllJobsRawText = async () => {

  // Arcjet protection
  await ensureServerActionProtection();


  try {
    // Fetch all jobs
    const jobs = await getAllJobsRawTextData();

    if (!jobs) {
      return {
        error: "Error! Failed to fetch resume data.",
        data: null,
      };
    }

    return {
      success: "Success! Resume data retrieved.",
      data: jobs,
    };
  } catch (error) {
    console.error("Error fetching resume raw text:", error);
    return {
      error: `Error fetching resume data: ${error}`,
      data: null,
    };
  }
};

export async function getUserJobShortlist(jobId: string, resumeId: string) {
  const session = await auth();

 if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const data = await getUserJobShortlistData(jobId, resumeId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    // console.error("Error in getJobApplicants:", error);
    return [];
  }
};

export async function getJobMatchesCount(jobId: string): Promise<number> {
  const session = await auth();

 if (!session?.user?.id) {
    throw new Error("User not authenticated.");
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {
    const count = await getJobMatchestData(jobId);
    return count;
  } catch (error) {
    console.error("Error getting shortlisted count:", error);
    return 0;
  }
}
