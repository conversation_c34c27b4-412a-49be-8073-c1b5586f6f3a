import styles from "@/styles/style";
import Button from "./Button";
import Link from 'next/link';

const CTA: React.FC = () => (
    <section className={`${styles.flexCenter} sm:my-10 my-4 sm:px-10 px-4 sm:py-10 py-4 sm:flex-row flex-col bg-black-gradient-2 rounded-[20px] box-shadow`}>
        <div className="flex-1 flex flex-col">
            <h2 className={styles.heading2}>
                Try our service now!
            </h2>
            <p className={`${styles.paragraph} max-w-[470px] mt-5`}>
                Smarter hiring, faster matches, and zero guesswork — all in one AI-driven platform.
            </p>
        </div>
        <div className={`${styles.flexCenter} sm:ml-10 ml-0 sm:mt-0 mt-10`}>
            <Link href="/auth/signup" passHref>
                <Button text="Get Started" />
            </Link>
        </div>
    </section>
)

export default CTA;