"use client";

import { useState } from "react";
import { CopyLinkMenuItem } from "@/components/general/CopyLink";
import { QuestionTooltip } from "@/components/info/InfoTooltip";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"; // Table specific imports
import {
  DropdownMenuLabel,
} from "@radix-ui/react-dropdown-menu";
import { EyeIcon, X } from "lucide-react"; // Added X icon
import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link"; // Keep for other links if any
import { MatchScoreCircle } from "@/components/company/MatchScoreCircle";
import { ResumeMatchDialog } from "@/components/user/resume/ResumeMatchEvaluation";
import { JobDetails } from "@/components/job/JobDetails";
import {
 Dialog as ShadDialog,
 // DialogContent will be replaced by manual structure
 DialogPortal as ShadDialogPortal,
 DialogOverlay as ShadDialogOverlay,
 DialogClose as ShadDialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input"; // Import Input component
// Removed MainPagination import as it's now handled by the page

interface UserJobMatchesProps {
    data: any[]; // Consider defining a more specific type for job match items
    // Pagination props are no longer needed here as MainPagination is in the page component
}

export function UserJobMatches({ data }: UserJobMatchesProps) {
    const [selectedJobIdForDetails, setSelectedJobIdForDetails] = useState<string | null>(null);
    const [isJobDetailsDialogOpen, setIsJobDetailsDialogOpen] = useState(false);

    const openJobDetailsDialog = (jobId: string) => {
        setSelectedJobIdForDetails(jobId);
        setIsJobDetailsDialogOpen(true);
    };

    const [searchTerm, setSearchTerm] = useState("");

    const filteredData = data?.filter((item: any) => {
        if (!searchTerm) return true; // If no search term, show all data
        const jobTitleMatch = item.jobTitle?.toLowerCase().includes(searchTerm.toLowerCase());
        const companyNameMatch = item.company?.name?.toLowerCase().includes(searchTerm.toLowerCase());
        // Add more fields to search if needed, e.g., item.location
        return jobTitleMatch || companyNameMatch;
    }) || [];

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="text-2xl">Your Job Matches</CardTitle>
                    <CardDescription>
                        <div className="flex items-center justify-between gap-2 pb-4">
                        Jobs that matches your resume with a score of 70 and above.
                        <Input
                            type="text"
                            placeholder="Search by job title, company..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="max-w-xs h-9"
                        />
                        <Button variant="default" size="sm" className="cursor-pointer" onClick={() => {window.location.href = "/public/job/list"}}>
                            Find a Job
                        </Button>
                        </div>
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Posted</TableHead>
                                <TableHead>Company</TableHead>
                                <TableHead>Job Title</TableHead>
                                <TableHead>
                                    Matched <QuestionTooltip 
                                    content="Your resume matched to a job using our AI matching algorithm."
                                    />
                                </TableHead>
                                <TableHead>Experience</TableHead>
                                <TableHead>Skills</TableHead>
                                <TableHead>Education</TableHead>
                                <TableHead>Evaluation</TableHead>
                                <TableHead>Shortlisted?</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredData.map((item: any) => (
                            <TableRow key={item.id} className="cursor-pointer hover:bg-muted/50">
                                <TableCell>{new Date(item.createdAt).toLocaleDateString()}</TableCell>
                                <TableCell className="items-center flex gap-2">                                    
                                    <Image
                                        src={item.company?.logo || ""}
                                        alt={item.company?.name || ""}
                                        width={32}
                                        height={32}
                                    /> 
                                    {item.company.name}
                                </TableCell>
                                <TableCell className="hover:underline cursor-pointer" onClick={() => {window.location.href = `/home/<USER>/job/${item.id}`}}>
                                    {item.jobTitle}
                                </TableCell>
                                <TableCell className="items-center justify-center">
                                    <MatchScoreCircle score={item.resumeMatches?.[0]?.overall_score} />
                                </TableCell>
                                <TableCell className="items-center text-center">
                                    <MatchScoreCircle score={item.resumeMatches?.[0]?.skills_match} />
                                </TableCell>
                                <TableCell className="items-center text-center">
                                    <MatchScoreCircle score={item.resumeMatches?.[0]?.experience_alignment} />
                                </TableCell>
                                <TableCell className="items-center text-center">
                                    <MatchScoreCircle score={item.resumeMatches?.[0]?.education_fit} />
                                </TableCell>
                                <TableCell className="items-center text-center">
                                    <ResumeMatchDialog
                                        resumeMatch={item.resumeMatches?.[0]}
                                        applicantName=""
                                    />
                                </TableCell>
                                <TableCell className="items-center text-center">
                                    {item.isShortlisted ? (
                                        <span className="text-green-500">Yes</span>
                                    ) : (
                                        <span className="text-red-500">No</span>
                                    )}
                                </TableCell>
                                <TableCell className="text-right">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="cursor-pointer">
                                        <MoreHorizontal />
                                    </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-auto"> {/* Adjusted width to auto */}
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem
                                        className="cursor-pointer flex items-center"
                                        onSelect={() => openJobDetailsDialog(item.id)} // Use onSelect to trigger action
                                    >
                                        <EyeIcon className="mr-2 h-4 w-4" /> {/* Ensured icon size */}
                                        <span>Details</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild className="cursor-pointer">
                                        <CopyLinkMenuItem
                                        jobUrl={`${process.env.NEXT_PUBLIC_APP_URL}/public/job/${item.id}`}
                                        />
                                    </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                </TableCell>
                            </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* Dialog for Job Details, structured like in JobCard.tsx for explicit centering */}
            {selectedJobIdForDetails && (
                <ShadDialog open={isJobDetailsDialogOpen} onOpenChange={setIsJobDetailsDialogOpen}>
                    <ShadDialogPortal>
                        <ShadDialogOverlay className="fixed inset-0 bg-black/50 dark:bg-black/80 z-[9999]" />
                        <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4"> {/* Centering wrapper */}
                            {/* Content Box */}
                            <div
                                className="relative bg-background border shadow-lg rounded-lg 
                                           sm:max-w-[900px] w-[90vw] h-[90vh] 
                                           p-0 flex flex-col overflow-hidden
                                           data-[state=open]:animate-in data-[state=closed]:animate-out 
                                           data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 
                                           data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95"
                            >
                                {isJobDetailsDialogOpen && <JobDetails jobId={selectedJobIdForDetails} />}
                                <ShadDialogClose asChild>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="absolute right-3 top-3 h-7 w-7 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-20 cursor-pointer"
                                    >
                                        <X className="h-4 w-4" />
                                        <span className="sr-only">Close</span>
                                    </Button>
                                </ShadDialogClose>
                            </div>
                        </div>
                    </ShadDialogPortal>
                </ShadDialog>
            )}
        </>
    );
}
