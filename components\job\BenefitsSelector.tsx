import { ControllerRenderProps } from "react-hook-form";
import { Badge } from "../ui/badge";
import { benefits } from "./ListOfBenefits";

interface BenefitsSelectorProps {
  field: Pick<ControllerRenderProps, "value" | "onChange" | "onBlur" | "ref">;
}

export function BenefitsSelector({ field }: BenefitsSelectorProps) {
  function toggleBenefit(benefitId: string) {
    const currentBenefits = field.value || [];

    const newBenefits = currentBenefits.includes(benefitId)
      ? currentBenefits.filter((id: string) => id !== benefitId)
      : [...currentBenefits, benefitId];

    field.onChange(newBenefits);
  }
  return (
    <div className="flex flex-wrap gap-3">
      <div className="flex flex-wrap gap-2 max-h-[180px] overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent hover:scrollbar-thumb-muted-foreground/50 p-1">
        {benefits.map((benefit) => {
          const isSelected = (field.value || []).includes(benefit.id);

          return (
            <Badge
              key={benefit.id}
              variant={isSelected ? "default" : "outline"}
              onClick={() => toggleBenefit(benefit.id)}
              className="cursor-pointer transition-all hover:scale-105 active:scale-95 text-sm px-4 py-1.5 rounded-full shrink-0"
            >
              <span className="flex items-center gap-2">
                {benefit.icon}
                {benefit.label}
              </span>
            </Badge>
          );
        })}
      </div>

      <div className="mt-4 text-sm text-muted-foreground">
        Selected benefits:
        <div className="flex flex-wrap gap-2">
          {field.value?.map((benefitId: string) => {
            const benefit = benefits.find((b) => b.id === benefitId);
            return benefit ? (
              <Badge key={benefit.id}>{benefit.label}</Badge>
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
}
