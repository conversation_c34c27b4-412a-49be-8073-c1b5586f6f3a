import { AppFeatures } from "@/components/main/AppFeatures";
import Business from "@/components/main/Business";
import CTA from "@/components/main/CTA";
import Hero from "@/components/main/Hero";
import { Solution } from "@/components/main/Solution";
import styles from "@/styles/style";


export default async function PublicPage() {

  return (
    <>        
        <div className={`${styles.flexCenter} pt-10`}>
            <Hero />
        </div>    
        <div className={`${styles.paddingX} ${styles.flexStart}`}>
          <div className={`${styles.boxWidth}`}>
            <Solution />
            <AppFeatures />
            <Business />
            <CTA />
          </div>
        </div>
    </>
  );
}
