"use client";

import { Dialog, DialogOverlay, Dialog<PERSON>ortal, DialogTrigger } from "@/components/ui/dialog";
import { DialogClose } from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button";
import { Eye, X } from "lucide-react";
import Image from "next/image";

interface FeedbackUser {
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  name: string | null;
  image: string | null;
}

export interface FeedbackItemType {
  id: string;
  message: string[];
  createdAt: Date;
  user: FeedbackUser | null;
  userId?: string | null; // Include userId for anonymous fallback
}

interface FeedbackDetailsDialogProps {
  feedbackItem: FeedbackItemType | null;
}

export function FeedbackDetailsDialog({
  feedbackItem,
}: FeedbackDetailsDialogProps) {
  if (!feedbackItem) {
    return null;
  }

  const userName =
    feedbackItem.user?.name ||
    `${feedbackItem.user?.firstName || ""} ${feedbackItem.user?.lastName || ""}`.trim() ||
    (feedbackItem.userId ? `User ID: ${feedbackItem.userId}` : "Anonymous");

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="text-primary hover:underline cursor-pointer" title="View Feedback">
            <Eye className="w-5 h-5" />
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/70 z-[9999]" />
        <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4">
          <div className="bg-background p-6 rounded-lg shadow-xl relative w-full max-w-2xl max-h-[85vh] overflow-y-auto">
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-3 right-3 text-muted-foreground hover:text-foreground cursor-pointer"
                title="Close"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button>
            </DialogClose>
            <h2 className="text-xl font-semibold mb-4">Feedback Details</h2>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <Image
                  src={feedbackItem.user?.image || "/icons/profile.png"}
                  alt={userName}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
                <div>
                  <p>
                    <strong>User:</strong> {userName}
                  </p>
                  {feedbackItem.user?.email && (
                    <p>
                      <strong>Email:</strong> {feedbackItem.user.email}
                    </p>
                  )}
                </div>
              </div>
              <p>
                <strong>Date:</strong>{" "}
                {new Date(feedbackItem.createdAt).toLocaleString()}
              </p>
              <div>
                <p className="font-semibold mb-1">Message:</p>
                <div className="bg-muted p-3 rounded-md whitespace-pre-wrap">
                  {feedbackItem.message.join("\n")}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
