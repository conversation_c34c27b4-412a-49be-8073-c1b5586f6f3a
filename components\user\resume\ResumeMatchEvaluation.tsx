import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>le, <PERSON>alogTrigger, DialogPortal, DialogOverlay, DialogClose, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { EyeIcon, X } from "lucide-react";
import { normalizeHeaders } from "@/utils/stringHelpters";

// Helper function to determine color based on score
const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-emerald-500";
    if (score >= 80) return "bg-green-500";
    if (score >= 70) return "bg-yellow-500";
    if (score >= 60) return "bg-orange-500";
    return "bg-red-500";
};

// Score circle component
const ScoreCircle = ({
  score,
  label,
}: {
  score: number | null | undefined;
  label: string;
}) => {
  // Default to 0 if score is undefined or null
  const displayScore = score ?? 0;

  return (
    <div className="flex flex-col items-center">
      <div
        className={`${getScoreColor(displayScore)} text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-sm`}
      >
        {displayScore}%
      </div>
      <span className="text-xs mt-1 text-gray-600">{label}</span>
    </div>
  );
};

// Helper function to parse string arrays
const parseStringArray = (data: string | null): string[] => {
  if (!data) return [];
  
  try {
    // Try to parse as JSON
    const parsed = JSON.parse(data);
    if (Array.isArray(parsed)) return parsed;
    return [];
  } catch (e) {
    // If it's not valid JSON, check if it looks like an array string
    if (data.startsWith('[') && data.endsWith(']')) {
      // Simple string parsing for array-like strings
      return data
        .slice(1, -1)
        .split(',')
        .map(item => item.trim().replace(/"/g, '').replace(/'/g, ''));
    }
    // Return as single item if it's just a string
    return [data];
  }
};

type ResumeMatch = {
  id: string;
  overall_score: number | null;
  skills_match: number | null;
  experience_alignment: number | null;
  education_fit: number | null;
  match_analysis: string | null;
  candidate_strengths: string | null;
  matching_skills: string | null;
  missing_requirements: string | null;
  experience_relevance: string | null;
};

export function ResumeMatchEvaluation({ 
  resumeMatch, 
  applicantName 
}: { 
  resumeMatch: ResumeMatch | undefined; 
  applicantName: string;
}) {
  if (!resumeMatch) {
    return (
      <div className="p-4">
        <p className="text-sm text-red-500">
          No match evaluation available for this applicant.
        </p>
      </div>
    );
  }

  // Parse potential array fields
  const matchingSkills = parseStringArray(resumeMatch.matching_skills);
  const missingRequirements = parseStringArray(resumeMatch.missing_requirements);
  const candidateStrengths = parseStringArray(resumeMatch.candidate_strengths);

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
        <div>
            <h3 className="text-lg font-semibold">
                Job Match Evaluation
            </h3> 
          <em className="text-sm text-gray-500">AI matching algorithm used industry best practices to evaluate how well this candidate matches the job requirements.</em>
        </div>
      </div>

      {/* Scores */}
      <div className="flex items-center justify-between gap-2 mt-4 mb-4 pl-10 pr-10">
        <ScoreCircle score={resumeMatch.overall_score} label="Overall" />
        <ScoreCircle score={resumeMatch.skills_match} label="Skills" />
        <ScoreCircle score={resumeMatch.experience_alignment} label="Experience" />
        <ScoreCircle score={resumeMatch.education_fit} label="Education" />
      </div>

      <Separator className="my-2" />

      {/* Match Analysis */}      
      <div className="mb-0">
        <div>
            <h4 className="font-medium text-sm mb-2">Match Analysis</h4>
            <ScrollArea className="h-[120px] pr-4">
                <p className="text-sm text-gray-700 whitespace-pre-line">
                {resumeMatch.match_analysis}
                </p>
            </ScrollArea>
        </div>     
        <Separator className="my-2" />
        <div>
            {/* Missing Requirements */}   
            <h4 className="font-medium text-sm mb-2">Missing Requirements</h4>
            <ScrollArea className="h-[auto] h-max-[100px] pb-2 pr-4">
            <ul className="list-disc pl-5 text-sm text-gray-700">
                {missingRequirements.map((req, index) => (
                <li key={`req-${index}`}>{req}</li>
                ))}
            </ul>
            </ScrollArea>
        </div>
     </div>

      <Separator className="my-2" />

      {/* Additional Match Details */}      
      <div className="grid grid-cols-3 grid-rows-1 gap-4 pb-4">
        <div>
            {/* Candidate Strengths */} 
            <h4 className="font-medium text-sm mb-2">Candidate Strengths</h4>
            <ScrollArea className="h-[auto] h-max-[100px] pb-2 pr-4">
            <ul className="list-disc pl-5 text-sm text-gray-700">
                {candidateStrengths.map((strength, index) => (
                <li key={`strength-${index}`}>{strength}</li>
                ))}
            </ul>
            </ScrollArea>
        </div>
        <div>         
            {/* Matching Skills */}   
            <h4 className="font-medium text-sm mb-2">Matching Skills</h4>
            <ScrollArea className="h-[auto] h-max-[100px] pb-2 pr-4">
            <ul className="list-disc pl-5 text-sm text-gray-700">
                {matchingSkills.map((skill, index) => (
                <li key={`skill-${index}`}>{skill}</li>
                ))}
            </ul>
            </ScrollArea> 
        </div>
        <div>
            {/* Experience Relevance */}  
            <h4 className="font-medium text-sm mb-2">Experience Relevance</h4>
            <ScrollArea className="h-[auto] h-max-[100px] pb-2 pr-4">
            <p className="text-sm text-gray-700 whitespace-pre-line">
                {resumeMatch.experience_relevance}
            </p>
            </ScrollArea>
        </div>
    </div>     
        <div className="mb-1 border-2 p-2 rounded-lg">
            <p className="text-sm text-gray-700 whitespace-pre-line">
                This evaluation is AI generated and may not be 100% accurate. Please review the resume and job description before making a decision.
            </p>
        </div>
        &nbsp;
    </div>
  );
}

export function ResumeMatchDialog({ 
  resumeMatch, 
  applicantName 
}: { 
  resumeMatch: ResumeMatch | undefined; 
  applicantName: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="text-primary hover:underline cursor-pointer" title="View Evaluation">
            <EyeIcon className="w-5 h-5" />
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[600px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6">
              <DialogTitle className="text-lg font-semibold">{normalizeHeaders(applicantName)}</DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 cursor-pointer">
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
            <div className="overflow-y-auto pl-6 pr-6">
              <ResumeMatchEvaluation resumeMatch={resumeMatch} applicantName={applicantName} />
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}






