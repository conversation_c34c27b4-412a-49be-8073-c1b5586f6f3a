import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { GetCompany } from "@/actions/company/getCompany";
import { redirect } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { CreateJobFormGeneric } from "@/components/job/CreateJobFormGeneric";

type SearchParams = {
  searchParams: Promise<{
    id?: string;
  }>;
};

export default async function AdminJobAddPage({ searchParams }: SearchParams) {
  const params = await searchParams;

  if (!params.id) {
    redirect("/admin/company");
  }

  const company = await GetCompany(params.id);

  if (!company) {
    redirect("/admin/company");
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/admin/company">
              <ArrowLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Add New Job</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-4">
            {company.logo && (
              <Image
                src={company.logo}
                alt={company.name}
                className="h-10 w-10 rounded-md object-contain"
                width={200}
                height={200}
              />
            )}
            <div>
              <h2 className="text-xl">{company.name}</h2>
              <p className="text-sm text-muted-foreground">
                {company.location}
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <Separator />
        <CardContent>
          <CreateJobFormGeneric userRole="ADMIN" />
        </CardContent>
      </Card>
    </div>
  );
}
