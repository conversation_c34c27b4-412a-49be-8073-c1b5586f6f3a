// Routes accessible to the public
export const publicRoutes = [
    "/",
    "/public",
    "/public/job",
    "/public/job/list",
    "/public/privacy",
    "/auth/verification"
];

// Routes for login and registration and will redirect users to the dashboard
export const authRoutes = [
    "/auth/signin",
    "/auth/signup",
    "/auth/error",
    "/auth/reset",
    "/auth/resetpassword"
];

export const signinRoute = [
    "/auth/signin"
];

export const apiRoutes = [
    "/api",
    "/api/auth",
    "/api/auth/session",
    "/api/user",
    "/api/company",
    "/api/job",
    "/api/arcjet",    
    "/api/location",
    "/api/uploadthing",
    "/api/inngest"
];

export const userRoute =  "/home/<USER>";
export const companyRoute = "/home/<USER>";
export const adminRoute = "/home/<USER>";
export const recruiterRoute = "/home/<USER>";
export const tenantRoute = "/home/<USER>";


// Route prefix for API authentication
export const apiAuthPrefix = "/api/auth"

// Default redirect path after logging in
export const DEFAULT_LOGIN_REDIRECT = "/public"

// Onboarding path
export const ONBOARDING_PATH = "/home/<USER>";

// Defines top-level path segments that are considered private or reserved
// and should not be treated as dynamic public user profiles.
export const PRIVATE_ROUTES = [
    'preview', 
    'api', 
    'upload', 
    'pdf', 
    'auth',    // Auth specific pages like /auth/error, /auth/reset
    'admin',   // Potential /admin top-level route
    'company', // Potential /company top-level route
    'user',    // Potential /user top-level route
    'onboarding', // Potential /onboarding top-level route
    'home',    // Prefix for all dashboards like /home/<USER>/home/<USER>
    'public'   // The /public segment itself, to distinguish from /public/*
];