import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'k7hs9dg5ho.ufs.sh',
        port: '',
        search: '',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        search: '',
      },
    ],
    unoptimized: true,
  },
  compiler: {
    // Remove all console.* calls in production builds
    // removeConsole: process.env.NODE_ENV === "production",
    // Or, to remove all console.* except console.error:
    // removeConsole: process.env.NODE_ENV === "production" ? { exclude: ['error'] } : false,
  },
};

export default nextConfig;
