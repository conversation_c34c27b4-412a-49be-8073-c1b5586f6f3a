export function extractResumeContactInfo(text: string): Record<string, string> {
  const contact: Record<string, string> = {
    email: "",
    phone: "",
    name: ""
  };

  const emailMatch = text.match(/\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/i);
  const phoneMatch = text.match(/(\(?\+?\d{1,3}\)?[ -]?)?(\(?\d{2,4}\)?[ -]?)?\d{3}[ -]?\d{4}/);
  const nameMatch = text.split('\n').find(l => /^[A-Z][a-z]+\s[A-Z][a-z]+$/.test(l.trim()));

  if (emailMatch) contact.email = emailMatch[0];
  if (phoneMatch) contact.phone = phoneMatch[0];
  if (nameMatch) contact.name = nameMatch.trim();

  return contact;
}
