import { get<PERSON><PERSON><PERSON><PERSON> } from "@/actions/job/getJob";
import { CopyLinkMenuItem } from "@/components/general/CopyLink";
import { EmptyState } from "@/components/general/EmptyState";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { auth } from "@/lib/auth/auth";
import { UserRole } from "@prisma/client";
import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@radix-ui/react-dropdown-menu";
import { FilePlusIcon, PenBoxIcon, XCircleIcon } from "lucide-react";
import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { MainPagination } from "@/components/general/MainPagination";

export default async function AdminJobsPage({ searchParams, }: { searchParams: Promise<{ page?: string }>; }) {
  const session = await auth();
  let isAdmin = false;

  if (session?.user?.role === UserRole.SUPERADMIN) {
    isAdmin = true;
  }

  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50; // Or any other page size you prefer

  const list = await getJobList(session?.user?.id as string, isAdmin, currentPage, pageSize);

  // Assuming getJobList will be updated to return totalItems or you fetch it separately
  // For now, let's assume `list` might contain all items and we paginate client-side,
  // or `list.totalItems` is returned by `getJobList`
  const totalItems = list?.totalItems || list?.data?.length || 0; // Adjust based on getJobList's return
  const totalPages = Math.ceil(totalItems / pageSize);

  return (
    <>
      {list?.data?.length === 0 ? (
        <EmptyState
          title="No job posts found"
          description="You don't have job posts yet."
          buttonText="Post a job"
          href="/job"
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Job Lists</CardTitle>
            <CardDescription>
              Manage job listings and applications here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead></TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Job Title</TableHead>
                  <TableHead>Applied</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {list?.data?.map((job) => ( // If getJobList handles pagination, this is already the paginated list
                  <TableRow key={job.id}>
                    <TableCell>
                      <Image
                        src={job.company.logo || ""}
                        alt={job.company.name || ""}
                        width={32}
                        height={32}
                      />
                    </TableCell>
                    <TableCell>{job.company.name}</TableCell>
                    <TableCell>{job.jobTitle}</TableCell>
                    <TableCell>{job._count.applications}</TableCell>
                    <TableCell>
                      {job.status.charAt(0).toUpperCase() +
                        job.status.slice(1).toLowerCase()}
                    </TableCell>
                    <TableCell>{job.createdAt.toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/job/add?id=${job.company.id}`}> 
                              <FilePlusIcon className="mr-2" />
                              Add Job
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/job/${job.id}/edit`}>
                              <PenBoxIcon className="mr-2" />
                              Edit Job
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <CopyLinkMenuItem
                              jobUrl={`${process.env.NEXT_PUBLIC_APP_URL}/public/job/${job.id}`}
                            />
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/job/${job.id}/delete`}>
                              <XCircleIcon className="mr-2" />
                              Delete
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <MainPagination totalPages={totalPages} currentPage={currentPage} />
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
