"use client";

import { z } from "zod";
import { signIn } from "next-auth/react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { FcGoogle } from "react-icons/fc";
import { FaGithub, FaLinkedin } from "react-icons/fa";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoginSchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useState, useTransition } from "react";
import { signin } from "@/actions/user/signin";
import { ErrorMessage } from "../info/ErrorMessage";
import { SuccessMessage } from "../info/SuccessMessage";
import { CardWrapper } from "../info/CardWrapper";

export function SignInForm() {
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [isPending, startTransition] = useTransition();

  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = (values: z.infer<typeof LoginSchema>) => {
    setError("");
    setSuccess("");

    startTransition(() => {
      signin(values).then((data) => {
        setError(data!.error);
        // setSuccess(data!.success);
      });
    });
  };

  const onSocialClick = (provider: "google" | "github" | "linkedin") => {
    signIn(provider, { redirectTo: "/public" });
  };

  return (
    <div>
      <CardWrapper
        captionLabel="Sign In"
        headerLabel="Welcome back!"
        backButtonLabel="Don't have an account? Sign Up"
        backButtonHref="/auth/signup"
        customClassName="w-full h-full md:w-[450px] shadow-md border-1"
      >
        <div className="mt-6 mb-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="<EMAIL>"
                          type="email"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="******"
                          type="password"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <ErrorMessage message={error} />
              <SuccessMessage message={success} />
              <Button
                type="submit"
                disabled={isPending}
                size="lg"
                className="w-full cursor-pointer"
              >
                Sign In
              </Button>
            </form>
          </Form>
        </div>
        <div className="text-medium items-center justify-center text-center">
          Or sign in using these services:
        </div>
        <div className="flex items-center justify-center gap-4 p-2">
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
              onSocialClick("google");
            }}
          >
            <FcGoogle className="mr-2 size-5" />
          </Button>
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
                onSocialClick("linkedin");
            }}
          >
            <FaLinkedin  className="mr-2 size-5 text-blue-500" />
          </Button>
          <Button
            disabled={isPending}
            variant="ghost"
            size="lg"
            className="cursor-pointer"
            onClick={() => {
              onSocialClick("github");
            }}
          >
            <FaGithub className="mr-2 size-5" />
          </Button>
        </div>
      </CardWrapper>
    </div>
  );
}
