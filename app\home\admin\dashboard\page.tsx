import { auth } from "@/lib/auth/auth";
import Link from "next/link";
import { Users, Building2, CreditCard } from "lucide-react"; // Import relevant icons

// Define a type for admin dashboard sections
interface AdminDashboardCardProps {
  title: string;
  description: string;
  link: string;
  linkText: string;
  icon?: React.ReactNode;
}

// Simple card component similar to the company dashboard
const AdminDashboardCard = ({ title, description, link, linkText, icon }: AdminDashboardCardProps) => (
  <div className="bg-white shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 flex flex-col">
    <div className="flex items-center mb-3">
      {icon && <span className="mr-3 text-blue-600 text-2xl">{icon}</span>}
      <h2 className="text-xl font-semibold text-gray-700">{title}</h2>
    </div>
    <p className="text-gray-600 mb-4 flex-grow">{description}</p>
    <Link href={link} className="mt-auto text-blue-600 hover:text-blue-800 font-medium hover:underline self-start">
      {linkText} &rarr;
    </Link>
  </div>
);

export default async function AdminCompanyPage() {
  const session = await auth();
  const user = session?.user;

  // Define the sections/cards for the super admin dashboard
  const adminSections: AdminDashboardCardProps[] = [
    {
      title: "Manage Job Seekers",
      description: "View, edit, or manage user accounts for job seekers.",
      link: "/home/<USER>/user/list", // Assuming this is the route for user management
      linkText: "View All Job Seekers",
      icon: <Users className="h-6 w-6" />
    },
    {
      title: "Manage Recruiters & Companies",
      description: "Oversee company accounts and recruiter users.",
      link: "/home/<USER>/recruiter/list", // Assuming this is the route for recruiter/company management
      linkText: "View All Companies",
      icon: <Building2 className="h-6 w-6" />
    },
    {
      title: "Manage Subscriptions",
      description: "Monitor and manage platform subscriptions and plans.",
      link: "/home/<USER>/subscription/list", // Assuming this is the route for subscription management
      linkText: "View Subscriptions",
      icon: <CreditCard className="h-6 w-6" />
    },
    // Add more sections as needed, e.g., Platform Settings, Analytics, etc.
  ];

  return (
    <div className="container mx-auto p-4 md:p-4 bg-gray-50 min-h-screen"> {/* Use similar container styling */}
      <header className="mb-8 md:mb-12">
        <h1 className="text-3xl md:text-3xl font-bold text-gray-800">
          Super Admin Dashboard
        </h1>
        {user && (
          <p className="text-md text-gray-600 mt-2">
            Welcome, {user.name || user.email || "Super Admin"}
          </p>
        )}
        <p className="text-md text-gray-600 mt-1">
          Manage the entire platform from here.
        </p>
      </header>

      {/* Placeholder for overall platform statistics */}
      {/* 
        <section className="mt-10 md:mt-16 mb-8 p-6 bg-white shadow-md rounded-lg">
          <h2 className="text-2xl font-semibold text-gray-700 mb-6">Platform Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
             <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-3xl font-bold text-blue-600">XXX</p>
              <p className="text-sm text-gray-600">Total Users</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-3xl font-bold text-green-600">YYY</p>
              <p className="text-sm text-gray-600">Total Companies</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <p className="text-3xl font-bold text-purple-600">ZZZ</p>
              <p className="text-sm text-gray-600">Active Subscriptions</p>
            </div>
          </div>
        </section>
      */}

      {/* Management Sections */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-10 mt-10 md:mb-16">
        {adminSections.map((section) => (
          <AdminDashboardCard
            key={section.title}
            title={section.title}
            description={section.description}
            link={section.link}
            linkText={section.linkText}
            icon={section.icon}
          />
        ))}
      </div>
    </div>
  );
}