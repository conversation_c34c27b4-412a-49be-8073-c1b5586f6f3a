"use client";

import { EmptyState } from "@/components/general/EmptyState";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { XCircleIcon } from "lucide-react";
import Image from "next/image";
import { MainPagination } from "@/components/general/MainPagination";
import { DelteUserFeedback, GetUserFeedback } from "@/actions/user/userFeedback";
import { FeedbackDetailsDialog, FeedbackItemType } from "@/components/general/FeedbackDetailsDialog";
import { toast } from "sonner";
import { useCallback, useEffect, useState } from "react";
import { useSearchParams } from 'next/navigation'
import { AlertDialogDelete } from "@/components/general/AlertDialogDelete";

export default function FeedbackPage() {
    const [pending, setPending] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [deleting, setDeleting] = useState(false);
    const [paginatedData, setPaginatedData] = useState<FeedbackItemType[]>([]);
    const [totalPages, setTotalPages] = useState(0);


    const searchParams = useSearchParams()
    const search = searchParams.get('page')
    const currentPage = Number(search) || 1;
    const pageSize = 30;
    
    const fetchFeedbackData = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await GetUserFeedback(currentPage, pageSize);
      if (result) {
        setPaginatedData(result.data || []);
        setTotalPages(Math.ceil((result.totalItems || 0) / pageSize));
      } else {
        setPaginatedData([]);
        setTotalPages(0);
        toast.error("Failed to fetch feedback data.");
      }
    } catch (error) {
      toast.error("An error occurred while fetching feedback.");
      setPaginatedData([]);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize]);

  useEffect(() => {
    fetchFeedbackData();
  }, [fetchFeedbackData]);

  const onDelete = async (id: string) => {
        try {            
            setPending(true);
            setDeleting(true);
            const result = await DelteUserFeedback(id);
            if (result?.success) {
                toast.success(result.success);
                fetchFeedbackData(); 
            } else {
                toast.error(result?.error || "Failed to delete feedback.");
            }
        } catch (error) {
            toast.error("An unexpected error occurred.");            
        } finally {
            setPending(false);
            setDeleting(false);
        }
    }

if (isLoading && paginatedData.length === 0) {
    return <div>Loading feedback...</div>; 
  }
    
  return (
    <div className="flex flex-col w-full justify-start items-start">
      {!paginatedData || paginatedData.length === 0 ? (
        <div className="flex flex-col w-full gap-4">
            <h1 className="text-2xl font-semibold">User Feedback</h1>
            <EmptyState
                    title="No feedback found"
                    description="There are no user feedback submissions yet." 
                    buttonText={""} 
                    href={""}
            />
        </div>
      ) : (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>
              <div className="flex items-center w-full justify-between gap-2">
                <h1 className="text-2xl font-semibold">User Feedback</h1>
              </div>
            </CardTitle>
            <CardDescription>View user feedback submissions.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead style={{ width: "11%" }}>Date</TableHead>
                    <TableHead style={{ width: "25%" }}>User</TableHead>
                    <TableHead style={{ width: "20%" }}>Email</TableHead>
                    <TableHead style={{ width: "30%" }}>Message</TableHead>
                    <TableHead style={{ width: "2%" }}>View</TableHead>
                    <TableHead style={{ width: "2%" }}>Del</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData?.map((feedbackItem) => (
                    <TableRow key={feedbackItem.id}>
                      <TableCell>
                        {new Date(feedbackItem.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="flex items-center gap-2">
                        <Image
                          src={feedbackItem.user?.image || "/icons/profile.png"}
                          alt={feedbackItem.user?.name || feedbackItem.user?.firstName || "User"}
                          width={28}
                          height={28}
                          className="rounded-full"
                        />
                        <span>
                          {feedbackItem.user?.name || 
                           `${feedbackItem.user?.firstName || ""} ${feedbackItem.user?.lastName || ""}`.trim() || 
                           (feedbackItem.userId ? `User ID: ${feedbackItem.userId}` : "Anonymous")}
                        </span>
                      </TableCell>
                      <TableCell>{feedbackItem.user?.email || "N/A"}</TableCell>
                      <TableCell>
                        <div className="max-w-[400px] whitespace-normal line-clamp-3 leading-tight overflow-hidden" title={feedbackItem.message.join("\n")}>
                          {feedbackItem.message.join(" ")}
                        </div>
                      </TableCell>
                      <TableCell><FeedbackDetailsDialog feedbackItem={feedbackItem} /></TableCell>
                      <TableCell>
                            <AlertDialogDelete id={feedbackItem.id} title="Delete Feedback" description="This will delete the user feedback." onDelete={onDelete} />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="flex justify-center mt-4">
                <MainPagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
