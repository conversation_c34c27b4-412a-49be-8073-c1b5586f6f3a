"use client";

import React, { ReactNode } from "react";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle } from "lucide-react";

interface InfoTooltipProps {
  content: ReactNode;
  icon?: ReactNode;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  iconClassName?: string;
  contentClassName?: string;
  delayDuration?: number;
}

export function InfoTooltip({
  content,
  icon = <HelpCircle className="h-4 w-4" />,
  side = "top",
  align = "center",
  iconClassName = "text-muted-foreground cursor-help",
  contentClassName = "text-xs max-w-xs",
  delayDuration = 300,
}: InfoTooltipProps) {
  return (
    <TooltipProvider delayDuration={delayDuration}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={`inline-flex ${iconClassName}`}>
            {icon}
          </span>
        </TooltipTrigger>
        <TooltipContent side={side} align={align} className={contentClassName}>
          {content}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Variant with a question mark in a circle
export function QuestionTooltip({
  content,
  side = "top",
  align = "center",
  contentClassName = "text-xs max-w-xs",
  delayDuration = 300,
}: Omit<InfoTooltipProps, "icon" | "iconClassName">) {
  return (
    <InfoTooltip
      content={content}
      icon={
        <span className="inline-flex items-center justify-center w-4 h-4 rounded-full border border-muted-foreground text-muted-foreground text-xs">
          ?
        </span>
      }
      side={side}
      align={align}
      iconClassName="cursor-help ml-1"
      contentClassName={contentClassName}
      delayDuration={delayDuration}
    />
  );
}