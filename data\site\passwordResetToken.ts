"use server";

import { prisma } from "@/lib/prisma/prismaClient";

export const GetPasswordResetTokenByToken = async (token: string) => {
    
    try {
        const passwordToken = await prisma.userPasswordResetToken.findUnique({where: {token}});
        return passwordToken;
    } catch {
        return null;
    }
}


export const GetPasswordResetTokenByEmail = async (hashedEmail: string) => {
    
    try {
        const passwordToken = await prisma.userPasswordResetToken.findFirst({
            where: {
                emailHash: hashedEmail
            }
        });
        return passwordToken;
    } catch {
        return null;
    }
}
