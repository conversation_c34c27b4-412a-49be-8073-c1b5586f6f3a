"use server";

import {
  checkResumeUsernameData,
  createResumeData,
  GetAllResumeRawTextData,
  getOriginalResumeData,
  getResumeData,
  getUserOriginalResumeData,
  getUserRawResumeData,
  getUserResumeData,
  getUserResumeEvaluationData,
  getUserResumeFileData,
  getUserResumeIdData,
  getUserResumeUsernameData,
  toggleShortlistResumeData,
  updateResumeData,
  updateResumeStatusData,
  updateResumeUsernameData,
} from "@/data/user/resume";
import { ResumeEvaluationSchema, ResumeSchema } from "@/data/zod/resumeZod";
import { Resume, ResumeEvaluation } from "@/lib/resume/resumeActions";
import { deleteResumeData } from "@/data/user/resume";
import { PublishStatus } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { auth } from "@/lib/auth/auth";
import { CandidateResume } from "@/types/customTypes";

export const GetResume = async (id: string): Promise<Resume> => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
  try {
    const resume = await getResumeData(id);

    if (resume) {
      return ResumeSchema.parse(resume);
    } else {
      // Create a new resume record if none exists
      const newResume = {
        id: uuidv4(),
        picture: null,
        status: PublishStatus.DRAFT,
        fileContent: null,
        resumeData: null,
      };

      // You might want to actually save this to the database
      return ResumeSchema.parse(newResume);
    }
  } catch (error) {
    console.error("Error in GetUserResume:", error);
    throw error;
  }
};

export const GetUserResume = async (userId: string): Promise<Resume> => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const resume = await getUserResumeData(userId);

    if (resume) {
        console.log({GetUserResumeAction: resume});
      return ResumeSchema.parse(resume);
    } else {
      // Create a new resume record if none exists
      const newResume = {
        id: uuidv4(),
        picture: null,
        status: PublishStatus.DRAFT,
        fileContent: null,
        resumeData: null,
      };

      // You might want to actually save this to the database
      return ResumeSchema.parse(newResume);
    }
  } catch (error) {
    console.error("Error in GetUserResume:", error);
    throw error;
  }
};

export const GetUserResumeId = async (userId: string) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const resumeId = await getUserResumeIdData(userId);

        if (resumeId) {
        return resumeId;
        }
      return null
  } catch (error) {
    console.error("Error in GetUserResumeId:", error);
    throw error;
  }
};

export const GetUserResumeUsername = async (userId: string) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const username = await getUserResumeUsernameData(userId);

        if (username) {
            return username;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const GetOriginalResume = async (fileId: string) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const resume = await getOriginalResumeData(fileId);

        if (resume) {
            return resume;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const GetUserOriginalResume = async (resumeId: string) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
        const resume = await getUserOriginalResumeData(resumeId);

        if (resume) {
            return resume;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const UpdateResumeStatus = async (
  resumeId: string,
  status: PublishStatus
) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();
    
    try {
    const result = await updateResumeStatusData(resumeId, status);

        if (result) {
            return result;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const UpdateResumeUsername = async (
  resumeId: string,
  username: string
) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

    try {
        const result = await updateResumeUsernameData(resumeId, username);

        if (result) {
            return result;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const CheckResumeUsername = async (
  username: string
): Promise<boolean> => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const available = await checkResumeUsernameData(username);
    if (available === true || available === false) {
      return available;
    } else {
      return false;
    }
  } catch (error) {
    throw error;
  }
};

export const GetUserResumeEvaluation = async (
  userId?: string,
  resumeId?: string
): Promise<ResumeEvaluation> => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    const evaluation = await getUserResumeEvaluationData(userId, resumeId);

    if (evaluation) {
      // The data is already in the correct format, just parse it
      return ResumeEvaluationSchema.parse(evaluation);
    } else {
      // Create a new empty evaluation record if none exists
      const newEvaluation = {
        evaluation: {
          scores: null,
          assessment: null,
          evaluation_details: null,
        },
      };

      return ResumeEvaluationSchema.parse(newEvaluation);
    }
  } catch (error) {
    console.error("Error in GetUserResumeEvaluation:", error);
    throw error;
  }
};

export const GetUserRawResume = async (resumeIds: string[]) => {

    // Arcjet protection
    await ensureServerActionProtection();

    try {
        const resumes = await getUserRawResumeData(resumeIds);

        if (resumes) {
            return resumes;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export async function ToggleShortlistResume(
  jobId: string,
  resumeId: string,
  isShortlisted: boolean
) {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  console.log({ ToggleShortlistResume: jobId, resumeId, isShortlisted });
  try {
    const data = await toggleShortlistResumeData(
      jobId,
      resumeId,
      isShortlisted
    );
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Error in getJobApplicants:", error);
    return [];
  }
}

export const GetUserResumeFile = async (processedResumeFileIds: string[]) => {

    // Arcjet protection
    await ensureServerActionProtection();

    try {
        const resumes = await getUserResumeFileData(processedResumeFileIds);

        if (resumes) {
            return resumes;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const GetAllResumeRawText = async () => {

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    // Fetch all resumes
    const resumes = await GetAllResumeRawTextData();

    if (!resumes) {
      return {
        error: "Error! Failed to fetch resume data.",
        data: null,
      };
    }

    return {
      success: "Success! Resume data retrieved.",
      data: resumes,
    };
  } catch (error) {
    console.error("Error fetching resume raw text:", error);
    return {
      error: `Error fetching resume data: ${error}`,
      data: null,
    };
  }
};

export const DeleteResume = async (id: string) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

  if (!id) {
    return { error: "Missing resume id." };
  }

  try {
    const result = await deleteResumeData(id);

    if (result.success) {
      return {
        success: result.success,
        deleteResult: result.resume,
      };
    } else {
      return {
        error: result.error,
        deleteResult: result.resume,
      };
    }
  } catch (error) {
    console.error(error);
    return {
      error: error,
      deleteResult: null,
    };
  }
};

export const UpdateResume = async (resume: Resume) => {
  const session = await auth();
    
    if (!session?.user?.id) {
        throw new Error("User not authenticated.");
    }

    // Arcjet protection
    await ensureServerActionProtection();

    try {
        const result = await updateResumeData(resume);

        if (result) {
            return result;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

export const CreateResume = async (resume: CandidateResume) => {
    // Arcjet protection
    await ensureServerActionProtection();

    if (!resume) {
        return { error: "Missing resume data." };
    }

    try {
        const result = await createResumeData(resume);

        if (result) {
            return result;
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};