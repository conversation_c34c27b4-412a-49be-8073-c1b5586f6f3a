import { formatStructuredToMarkdown } from '../llm/common/formatStructuredToMarkdown';
import { mergeRefinedIntoStructured } from '../llm/common/mergeRefinedIntoStructured';
import { extractResumeContactInfo } from './extractResumeContactInfo';
import { redactResumePII } from './redactResumePII';
import { refineResumeFlatTextToSections } from './refineResumeFlatTextToSections';

export function prepareResumeForLLM(rawResumeText: string) {
  const extractedContact = extractResumeContactInfo(rawResumeText);
  const structured = refineResumeFlatTextToSections(rawResumeText);

  // Include raw as fallback "General" if no other sections were detected
  if (Object.keys(structured).length === 0) {
    structured.General = rawResumeText;
  }

  const promptMarkdown = formatStructuredToMarkdown(structured);

  return {
    promptMarkdown,
    structured,
    extractedContact
  };
}

