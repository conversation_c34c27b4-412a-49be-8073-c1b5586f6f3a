"use server";

import { z } from "zod";
import { prisma } from "@/lib/prisma/prismaClient";
import { Prisma } from "@prisma/client";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { v4 as uuidv4 } from "uuid";
import { JobNoteSchema } from "../zod/zodSchema";
import { JobDescPost } from "@/types/customTypes";

export const getJobData = async (id: string) => {

  try {
    const jobData = await prisma.jobPost.findUnique({
            where: {
              id: id,
            },
            select: {
              id: true,
              jobTitle: true,
              employmentType: true,
              experienceLevel: true,
              country: true,
              location: true,
              department: true,
              salaryCurrency: true,
              salaryFrom: true,
              salaryTo: true,
              jobDescription: true,
              listingDuration: true,
              interviewType: true,
              localRemoteWork: true,
              overseasRemoteWork: true,
              skills: true,
              languageRequirements: true,
              tags: true,
              companyId: true,
              status: true,
              createdAt: true,
            },
          });
    
      if (!jobData) {
        return null;
      }
      console.log({jobData: jobData})
      return jobData;

  } catch {
    return null;
  }      
}

export const getJobUserData = async (id: string, userId?: string) => {

  try {
    const [jobData, savedJob] = await Promise.all([
    
        await prisma.jobPost.findUnique({
            where: {
              id: id,
              status: "ACTIVE",
            },
            select: {
              id: true,
              jobTitle: true,
              employmentType: true,
              experienceLevel: true,
              location: true,
              country: true,
              salaryCurrency: true,
              salaryFrom: true,
              salaryTo: true,
              jobDescription: true,
              listingDuration: true,
              interviewType: true,
              localRemoteWork: true,
              overseasRemoteWork: true,
              skills: true,
              languageRequirements: true,
              tags: true,
              status: true,
              createdAt: true,
              company: {
                select: {
                  name: true,
                  about: true,
                  logo: true,
                  location: true,
                  address: true,
                  website: true,
                  benefits: true,
                  tags: true,
                  xAccount: true,
                  linkedIn: true,
                  englishUsageRatio: true,
                  foreignerRatio: true,
                },
              },
              applications: {
                where: {
                  userId: userId,
                },
                select: {
                  id: true,
                },
              }
            },
          }),
    
          userId ?
          prisma.savedJobPost.findUnique({
            where: {
              userId_jobPostId: {
                userId: userId,
                jobPostId: id,
              },
            },
            select: {
              id: true,
            },
          }) : null,
    
      ]);
    
      if (!jobData) {
        return null;
      }

      return {jobData, savedJob: !!savedJob};

  } catch {
    return null;
  }      
}

export const getJobListData = async (
  userId: string,
  isAdmin: boolean,
  page: number = 1,
  pageSize: number = 10
) => {
  try {

    const whereClause: Prisma.JobPostWhereInput = isAdmin 
      ? {} // Admins see all jobs (including DELETED if no other status filter is applied here)
      : { // Non-admins
          company: {
            userId: userId,
          },
          status: { not: "DELETED" },
        };

    const [data, totalItems] = await prisma.$transaction([
      prisma.jobPost.findMany({
        where: whereClause,
        select: {
          id: true,
          jobTitle: true,
          jobDescription: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          company: {
            select: {
                id: true,
              name: true,
              logo: true,
              _count: {
                select: {
                  jobPosts: true, // This will count all job posts for this company
                },
              },
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
          resumeMatches: {
            where: {
              overall_score: {
                gte: 0
              }
            }
          }
        },    
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      prisma.jobPost.count({ where: whereClause }),
    ]);

    //   console.log({jobsdata: JSON.stringify(data, null, 2)});

    // Transform the data to include the count of high-scoring matches
    const jobList = data?.map(job => ({
        ...job,
        _count: {
        ...job._count,
        resumeMatches: job.resumeMatches.length
        }
    }));
    return { data: jobList, totalItems };
  } catch {
    return null;
  }
};

export const getFavoriteJobsData = async (
    userId: string,
    page: number = 1,
    pageSize: number = 10 // Default page size
) => {
    try {
        const skip = (page - 1) * pageSize;
        const whereClause = {
            where: {
              userId: userId,
            },
        };
        const [favorites, totalCount] = await Promise.all([
            prisma.savedJobPost.findMany({
            ...whereClause,
            select: {
              jobPost: {
                    select: {
                        id: true,
                        jobTitle: true,
                        employmentType: true,
                        location: true,
                        salaryFrom: true,
                        salaryTo: true,
                        jobDescription: true,
                        createdAt: true,
                        company: {
                            select: {
                            name: true,
                            about: true,
                            logo: true,
                            location: true,
                            website: true,
                            },
                        },
                        resumeMatches: {
                            where: {
                                resume: {
                                    userId: userId
                                }
                            },
                            select: {
                                id: true,
                                overall_score: true,
                                skills_match: true,
                                experience_alignment: true,
                                education_fit: true,
                                match_analysis: true,
                                candidate_strengths: true,
                                matching_skills: true,
                                missing_requirements: true,
                                experience_relevance: true
                            }
                        },
                        shortlists: {
                            where: {
                                resume: {
                                    userId: userId
                                }
                            },
                                select: {
                                id: true
                            }
                        }
                    },                    
                },
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    }
                }
            },
            orderBy: { jobPost: { createdAt: 'desc' } }, // Or by savedAt if you have such a field on SavedJobPost
            take: pageSize,
            skip: skip,
            }),
            prisma.savedJobPost.count(whereClause)
        ]);

        const processedFavorites = favorites.map(favorite=> ({
            ...favorite,
            user: {
                ...favorite.jobPost,
                ...favorite.user,
                firstName: favorite.user.firstName ? safeDecrypt(favorite.user.firstName) : "",
                lastName: favorite.user.lastName ? safeDecrypt(favorite.user.lastName) : "",
                isShortlisted: favorite.jobPost?.shortlists?.length > 0
            }
        }));

        return { favorites: processedFavorites, totalPages: Math.ceil(totalCount / pageSize) };
    } catch (error) {
      console.error("Error fetching favorite jobs:", error);
      return { favorites: [], totalPages: 0, error: "Failed to fetch favorite jobs." };
    }
  };

export const getUserJobApplicationsData = async (
    userId: string,
    page: number = 1,
    pageSize: number = 10 // Default page size, adjust as needed
) => {
    try {
        const skip = (page - 1) * pageSize;

        const whereClause = {
            where: {
              userId: userId,
            },
        };
        const [applications, totalCount] = await Promise.all([
            prisma.appliedJobPost.findMany({
            ...whereClause,
            select: {
              createdAt: true,
              jobPost: {
                    select: {
                        id: true,
                        jobTitle: true,
                        employmentType: true,
                        location: true,
                        salaryFrom: true,
                        salaryTo: true,
                        jobDescription: true,
                        createdAt: true,
                        company: {
                            select: {
                            name: true,
                            about: true,
                            logo: true,
                            location: true,
                            website: true,
                            },
                        },
                        resumeMatches: {
                            where: {
                                resume: {
                                    userId: userId
                                }
                            },
                            select: {
                                id: true,
                                overall_score: true,
                                skills_match: true,
                                experience_alignment: true,
                                education_fit: true,
                                match_analysis: true,
                                candidate_strengths: true,
                                matching_skills: true,
                                missing_requirements: true,
                                experience_relevance: true
                            }
                        },
                        shortlists: {
                            where: {
                                resume: {
                                    userId: userId
                                }
                            },
                                select: {
                                id: true
                            }
                        }
                    },                    
                },
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    }
                }
            },
            orderBy: { createdAt: 'desc' },
            take: pageSize,
            skip: skip,
            }),
            prisma.appliedJobPost.count(whereClause)
        ]);

          const processedApplications = applications.map(application => ({
            ...application,
            user: {
                ...application.jobPost,
                ...application.user,
                firstName: application.user.firstName ? safeDecrypt(application.user.firstName) : "",
                lastName: application.user.lastName ? safeDecrypt(application.user.lastName) : "",
                isShortlisted: application.jobPost?.shortlists?.length > 0
            }
        }));

        return { applications: processedApplications, totalPages: Math.ceil(totalCount / pageSize) };
    } catch (error) {
        console.error("Error fetching job applications:", error);
      return { applications: [], totalPages: 0, error: "Failed to fetch job applications." };
    }
};

export const getUserJobMatchesData = async (
    resumeId: string,
    page: number = 1,
    pageSize: number = 20 // Default page size
) => {
    try {
        const skip = (page - 1) * pageSize;

        const whereClause = {
            where: {
                resumeMatches: {
                    some: {
                        resume: {
                            id: resumeId
                        },
                        overall_score: {
                            gte: 70
                        }
                    }
                }
            }
        };

        const [matches, totalCount] = await Promise.all([
            prisma.jobPost.findMany({
            ...whereClause,
            select: {
                    id: true,
                    jobTitle: true,
                    createdAt: true,
                    company: {
                        select: {
                        name: true,
                        about: true,
                        logo: true,
                        location: true,
                        website: true,
                        },
                    },
                    resumeMatches: {
                        where: {
                            resume: {
                                id: resumeId
                            },
                            overall_score: {
                                gte: 70 // Only include matches with 70% or higher score
                            }
                        },
                        select: {
                            id: true,
                            overall_score: true,
                            skills_match: true,
                            experience_alignment: true,
                            education_fit: true,
                            match_analysis: true,
                            candidate_strengths: true,
                            matching_skills: true,
                            missing_requirements: true,
                            experience_relevance: true
                        }
                    },
                    shortlists: {
                        where: {
                            resume: {
                                id: resumeId
                            }
                        },
                            select: {
                            id: true
                        }
                    }
                },   
                orderBy: {
                    createdAt: 'desc'
                },
                take: pageSize,
                skip: skip,
            }),
            prisma.jobPost.count(whereClause)
        ]);

        // Process matches to include isShortlisted (already in your select, just ensuring structure)
        const processedMatches = matches.map(job => ({
            ...job,
            isShortlisted: job.shortlists.length > 0,
        }));

        return { matches: processedMatches, totalPages: Math.ceil(totalCount / pageSize) };
    } catch (error) {
        console.error("Error fetching job matches:", error);
      return { matches: [], totalPages: 0, error: "Failed to fetch job matches." };
    }
};

export const getCompanyJobMatchesData = async (
    companyId: string,
    page: number = 1,
    pageSize: number = 20, // Default page size
    score: number = 70 // Default score
) => {
    try {
        const skip = (page - 1) * pageSize;

        const whereClause = {
            where: {
                resumeMatches: {
                    some: {
                        jobPost: {
                            companyId: companyId
                        },
                        overall_score: {
                            gte: score
                        }
                    }
                }
            }
        };

        const [matches, totalCount] = await Promise.all([
            prisma.jobPost.findMany({
            ...whereClause,
            select: {
                    id: true,
                    jobTitle: true,
                    createdAt: true,
                    company: {
                        select: {
                        name: true,
                        about: true,
                        logo: true,
                        location: true,
                        website: true,
                        },
                    },
                    resumeMatches: {
                        where: {
                            jobPost: {
                                companyId: companyId
                            },
                            overall_score: {
                                gte: score
                            }
                        },
                        select: {
                            id: true,
                            overall_score: true,
                            skills_match: true,
                            experience_alignment: true,
                            education_fit: true,
                            match_analysis: true,
                            candidate_strengths: true,
                            matching_skills: true,
                            missing_requirements: true,
                            experience_relevance: true,
                            resume: {
                                select: {
                                    name: true,
                                    picture: true,
                                }
                            }
                        }
                    },
                    shortlists: {
                        select: {
                            id: true
                        }
                    }
                },   
                orderBy: {
                    createdAt: 'desc'
                },
                take: pageSize,
                skip: skip,
            }),
            prisma.jobPost.count(whereClause)
        ]);

        // Process matches to include isShortlisted (already in your select, just ensuring structure)
        const processedMatches = matches.map(job => ({
            ...job,
            isShortlisted: job.shortlists.length > 0,            
            name: job.resumeMatches[0]?.resume?.name ? safeDecrypt(job.resumeMatches[0]?.resume?.name) : null,
            picture: job.resumeMatches[0]?.resume?.picture ? safeDecrypt(job.resumeMatches[0]?.resume?.picture) : null,
        }));

        return { matches: processedMatches, totalPages: Math.ceil(totalCount / pageSize) };
    } catch (error) {
        console.error("Error fetching job matches:", error);
      return { matches: [], totalPages: 0, error: "Failed to fetch job matches." };
    }
};

export const getAppliedJobsCompanyData = async (userId: string, companyId: string) => {
    try {
        const jobApplications = await prisma.appliedJobPost.findMany({
            where: {
              userId: userId,
              jobPost: {
                companyId: companyId
              }
            },
            select: {
              jobPost: {
                select: {
                  id: true,
                  jobTitle: true,
                  employmentType: true,
                  location: true,
                  salaryFrom: true,
                  salaryTo: true,
                  jobDescription: true,
                  createdAt: true,
                  company: {
                    select: {
                      name: true,
                      about: true,
                      logo: true,
                      location: true,
                      website: true,
                    },
                  },
                },
              },
            },
          });

          return jobApplications;
    } catch {
      return null;
    }
};

export const getJobApplicantsData = async (jobId: string, userId?: string) => {
    try {
      const data = await prisma.appliedJobPost.findMany({
        where: {
          jobPostId: jobId,
          ...(userId ? { userId: userId } : {})
        },
        include: {
           jobPost: {
                select: {
                    id: true,
                    jobTitle: true
                },
            },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobilePhone: true,
              username: true,
              image: true,
              resumes: {
                select: {
                  id: true,
                  name: true,
                  username: true,
                  picture: true,
                  resumeMatches: {
                    where: {
                      jobId: jobId
                    },
                    select: {
                      id: true,
                      overall_score: true,
                      skills_match: true,
                      experience_alignment: true,
                      education_fit: true,
                      match_analysis: true,
                      candidate_strengths: true,
                      matching_skills: true,
                      missing_requirements: true,
                      experience_relevance: true
                    }
                  },
                  shortlists: {
                    where: {
                      jobId: jobId
                    },
                    select: {
                      id: true,
                      resumeId: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      const jobApplicants = data.map(application => ({
        ...application,
        user: {
          ...application.user,
          firstName: application.user.firstName ? safeDecrypt(application.user.firstName) : "",
          lastName: application.user.lastName ? safeDecrypt(application.user.lastName) : "",
          email: application.user.email ? safeDecrypt(application.user.email) : "",
          mobilePhone: application.user.mobilePhone ? safeDecrypt(application.user.mobilePhone) : "",
          username: application.user.username ? application.user.username : "",
          image: application.user.image ? safeDecrypt(application.user.image) : "",
          isShortlisted: application.user.resumes?.[0]?.shortlists?.length > 0,
          resumes: application.user.resumes?.[0] ? {
            ...application.user.resumes?.[0],
            name: application.user.resumes?.[0].name ? safeDecrypt(application.user.resumes?.[0].name) : null,
            picture: application.user.resumes?.[0].picture ? safeDecrypt(application.user.resumes?.[0].picture) : null,
          } : null
        }
      }));

      // Sort by overall_score in descending order
      jobApplicants.sort((a, b) => {
        const scoreA = a.user.resumes?.resumeMatches?.[0]?.overall_score || 0;
        const scoreB = b.user.resumes?.resumeMatches?.[0]?.overall_score || 0;
        return scoreB - scoreA;
      });

    //    console.log(`[getJobApplicantsData] Raw data from Prisma (jobId: "${jobId}", resumeId: "${userId}"):`, JSON.stringify(jobApplicants, null, 2));

      return jobApplicants;
    } catch (error) {
    //   console.error("Error fetching job applicants:", error);
      return [];
    }
  };

export const getJobResumeMatchesData = async (jobId: string, resumeId?: string, score?: number) => {
    try {
      const whereConditions: Prisma.JobResumeMatchWhereInput = {
        jobId: jobId,
      };

      // Only apply score filter if a score is provided
      if (score !== undefined) {
        whereConditions.overall_score = {
          gte: score,
        };
      }

      const data = await prisma.jobResumeMatch.findMany({
        where: whereConditions,
        select: {
            id: true,
            jobId: true,
            resumeId: true,
            overall_score: true,
            skills_match: true,
            experience_alignment: true,
            education_fit: true,
            match_analysis: true,
            candidate_strengths: true,
            matching_skills: true,
            missing_requirements: true,
            experience_relevance: true,
            resume: {
                select: {
                    id: true,
                    userId: true,
                    name: true,
                    username: true,
                    picture: true,
                    shortlists: {
                        where: {
                            jobId: jobId
                        },
                        select: {
                            id: true
                        }
                    },
                    user: {
                        where: {
                            companyId: null
                        },
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            mobilePhone: true,
                            username: true,
                            image: true,
                        }
                    }
                }
            },
            jobPost: {
                select: {
                    jobTitle: true,
                }
            }
        },
        orderBy: {
          overall_score: 'desc'
        }
      });

      const jobMatches = data.map(match => ({
        ...match,
        resume: match.resume ? {
          ...match.resume,
          name: match.resume.name ? safeDecrypt(match.resume.name) : null,
          picture: match.resume.picture ? safeDecrypt(match.resume.picture) : null,
          isShortlisted: match.resume.shortlists?.length > 0,          
          user: match.resume?.user ? {
            ...match.resume.user,
            firstName: match.resume.user.firstName ? safeDecrypt(match.resume.user.firstName) : "",
            lastName: match.resume.user.lastName ? safeDecrypt(match.resume.user.lastName) : "",
            email: match.resume.user.email ? safeDecrypt(match.resume.user.email) : "",
            mobilePhone: match.resume.user.mobilePhone ? safeDecrypt(match.resume.user.mobilePhone) : "",
            username: match.resume.user.username || "",
            image: match.resume.user.image ? safeDecrypt(match.resume.user.image) : "",
            } : null,
        } : null
      }));
    //   console.log({jobMatches: JSON.stringify(jobMatches, null, 2)});
      return jobMatches;
    } catch (error) {
      return [];
    }
  };

  export const getJobShortlistedApplicantsData = async (jobId: string) => {
    try {
        const data = await prisma.jobShortlist.findMany({
          where: {
              jobId: jobId
          },
          include: {
              jobPost: {
                  select: {
                      id: true,
                      jobTitle: true,
                    },
              },
              resume: {
                  select: {
                      id: true,
                      userId: true,
                      name: true,
                      picture: true,
                      resumeMatches: {
                          where: {
                              jobId: jobId
                          },
                          select: {
                              id: true,
                              overall_score: true,
                              skills_match: true,
                              experience_alignment: true,
                              education_fit: true,
                              match_analysis: true,
                              candidate_strengths: true,
                              matching_skills: true,
                              missing_requirements: true,
                              experience_relevance: true
                          },
                      },
                      shortlists: {
                          where: {
                              jobId: jobId
                          },
                          select: {
                              id: true
                          }
                      },
                      user: {
                          select: {
                              id: true,
                              firstName: true,
                              lastName: true,
                              email: true,
                              mobilePhone: true,
                              username: true,
                              image: true,
                          }
                      }
                  }
              }
          }
        });
  
        const jobShortlist = data.map(shortlist => ({
          ...shortlist,
          user: shortlist.resume?.user ? {
            ...shortlist.resume.user,
            firstName: shortlist.resume.user.firstName ? safeDecrypt(shortlist.resume.user.firstName) : "",
            lastName: shortlist.resume.user.lastName ? safeDecrypt(shortlist.resume.user.lastName) : "",
            email: shortlist.resume.user.email ? safeDecrypt(shortlist.resume.user.email) : "",
            mobilePhone: shortlist.resume.user.mobilePhone ? safeDecrypt(shortlist.resume.user.mobilePhone) : "",
            username: shortlist.resume.user.username || "",
            image: shortlist.resume.user.image ? safeDecrypt(shortlist.resume.user.image) : "",
          } : null,
          resume: shortlist.resume ? {
            ...shortlist.resume,
            name: shortlist.resume?.name ? safeDecrypt(shortlist.resume.name) : null,
            picture: shortlist.resume.picture ? safeDecrypt(shortlist.resume.picture) : null,
          } : null
        }));

        // Sort by overall_score in descending order
        jobShortlist.sort((a, b) => {
          const scoreA = a.resume?.resumeMatches?.[0]?.overall_score || 0;
          const scoreB = b.resume?.resumeMatches?.[0]?.overall_score || 0;
          return scoreB - scoreA;
        });
  
        return jobShortlist;
      } catch (error) {
        return [];
      }
};

export const getUserJobShortlistData = async (jobId: string, resumeId: string) => {
    try {
        const data = await prisma.jobShortlist.findMany({
          where: {
              jobId: jobId,
              resumeId: resumeId
          },
          include: {
              resume: {
                  select: {
                      id: true,
                      userId: true,
                      picture: true,
                      resumeMatches: {
                          where: {
                              jobId: jobId
                          },
                          select: {
                              id: true,
                              overall_score: true,
                              skills_match: true,
                              experience_alignment: true,
                              education_fit: true,
                              match_analysis: true,
                              candidate_strengths: true,
                              matching_skills: true,
                              missing_requirements: true,
                              experience_relevance: true
                          },
                      },
                      shortlists: {
                          where: {
                              jobId: jobId
                          },
                          select: {
                              id: true
                          }
                      },
                      user: {
                          select: {
                              id: true,
                              firstName: true,
                              lastName: true,
                              email: true,
                              mobilePhone: true,
                              username: true,
                              image: true,
                          }
                      }
                  }
              }
          }
        });
  
        const jobShortlist = data.map(shortlist => ({
          ...shortlist,
          user: shortlist.resume?.user ? {
            ...shortlist.resume.user,
            firstName: shortlist.resume.user.firstName ? safeDecrypt(shortlist.resume.user.firstName) : "",
            lastName: shortlist.resume.user.lastName ? safeDecrypt(shortlist.resume.user.lastName) : "",
            email: shortlist.resume.user.email ? safeDecrypt(shortlist.resume.user.email) : "",
            mobilePhone: shortlist.resume.user.mobilePhone ? safeDecrypt(shortlist.resume.user.mobilePhone) : "",
            username: shortlist.resume.user.username || "",
            image: shortlist.resume.user.image ? safeDecrypt(shortlist.resume.user.image) : "",
          } : null,
          resume: shortlist.resume ? {
            ...shortlist.resume,
            picture: shortlist.resume.picture ? safeDecrypt(shortlist.resume.picture) : null,
          } : null
        }));

        // Sort by overall_score in descending order
        jobShortlist.sort((a, b) => {
          const scoreA = a.resume?.resumeMatches?.[0]?.overall_score || 0;
          const scoreB = b.resume?.resumeMatches?.[0]?.overall_score || 0;
          return scoreB - scoreA;
        });
  
        return jobShortlist;
      } catch (error) {
        return [];
      }
};

export async function getJobShortlistedCountData(jobId: string): Promise<number> {
    try {
      const count = await prisma.jobShortlist.count({
        where: {
          jobId: jobId
        }
      });
      return count;
    } catch (error) {
      console.error("Error getting shortlisted count:", error);
      return 0;
    }
};

export async function getJobApplicantsCountData(jobId: string): Promise<number> {
    try {
      const count = await prisma.appliedJobPost.count({
        where: {
          jobPostId: jobId
        }
      });
      return count;
    } catch (error) {
      console.error("Error getting shortlisted count:", error);
      return 0;
    }
};

export const getUserJobResumeMatchesData = async (resumeId: string, companyId: string) => {
    try {
      const data = await prisma.jobResumeMatch.findMany({
        where: {
            resume: {
                id: resumeId
            },
            jobPost: {
                companyId: companyId
            },
            overall_score: {
              gte: 70 // Only include matches with 70% or higher score
            }
        },
        include: {
            jobPost: {
                select: {
                    id: true,
                    jobTitle: true,
                    employmentType: true,
                    location: true,
                    salaryFrom: true,
                    salaryTo: true,
                    jobDescription: true,
                    createdAt: true,
                },
            },
            resume: {
                select: {
                    id: true,
                    userId: true,
                    name: true,
                    picture: true,
                }
            }   

        },
        orderBy: {
            jobPost: {
                createdAt: 'desc'
            }
        }
      });

        if (data && data.length > 0) {
            const jobMatches = data.map(match => ({
                ...match,
                resume: match.resume ? {
                ...match.resume,
                name: match.resume?.name ? safeDecrypt(match.resume.name) : null,
                picture: match.resume.picture ? safeDecrypt(match.resume.picture) : null,
                } : null
            }));

            return jobMatches;
        }
        return null;
    } catch (error) {
      console.error("Error fetching job matches:", error);
      return null;
    }
};

  export const getUserJobApplicationMatchesData = async (jobId: string) => {
    try {
      const data = await prisma.jobResumeMatch.findMany({
        where: {
            jobId: jobId,
            overall_score: {
              gte: 70 // Only include matches with 70% or higher score
            }
        },
        include: {
            jobPost: {
                select: {
                    id: true,
                    jobTitle: true,
                    employmentType: true,
                    location: true,
                    salaryFrom: true,
                    salaryTo: true,
                    jobDescription: true,
                    createdAt: true,
                },
            },
            resume: {
                select: {
                    id: true,
                    userId: true,
                    name: true,
                    picture: true,
                    resumeMatches: {
                        where: {
                            jobId: jobId
                        },
                        select: {
                            id: true,
                            overall_score: true,
                            skills_match: true,
                            experience_alignment: true,
                            education_fit: true,
                            match_analysis: true,
                            candidate_strengths: true,
                            matching_skills: true,
                            missing_requirements: true,
                            experience_relevance: true
                        },
                    },
                    shortlists: {
                        where: {
                            jobId: jobId
                        },
                        select: {
                            id: true
                        }
                    },
                    user: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            mobilePhone: true,
                            username: true,
                            image: true,
                        }
                    }
                }
            }
        },
        orderBy: {
            jobPost: {
                createdAt: 'desc'
            }
        }
      });
      
      const jobMatches = data.map(match => ({
        ...match,
        user: match.resume?.user ? {
          ...match.resume.user,
          firstName: match.resume.user.firstName ? safeDecrypt(match.resume.user.firstName) : "",
          lastName: match.resume.user.lastName ? safeDecrypt(match.resume.user.lastName) : "",
          email: match.resume.user.email ? safeDecrypt(match.resume.user.email) : "",
          mobilePhone: match.resume.user.mobilePhone ? safeDecrypt(match.resume.user.mobilePhone) : "",
          username: match.resume.user.username || "",
          image: match.resume.user.image ? safeDecrypt(match.resume.user.image) : "",
          isShortlisted: match.resume.shortlists?.length > 0,
        } : null,
        resume: match.resume ? {
          ...match.resume,
          name: match.resume?.name ? safeDecrypt(match.resume.name) : null,
          picture: match.resume.picture ? safeDecrypt(match.resume.picture) : null,
        } : null
      }));

      return jobMatches;
    } catch (error) {
      console.error("Error fetching job matches:", error);
      return [];
    }
  };

  export async function getJobMatchestData(jobId: string): Promise<number> {
    try {
      const count = await prisma.jobResumeMatch.count({
        where: {
          jobId: jobId
        }
      });
      return count;
    } catch (error) {
      console.error("Error getting shortlisted count:", error);
      return 0;
    }
};

  export const applyToJobData = async (userId: string, jobId: string) => {
    
    try {
    const data = await prisma.appliedJobPost.create({
        data: {
            id: uuidv4(),
            userId: userId,
            jobPostId: jobId,
            status: "APPLIED",
        }
    });
        return data;
    } catch (error) {
        console.error("Error applying for jobs:", error);
        return null;
    }
  };

export const getAllJobsRawTextData = async () => {
    
    try {
        const data = await prisma.jobPost.findMany({
            select: {
                id: true,
                userId: true,
                jobDescription: true,
                rawText: true,
            },
            orderBy: {
                createdAt: "desc",
            }
        });
        return data;
    } catch (error) {
        return null;
    }
  };

export const deleteJobData = async (id: string) => {
    try {
        // First, get the jobId before deleting the resume
        const jobData = await prisma.jobPost.findUnique({
            where: {
                id: id,
            },
        });
        
        if (!jobData) {
            return {
                error: "Job not found.",
                job: null,
            };
        }
        
        // Use a transaction to ensure both operations succeed or fail together
        const result = await prisma.$transaction(async (tx) => {
            // Set the job post to deleted, we do not delete record
            const deletedJob = await tx.jobPost.update({
                where: {
                    id: id,
                },
                data: {
                    status: "DELETED",
                },
            });
            
            // Delete the associated savedpost
            if (deletedJob) {
                await tx.savedJobPost.deleteMany({
                    where: {
                        jobPostId: deletedJob.id,
                    },
                });

                await tx.appliedJobPost.deleteMany({
                    where: {
                        jobPostId: deletedJob.id,
                    },
                });

                await tx.jobResumeMatch.deleteMany({
                    where: {
                        jobId: deletedJob.id,
                    },
                });

                await tx.jobShortlist.deleteMany({
                    where: {
                        jobId: deletedJob.id,
                    },
                });
            }
            
            return deletedJob;
        });
        
        return {
            success: "Success! job and associated items deleted.",
            job: result,
        };
    } catch (error) {
        console.error("Error deleting job:", error);
        return {
            error: `Error! Failed to delete job: ${error}`,
            job: null,
        };
    }
};

export const searchJobData = async (searchTerm: string, page: number = 1, pageSize: number = 10) => {
  try {
    if (!searchTerm.trim()) {
      return { jobs: [], totalPages: 0 }; // Return structure with totalPages
    }

    const skip = (page - 1) * pageSize;
    const whereClause: Prisma.JobPostWhereInput = {
      status: "ACTIVE",
      OR: [
          {
            jobTitle: {
              contains: searchTerm,
              mode: "insensitive",
            },
          },
          {
          jobDescription: {
              contains: searchTerm,
              mode: "insensitive",
            },
          },
          {
            company: {
              name: {
                contains: searchTerm,
                mode: "insensitive",
              },
            },
          },
        ],
    };

    const [jobs, totalCount] = await prisma.$transaction([
      prisma.jobPost.findMany({
        where: whereClause,
        select: {
          id: true,
          jobTitle: true,
          createdAt: true,
          company: {
            select: {
              name: true,
              logo: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: pageSize,
        skip: skip,
      }),
      prisma.jobPost.count({ where: whereClause }),
    ]);

    return { jobs, totalPages: Math.ceil(totalCount / pageSize) };
  } catch (error) {
    console.error("Error in searchJobData:", error);
    // Return structure consistent with success, including an error message
    return { jobs: [], totalPages: 0, error: "Failed to retrieve search results." };
  }
};

export const getJobNotesData = async (jobId: string, resumeId: string) => {    
    try {
        const data = await prisma.jobResumeNote.findMany({
            where: {
                jobId: jobId,
                resumeId: resumeId
            },
            include: {
                user: { // company user who entered the note
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        image: true,
                    }
                },
                resume: {
                    select: {
                        name: true,
                        picture: true,
                        resumeMatches: {
                            where: {
                                jobId: jobId,
                                resumeId: resumeId
                            },
                            select: {
                                id: true,
                                overall_score: true,
                                skills_match: true,
                                experience_alignment: true,
                                education_fit: true,
                                match_analysis: true,
                                createdAt: true
                            }
                        }
                    }
                }
            },
            orderBy: {
                createdAt: "desc",
            }
        });

        //  console.log(`[getJobNotesData] Raw data from Prisma (jobId: "${jobId}", resumeId: "${resumeId}"):`, JSON.stringify(data, null, 2));

        const jobNotes = data.map(note => ({
        ...note,
        user: note?.user ? {
          ...note.user,
          firstName: note.user.firstName ? safeDecrypt(note.user.firstName) : "",
          lastName: note.user.lastName ? safeDecrypt(note.user.lastName) : "",
          image: note.user.image ? safeDecrypt(note.user.image) : null,
        } : null,
        resume: note?.resume ? {
          ...note.resume,
          name: note.resume.name ? safeDecrypt(note.resume.name) : null,
          picture: note.resume.picture ? safeDecrypt(note.resume.picture) : null,
        } : null,
      }));
      
    //   console.log(`[getJobNotesData] Transformed jobNotes (jobId: "${jobId}", resumeId: "${resumeId}"):`, JSON.stringify(jobNotes, null, 2));

        return jobNotes;
    } catch (error) {
        return null;
    }
};

export async function AddJobNoteData(data: z.infer<typeof JobNoteSchema>) {
    try {

        const validatedFields = JobNoteSchema.safeParse(data);
        
        if (!validatedFields.success) {
            return { error: "Invalid fields." };
        }
        
        const { userId, companyId, jobId, resumeId, note, skills_match, experience_alignment, education_fit, other_notes } = validatedFields.data;
        const overall_score = (skills_match + experience_alignment + education_fit) / 3;
        const newId = uuidv4();

        const newNote = await prisma.jobResumeNote.create({
            data: {
                id: newId,
                userId: userId as string,
                companyId: companyId as string,
                jobId: jobId as string,
                resumeId: resumeId as string,
                note: note,
                otherNotes: other_notes,
                overall_score: overall_score,
                skills_match: skills_match,
                experience_alignment: experience_alignment,
                education_fit: education_fit,
            }
        });

        return newNote;
        
    } catch (error) {
        return null;
    }
};

export async function DeleteJobNoteData(id: string) {
    try {
        const note = await prisma.jobResumeNote.delete({
            where: {
                id: id
            }
        });

        return note;
    } catch (error) {
        return null;
    }
};

export async function createJobFromFileData(jobPost: JobDescPost) {
    try {
        const result = await prisma.jobPost.create({
            data: {
                id: jobPost.id,
                userId: jobPost.userId,
                companyId: jobPost.companyId as string,
                fileId: jobPost.fileId,
                jobTitle: jobPost.jobTitle,
                employmentType: jobPost.employmentType,
                experienceLevel: jobPost.experienceLevel,
                country: jobPost.country,
                location: jobPost.location,
                department: jobPost.department, 
                salaryCurrency: jobPost.salaryCurrency,
                salaryFrom: jobPost.salaryFrom,
                salaryTo: jobPost.salaryTo,
                jobDescription: jobPost.jobDescription,
                listingDuration: jobPost.listingDuration,        
                interviewType: jobPost.interviewType,
                localRemoteWork: jobPost.localRemoteWork,
                overseasRemoteWork: jobPost.overseasRemoteWork,
                skills: jobPost.skills,
                languageRequirements: jobPost.languageRequirements,
                tags: jobPost.tags,
                status: jobPost.status,
                rawText: jobPost.rawText,
                promptMarkdown: jobPost.promptMarkdown,
                structured: jobPost.structured, 
                metadata: jobPost.metadata,
            }
        });

        console.log({JOBFROMFILECREATED: JSON.stringify(result, null, 2)});
        return result;
    } catch (error) {
        console.log({JOBFROMFILECREATEDERROR: JSON.stringify(error, null, 2)});
        return null;
    }
};