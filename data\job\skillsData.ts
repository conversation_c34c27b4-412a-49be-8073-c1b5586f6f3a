export type SkillsData = {
  [category: string]: string[];
};

export const defaultSkillsData: SkillsData = {
  'Aerospace & Defense': [
    'Aerodynamics',
    'Aircraft Design',
    'Aircraft Maintenance',
    'Avionics',
    'Defense Systems',
    'Flight Control Systems',
    'Military Standards',
    'Missile Systems',
    'Radar Systems',
    'Satellite Systems',
    'Space Technology',
    'Systems Integration',
    'UAV/Drone Technology'
  ],
  'Agriculture & Farming': [
    'Agricultural Engineering',
    'Animal Health',
    'Crop Management',
    'Farm Equipment Operation',
    'Farm-to-Table',
    'Food Security',
    'Irrigation Systems',
    'Livestock Management',
    'Organic Farming',
    'Pest Management',
    'Precision Farming',
    'Soil Science',
    'Sustainability Practices'
  ],
  'Artificial Intelligence': [
    'AI Ethics',
    'Computer Vision',
    'Deep Learning',
    'Expert Systems',
    'Genetic Algorithms',
    'Knowledge Representation',
    'Natural Language Processing',
    'Neural Networks',
    'Machine Learning',
    'Robotics',
    'Search Algorithms',
    'Reinforcement Learning',
    'Robotics',
    'Speech Recognition'
  ],
  'Automotive': [
    'ADAS Systems',
    'Automotive Design',
    'Automotive Electronics',
    'Autonomous Driving',
    'Connected Car Technologies',
    'Electric Vehicles',
    'Engine Management Systems',
    'Hybrid Technology',
    'OBD Diagnostics',
    'Powertrain Systems',
    'Vehicle Diagnostics',
    'Vehicle Safety Systems'
  ],
  'Backend Development': [
    'ASP.NET Core',
    'Django',
    'Express.js',
    'FastAPI',
    'Flask',
    'GraphQL',
    'Laravel',
    'Microservices',
    'Node.js',
    'RESTful APIs',
    'Ruby on Rails',
    'Spring Boot'
  ],
  'Cloud Computing': [
    'AWS',
    'Azure',
    'Cloud Architecture',
    'Cloud Migration',
    'Cloud Security',
    'Cloud Storage',
    'GCP (Google Cloud Platform)',
    'IaaS',
    'Multi-cloud Management',
    'PaaS',
    'SaaS',
    'Serverless Computing'
  ],
  'Customer Service': [
    'Active Listening',
    'Adaptability',
    'Call Center Operations',
    'Client Onboarding',
    'Client Relationship Management',
    'Communication Skills',
    'Complaint Resolution',
    'CRM Software (e.g., Salesforce, Zendesk, HubSpot)',
    'Customer Advocacy',
    'Cultural Sensitivity',
    'Customer Feedback Analysis',
    'Customer Retention',
    'Customer Satisfaction (CSAT)',
    'Customer Support Platforms',
    'De-escalation Techniques',
    'Empathy',
    'Help Desk Support',
    'Issue Resolution',
    'Issue Tracking Systems (e.g., JIRA Service Desk)',
    'Knowledge Base Management',
    'Live Chat Support',
    'Multitasking',
    'Patience',
    'Problem Solving',
    'Proactive Communication',
    'Product Knowledge',
    'Service Level Agreements (SLAs)',
    'Resilience',
    'Technical Support',
    'Ticketing Systems',
    'Time Management',
    'Upselling/Cross-selling'
  ],
  'Data Science': [
    'A/B Testing',
    'Big Data Analytics',
    'Data Mining',
    'Data Modeling',
    'Data Visualization',
    'Experimental Design',
    'Feature Engineering',
    'Hypothesis Testing',
    'Power BI',
    'Predictive Analytics',
    'Python for Data Science',
    'R Programming',
    'Statistical Analysis',
    'Tableau'
  ],
  'Database': [
    'Cassandra',
    'Data Warehousing',
    'DynamoDB',
    'Elasticsearch',
    'ETL Processing',
    'Firebase',
    'MongoDB',
    'MySQL',
    'Neo4j',
    'Oracle',
    'PostgreSQL',
    'Redis',
    'SQL Server'
  ],
  'DevOps': [
    'Ansible',
    'CI/CD Pipelines',
    'Configuration Management',
    'Docker',
    'GitHub Actions',
    'GitLab CI/CD',
    'Infrastructure as Code',
    'Jenkins',
    'Kubernetes',
    'Linux Administration',
    'Monitoring & Logging',
    'Shell Scripting',
    'Terraform'
  ],
  'Education & E-Learning': [
    'Adult Education',
    'Blackboard LMS',
    'Canvas LMS',
    'Curriculum Development',
    'Distance Learning',
    'Educational Assessment',
    'Educational Psychology',
    'Educational Technology',
    'Instructional Design',
    'Learning Analytics',
    'Learning Management Systems',
    'Moodle',
    'Online Teaching',
    'Student Information Systems',
    'Virtual Classroom Management'
  ],
  'Energy & Utilities': [
    'Drilling',
    'Electrical Grid Management',
    'Energy Efficiency',
    'Energy Storage Systems',
    'Exploration',
    'Nuclear Energy',
    'Oil & Gas',
    'Power Systems Engineering',
    'Renewable Energy',
    'Smart Grid',
    'Solar Energy',
    'Utility Management',
    'Wind Energy'
  ],
  'Enterprise Systems': [
    'Dynamics 365',
    'Epicor',
    'ERP Implementation',
    'ERP Systems',
    'IFS',
    'Infor',
    'JD Edwards',
    'NetSuite',
    'Odoo',
    'Oracle ERP',
    'PeopleSoft',
    'Sage',
    'SAP ERP',
    'SAP S/4HANA',
    'Workday'
  ],
  'Environmental & Sustainability': [
    'Carbon Footprint Analysis',
    'Climate Change Mitigation',
    'Environmental Compliance',
    'Environmental Engineering',
    'Environmental Impact Assessment',
    'Environmental Monitoring',
    'Green Building',
    'Green Technology',
    'Natural Resource Management',
    'Renewable Energy Systems',
    'Sustainability Assessment',
    'Waste Management',
    'Water Resource Management'
  ],
  'Fashion & Apparel': [
    'Apparel Production',
    'CAD for Fashion',
    'Color Theory',
    'Fashion Design',
    'Fashion Merchandising',
    'Garment Construction',
    'Pattern Making',
    'Retail Buying',
    'Sustainable Fashion',
    'Textile Design',
    'Textile Science',
    'Trend Analysis',
    'Visual Merchandising'
  ],
  'Finance & Banking': [
    'Asset Management',
    'Banking Systems',
    'Blockchain Technology',
    'Bloomberg Terminal',
    'Core Banking Systems',
    'Credit Risk',
    'Cryptocurrency',
    'Derivatives Trading',
    'Financial Analysis',
    'Financial Modeling',
    'Financial Regulations',
    'Forex Trading',
    'Investment Analysis',
    'Market Risk',
    'MetaTrader',
    'Portfolio Management',
    'Risk Management',
    'Trading Platforms'
  ],
  'Food & Beverage Industry': [
    'Beverage Production',
    'Food Cost Control',
    'Food Processing',
    'Food Product Development',
    'Food Quality Control',
    'Food Safety',
    'Food Science',
    'HACCP',
    'Menu Planning',
    'Nutrition Analysis',
    'Production Planning',
    'Quality Assurance',
    'Recipe Development',
    'Supply Chain Management'
  ],
  'Frontend/Web Development': [
    'Angular',
    'Bootstrap',
    'CSS3',
    'HTML5',
    'JavaScript Frameworks',
    'Material UI',
    'Next.js',
    'Progressive Web Apps',
    'React',
    'Redux',
    'Responsive Design',
    'Sass/SCSS',
    'Svelte',
    'Tailwind CSS',
    'TypeScript',
    'Vue.js',
    'Web Accessibility',
    'WebGL'
  ],
  'Healthcare & Medical': [
    'Clinical Documentation',
    'Clinical Workflows',
    'Electronic Health Records (EHR)',
    'Epic Systems',
    'Healthcare Administration',
    'Healthcare Analytics',
    'Healthcare Compliance',
    'Healthcare Informatics',
    'Medical Devices',
    'Medical Imaging',
    'Medical Terminology',
    'Nursing Informatics',
    'Patient Care',
    'Telemedicine',
    'Vital Signs Monitoring'
  ],
  'Hospitality & Tourism': [
    'Customer Experience Management',
    'Event Management',
    'Event Planning',
    'Food & Beverage Management',
    'Guest Services',
    'Hotel Management Software',
    'Maestro',
    'Opera',
    'Reservation Systems',
    'Revenue Management',
    'Tourism & Travel Management',
    'Travel Itinerary'
  ],
  'Industry Standards': [
    'API Standards',
    'AS9100 (Aerospace)',
    'ASME Standards',
    'ASTM Standards',
    'AWS Standards',
    'CE Marking',
    'cGMP',
    'FDA 21 CFR Part 11',
    'FDA QSR',
    'GAMP',
    'GMP',
    'GxP',
    'HACCP',
    'IATF 16949',
    'IEC Standards',
    'ISO 13485 (Medical)',
    'ISO 14001 (Environmental)',
    'ISO 22000 (Food Safety)',
    'ISO 26262 (Automotive)',
    'ISO 27001 (Information Security)',
    'ISO 45001 (Safety)',
    'ISO 9001',
    'ISO/TS 16949',
    'REACH',
    'RoHS'
  ],
  'Languages': [
    'Arabic',
    'Cebuano',
    'English',
    'French',
    'German',
    'Hindi',
    'Italian',
    'Japanese',
    'Korean',
    'Mandarin',
    'Portuguese',
    'Russian',
    'Spanish',
    'Tagalog',
    'Thai',
    'Vietnamese'
  ],
  'Legal & Compliance': [
    'Civil Litigation',
    'Contract Drafting',
    'Contract Law',
    'Corporate Law',
    'Data Privacy Laws',
    'GDPR',
    'HIPAA',
    'Intellectual Property (IP)',
    'Legal Research',
    'LexisNexis',
    'Litigation',
    'Patent Law',
    'Regulatory Compliance',
    'Trademark Law',
    'Westlaw'
  ],
  'Machine Learning': [
    'Clustering',
    'Deep Learning',
    'Dimensionality Reduction',
    'Ensemble Methods',
    'Feature Engineering',
    'Machine Learning Algorithms',
    'Model Deployment',
    'Neural Networks',
    'PyTorch',
    'Regression Analysis',
    'Scikit-learn',
    'TensorFlow',
    'Time Series Analysis'
  ],
  'Manufacturing & Supply Chain': [
    '3D Printing',
    'Additive Manufacturing',
    'CNC Programming',
    'Demand Planning',
    'ERP Systems',
    'Inventory Management',
    'JIT Manufacturing',
    'Lean Manufacturing',
    'Logistics Planning',
    'Manufacturing Processes',
    'Process Planning',
    'Procurement',
    'Production Planning',
    'Quality Control',
    'Six Sigma',
    'Supply Chain Management',
    'Tool Design',
    'Warehouse Management'
  ],
  'Marketing & Advertising': [
    'Brand Management',
    'Campaign Management',
    'Content Marketing',
    'Digital Marketing',
    'Email Marketing',
    'Google Analytics',
    'Market Analysis',
    'Marketing Automation',
    'Marketing Strategy',
    'PPC Advertising',
    'SEO/SEM',
    'Social Media Marketing',
    'Web Analytics'
  ],
  'Mechanical Design': [
    '3D Modeling',
    'AutoCAD',
    'CATIA',
    'Creo',
    'Fusion 360',
    'GD&T',
    'Inventor',
    'NX',
    'Pro/ENGINEER',
    'SolidWorks'
  ],
  'Media & Entertainment': [
    'Adobe Creative Suite',
    'Adobe Premiere Pro',
    'Animation',
    'Autodesk Maya',
    'Broadcasting',
    'Cinematography',
    'Film Production',
    'Final Cut Pro',
    'Game Development',
    'Motion Graphics',
    'Sound Design',
    'Unity',
    'Unreal Engine',
    'Video Editing',
    'Video Production'
  ],
  'Mobile Development': [
    'Android Development',
    'App Store Optimization',
    'Cross-platform Development',
    'Flutter',
    'iOS Development',
    'Kotlin',
    'Mobile App Security',
    'Mobile UI/UX',
    'Native App Development',
    'React Native',
    'Swift',
    'Xamarin'
  ],
  'Pharmaceuticals & Biotechnology': [
    'Biostatistics',
    'Biotechnology Techniques',
    'Clinical Research',
    'Clinical Trials',
    'CRISPR',
    'Drug Development',
    'Drug Safety',
    'FDA Regulations',
    'GMP Compliance',
    'Laboratory Techniques',
    'Pharmacovigilance',
    'Quality Control'
  ],
  'Programming Languages': [
    'C#',
    'C++',
    'Go',
    'Java',
    'JavaScript',
    'Kotlin',
    'MATLAB',
    'PHP',
    'Python',
    'R',
    'Ruby',
    'Rust',
    'Scala',
    'Shell Scripting',
    'Swift',
    'TypeScript'
  ],
  'Project Management': [
    'Agile',
    'Budgeting',
    'Confluence',
    'JIRA',
    'Kanban',
    'MS Project',
    'Portfolio Management',
    'Program Management',
    'Project Planning',
    'Resource Planning',
    'Risk Management',
    'Scrum',
    'Stakeholder Management'
  ],
  'Quality Management': [
    '5S Methodology',
    '8D Problem Solving',
    'APQP',
    'Continuous Improvement',
    'DMAIC',
    'FMEA',
    'ISO 9001',
    'Kaizen',
    'Lean Management',
    'Lean Six Sigma',
    'PDCA Cycle',
    'PPAP',
    'Process Capability Analysis',
    'Quality Auditing',
    'Quality Control',
    'Quality Management Systems (QMS)',
    'Root Cause Analysis',
    'SPC (Statistical Process Control)',
    'Six Sigma',
    'TQM (Total Quality Management)',
    'Value Stream Mapping'
  ],
  'Real Estate & Construction': [
    'Architectural Design',
    'Building Codes',
    'Building Information Modeling (BIM)',
    'Construction Management',
    'Construction Safety',
    'Cost Estimation',
    'Facilities Management',
    'HVAC Systems',
    'Property Management',
    'Real Estate Development',
    'Real Estate Finance',
    'Site Planning',
    'Structural Engineering'
  ],
  'Research & Development': [
    'Clinical Research',
    'Data Analysis',
    'Experimental Design',
    'Laboratory Management',
    'Laboratory Techniques',
    'Market Research',
    'Product Development',
    'Project Management',
    'Protocol Development',
    'Quality Control',
    'Research Documentation',
    'Research Methodology',
    'Scientific Research',
    'Statistical Analysis'
  ],
  'Retail & E-commerce': [
    'BigCommerce',
    'CRM Systems',
    'E-commerce Platforms',
    'HubSpot',
    'Inventory Management',
    'Last-Mile Delivery Optimization',
    'Logistics & Supply Chain',
    'Magento',
    'Merchandising',
    'Point of Sale (POS)',
    'Retail Analytics',
    'SAP',
    'Salesforce',
    'Shopify',
    'Supply Chain Management',
    'WooCommerce'
  ],
  'Sales': [
    'Account Management',
    'B2B Sales',
    'B2C Sales',
    'Business Development',
    'Competitive Analysis',
    'Channel Sales',
    'Client Acquisition',
    'Closing Techniques',
    'Cold Calling',
    'Consultative Selling',
    'Contract Negotiation',
    'Digital Selling/Social Selling',
    'Emotional Intelligence',
    'CRM Software (e.g., Salesforce, HubSpot, Pipedrive)',
    'Cross-selling',
    'Customer Relationship Management',
    'Demo/Presentation Skills',
    'Lead Generation',
    'Market Analysis',
    'Negotiation Skills',
    'Networking',
    'Objection Handling',
    'Pipeline Management',
    'Product Demonstration',
    'Product Knowledge',
    'Prospecting',
    'Resilience/Persistence',
    'Relationship Building',
    'Sales Analytics',
    'Sales Cycle Management',
    'Sales Forecasting',
    'Sales Funnel Management',
    'Sales Management',
    'Sales Operations',
    'Sales Presentations',
    'Sales Strategy',
    'Storytelling in Sales',
    'Solution Selling',
    'Strategic Alliances',
    'Target Account Selling',
    'Territory Management',
    'Upselling',
    'Value Proposition Development'
  ],
  'Security': [
    'Access Control',
    'Application Security',
    'Authentication',
    'Authorization',
    'Cloud Security',
    'Cryptography',
    'Cybersecurity',
    'Encryption',
    'Ethical Hacking',
    'Identity Management',
    'Incident Response',
    'Information Security',
    'JWT',
    'Mobile Security',
    'Network Security',
    'OAuth',
    'OWASP',
    'Penetration Testing',
    'Security Auditing',
    'SIEM',
    'SSL/TLS',
    'Vulnerability Assessment'
  ],
  'Soft Skills': [
    'Adaptability',
    'Communication',
    'Conflict Resolution',
    'Critical Thinking',
    'Cross-cultural Communication',
    'Decision Making',
    'Emotional Intelligence',
    'Leadership',
    'Negotiation',
    'Presentation Skills',
    'Problem Solving',
    'Public Speaking',
    'Team Leadership',
    'Time Management'
  ],
  'Software Architecture': [
    'API Design',
    'Clean Architecture',
    'Cloud Architecture',
    'Design Patterns',
    'Distributed Systems',
    'Domain-Driven Design',
    'Event-Driven Architecture',
    'Microservices',
    'RESTful Architecture',
    'Scalable Systems',
    'Serverless Architecture',
    'Service-Oriented Architecture',
    'System Design'
  ],
  'Telecommunications': [
    '4G/5G Technology',
    'Cellular Networks',
    'Fiber Optics',
    'IP Networking',
    'Mobile Communications',
    'Network Infrastructure',
    'Network Planning',
    'Network Security',
    'RF Engineering',
    'Satellite Communications',
    'Telecom Systems',
    'VoIP',
    'Wireless Communications'
  ],
  'Testing & QA': [
    'Automated Testing',
    'Cypress',
    'E2E Testing',
    'Integration Testing',
    'Jest',
    'JUnit',
    'Load Testing',
    'Manual Testing',
    'Mocha',
    'Performance Testing',
    'Selenium',
    'Test Automation',
    'Test Planning',
    'TestNG',
    'Unit Testing'
  ],
  'Transportation & Logistics': [
    'Airline Operations',
    'Fleet Management',
    'Freight Billing',
    'Freight Forwarding',
    'Inventory Management',
    'Logistics Planning',
    'Route Optimization',
    'Shipping & Freight',
    'Supply Chain Management',
    'Transportation Management Systems (TMS)',
    'Warehouse Management'
  ],
  'Version Control': [
    'Bitbucket',
    'Git',
    'GitHub',
    'GitLab',
    'Mercurial',
    'Source Control Management',
    'SVN',
    'Version Control Systems'
  ],
  'Writing & Editing': [
    'Blog Writing',
    'Content Creation',
    'Content Strategy',
    'Copywriting',
    'Editorial Planning',
    'Editing',
    'Grant Writing',
    'Proofreading',
    'Technical Writing',
    'Writing'
  ]
};

// Function to add a new skill
export async function addSkill(
  category: string,
  skillName: string
): Promise<void> {
  const formattedSkillName = skillName.trim();
  const formattedCategory = category.trim();

  try {
    // First update the in-memory list
    if (!defaultSkillsData[formattedCategory]) {
      defaultSkillsData[formattedCategory] = [];
    }

    // Only proceed with skill addition if there's a non-empty skill name
    if (formattedSkillName) {
      const skillExists = defaultSkillsData[formattedCategory].includes(formattedSkillName);

      if (!skillExists) {
        // Remove any duplicates before adding the new skill
        defaultSkillsData[formattedCategory] = [...new Set(defaultSkillsData[formattedCategory])];
        defaultSkillsData[formattedCategory].push(formattedSkillName);
        defaultSkillsData[formattedCategory].sort((a, b) => a.localeCompare(b));
      }
    }

    // Call API to persist to file
    const response = await fetch("/api/job/skill", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        category: formattedCategory,
        skillName: formattedSkillName,
      }),
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to persist skill');
    }
  } catch (error) {
    console.error("Error adding skill:", error);
    throw error;
  }
}
