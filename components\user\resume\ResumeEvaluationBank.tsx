import { <PERSON>alog, <PERSON>alog<PERSON><PERSON>le, DialogTrigger, DialogPortal, DialogOverlay, DialogClose, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { EyeIcon, X } from "lucide-react";

// Helper function to determine color based on score
const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-emerald-500";
    if (score >= 80) return "bg-green-500";
    if (score >= 70) return "bg-yellow-500";
    if (score >= 60) return "bg-orange-500";
    return "bg-red-500";
};

// Score circle component
const ScoreCircle = ({
  score,
  label,
}: {
  score: number | null | undefined;
  label: string;
}) => {
  // Default to 0 if score is undefined or null
  const displayScore = score ?? 0;

  return (
    <div className="flex flex-col items-center">
      <div
        className={`${getScoreColor(displayScore)} text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-sm`}
      >
        {displayScore}%
      </div>
      <span className="text-xs mt-1 text-gray-600">{label}</span>
    </div>
  );
};

// Helper function to parse string arrays
const parseStringArray = (data: string | null): string[] => {
  if (!data) return [];
  
  try {
    // Try to parse as JSON
    const parsed = JSON.parse(data);
    if (Array.isArray(parsed)) return parsed;
    return [];
  } catch (e) {
    // If it's not valid JSON, check if it looks like an array string
    if (data.startsWith('[') && data.endsWith(']')) {
      // Simple string parsing for array-like strings
      return data
        .slice(1, -1)
        .split(',')
        .map(item => item.trim().replace(/"/g, '').replace(/'/g, ''));
    }
    // Return as single item if it's just a string
    return [data];
  }
};

// List component for strengths, improvements, etc.
const EvaluationList = ({
    items,
    title,
  }: {
    items: string[] | undefined;
    title: string;
  }) => (
    <div className="mb-4">
      <h4 className="font-medium text-sm mb-2">{title}</h4>
      <ul className="list-disc pl-5 text-sm space-y-1">
        {items?.map((item, index) => (
          <li key={index} className="text-gray-700">
            {item}
          </li>
        )) || <li className="text-gray-500">No data available</li>}
      </ul>
    </div>
  );

type ResumeEval = {
  id: string;
  overallScore: number | null;
  completenessScore: number | null;
  experienceQualityScore: number | null;
  educationScore: number | null;
  skillsScore: number | null;
  strengths: string | null;
  improvements: string | null;
  recommendations: string | null;
  experienceEvaluation: string | null;
  educationEvaluation: string | null;
  skillsEvaluation: string | null;
};

export function ResumeEvaluation({ 
  resumeEval, 
  applicantName 
}: { 
resumeEval: ResumeEval | undefined; 
  applicantName: string;
}) {
  if (!resumeEval) {
    return (
      <div className="p-4">
        <p className="text-sm text-red-500">
          No evaluation available for this resume.
        </p>
      </div>
    );
  }

  // Parse potential array fields
  const skillsEvaluation = parseStringArray(resumeEval.skillsEvaluation);
  const educationEvaluation = parseStringArray(resumeEval.educationEvaluation);
  const experienceEvaluation = parseStringArray(resumeEval.experienceEvaluation);
  const improvements = parseStringArray(resumeEval.improvements);
  const strengths = parseStringArray(resumeEval.strengths);
  const recommendations = parseStringArray(resumeEval.recommendations);

  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
        <div>
            <h3 className="text-lg font-semibold">
                Resume Evaluation
            </h3> 
          <em className="text-sm text-gray-500">AI algorithm used industry best practices to evaluate how well this resume is well created.</em>
        </div>
      </div>

      {/* Scores */}
      <div className="flex items-center justify-between gap-2 mt-4 mb-4 pl-10 pr-10">
        <ScoreCircle score={resumeEval.overallScore} label="Overall" />
        <ScoreCircle score={resumeEval.completenessScore} label="Completeness" />
        <ScoreCircle score={resumeEval.skillsScore} label="Skills" />
        <ScoreCircle score={resumeEval.experienceQualityScore} label="Experience" />
        <ScoreCircle score={resumeEval.educationScore} label="Education" />
      </div>

      <Separator className="my-2" />
        
        <div className="flex flex-col">
            <div className="mb-2">
                {/* Assessment */}
                <ScrollArea className="h-auto h-max-[150px] pr-4">
                    <EvaluationList items={strengths} title="Strengths" />
                </ScrollArea>
            </div>
            <Separator />
            <div className="mb-4 mt-2">
                {/* Detailed Evaluation */}
                <ScrollArea className="h-auto h-max-[150px] pr-4">
                    <div className="text-sm space-y-3">
                        <div>
                        <h4 className="font-medium mb-1">Experience</h4>
                        <p className="text-gray-700">{experienceEvaluation}</p>
                        </div>
                        <div>
                        <h4 className="font-medium mb-1">Education</h4>
                        <p className="text-gray-700">{educationEvaluation}</p>
                        </div>
                        <div>
                        <h4 className="font-medium mb-1">Skills</h4>
                        <p className="text-gray-700">{skillsEvaluation}</p>
                        </div>
                    </div>
                </ScrollArea>
            </div>
        </div>    
        <div className="mb-1 border-2 p-2 rounded-lg">
            <p className="text-sm text-gray-700 whitespace-pre-line">
                This evaluation is AI generated and may not be 100% accurate. Please review the resume and job description before making a decision.
            </p>
        </div>
        &nbsp;
    </div>
  );
}

export function ResumeEvaluationDialog({ 
resumeEval, 
  applicantName 
}: { 
    resumeEval: ResumeEval | undefined; 
  applicantName: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="text-primary hover:underline cursor-pointer" title="View Evaluation">
            <EyeIcon className="w-5 h-5" />
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-[9999]" />
        <div className="fixed inset-0 flex items-center justify-center z-[10000] overflow-auto p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-[600px] max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6">
              <DialogTitle className="text-lg font-semibold">{applicantName}</DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 cursor-pointer">
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </DialogClose>
            </div>
            <div className="overflow-y-auto pl-6 pr-6">
              <ResumeEvaluation resumeEval={resumeEval} applicantName={applicantName} />
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}






