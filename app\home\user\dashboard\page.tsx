"use client";

import { useSession } from "next-auth/react";
import Link from "next/link";
import { useState, ChangeEvent, useEffect, useCallback } from "react";
import Image from "next/image"; // For company logo
import PriceSelectTable from "@/components/general/PriceSelect";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from "@/components/ui/dialog";
import LoadingFallback from "@/components/general/LoadingFallback";
import { LoadingStateHorizontal } from "@/components/general/LoadingStateHorizontal";
import { CurrencyData } from "@/utils/currencyUtils";
import { UserPlan } from "@/types/customTypes";
import { setUserPlan } from "@/actions/user/userActions";
import { toast } from "sonner";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { SuccessMessage } from "@/components/info/SuccessMessage";
import { searchJob } from "@/actions/job/searchJob"; // Import your actual server action
import { MainPagination } from "@/components/general/MainPagination"; // Import pagination component
import { FileInputIcon, FilePenLine, Heart, UserCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { verifyEmail } from "@/actions/user/verifyEmail";

// Define a type for search results
interface JobSearchResult {
  id: string;
  jobTitle: string;
  company: {
    name: string;
    logo?: string | null; // Added logo
  } | null;
  createdAt: string; // Added createdAt for post date
}

// Define a type for common dashboard card props for reusability
interface DashboardCardProps {
  title: string;
  description: string;
  link: string;
  linkText: string;
  icon?: React.ReactNode;
}

// A simple reusable card component.
// For a larger application, you might move this to its own file
const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  description,
  link,
  linkText,
  icon,
}) => (
  <div className="bg-white shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
    <div className="flex items-center mb-3">
      {icon && <span className="mr-3 text-blue-600 text-2xl">{icon}</span>}
      <h2 className="text-xl font-semibold text-gray-700">{title}</h2>
    </div>
    <p className="text-gray-600 mb-4">{description}</p>
    <Link
      href={link}
      className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
    >
      {linkText} &rarr;
    </Link>
  </div>
);

export default function UserDashboardPage() {
  const { data: session, status, update } = useSession();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<JobSearchResult[]>([]);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isLoadingSearch, setIsLoadingSearch] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const SEARCH_PAGE_SIZE = 10; // Define page size for search results

  const [isPriceDialogForcedOpen, setIsPriceDialogForcedOpen] = useState(false);
  const [isPlanUpdating, setIsPlanUpdating] = useState(false);
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [planUpdateError, setPlanUpdateError] = useState<string | undefined>(
    ""
  );
  const [planUpdateSuccess, setPlanUpdateSuccess] = useState<
    string | undefined
  >("");

  const isLoadingSession = status === "loading";
  const isAuthenticated = status === "authenticated";

  useEffect(() => {
    const shouldBeOpen = isAuthenticated && session && !session.user.planSet;
    setIsPriceDialogForcedOpen(shouldBeOpen);
  }, [isAuthenticated, session, status]);

  // Debounce search term
  useEffect(() => {
    const timerId = setTimeout(() => {
      // Do not set debounced search term if the price dialog is open,
      // as search is not relevant then.
      if (isPriceDialogForcedOpen) return;

      if (searchTerm.trim().length >= 2 || searchTerm.trim().length === 0) {
        setDebouncedSearchTerm(searchTerm.trim());
      } else if (searchTerm.trim().length < 2 && searchTerm.trim().length > 0) {
        // If less than 2 chars but not empty, clear results and show a message or do nothing
        setSearchResults([]);
        setSearchError(null); // Or "Type at least 2 characters"
        setDebouncedSearchTerm(""); // Prevent search with <2 chars
      }
    }, 500); // 500ms debounce delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm, isPriceDialogForcedOpen]);

  // Effect to perform search when debouncedSearchTerm changes and meets criteria
  const performSearch = useCallback(
    async (termToSearch: string, page: number) => {
      if (!termToSearch || termToSearch.length < 2) {
        setSearchResults([]);
        setSearchError(null);
        setTotalPages(0);
        // setCurrentPage(1); // Already handled by handleSearchChange or initial state
        setIsLoadingSearch(false);
        return;
      }

      setIsLoadingSearch(true);
      setSearchError(null);

      try {
        const response = await searchJob(termToSearch, page, SEARCH_PAGE_SIZE);
        if (response.error) {
          setSearchError(response.error);
          setSearchResults([]);
          setTotalPages(0);
        } else if (response.jobs) {
          setSearchResults(response.jobs);
          setTotalPages(response.totalPages || 0);
        }
      } catch (error) {
        setSearchError("An unexpected error occurred during search.");
        setSearchResults([]);
        setTotalPages(0);
      } finally {
        setIsLoadingSearch(false);
      }
    },
    [SEARCH_PAGE_SIZE]
  );

  useEffect(() => {
    if (debouncedSearchTerm.length >= 2) {
      performSearch(debouncedSearchTerm, currentPage);
    } else {
      setSearchResults([]);
      setTotalPages(0);
      setSearchError(null);
      // currentPage is not reset here, allowing it to persist if user types again
    }
  }, [debouncedSearchTerm, currentPage, performSearch]);

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  if (isLoadingSession) {
    return <LoadingFallback message="Loading your session..." />;
  }

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  async function onPlanSelect(
    planId: string,
    price: number,
    isAnnual: boolean,
    currencyData: CurrencyData,
    subscriptionID?: string,
    processor?: string
  ) {
    setIsPriceDialogForcedOpen(false); // Close dialog immediately on attempt

    const userId = session?.user?.id;

    if (!userId) {
      setIsPlanUpdating(false);
      setPlanUpdateError(
        "User information is missing. Please re-authenticate."
      );
      toast.error("User information is missing. Please re-authenticate.");
      return;
    }

    setIsPlanUpdating(true);
    setPlanUpdateError("");
    setPlanUpdateSuccess("");

    try {
      const userPlan: UserPlan = {
        id: "", // Will be set by the backend
        userId: userId,
        processor: processor,
        subscriptionId: subscriptionID,
        planId: planId,
        amount: price,
        paymentFrequency: isAnnual ? "Annual" : "Monthly",
        exchangeRate: currencyData.exchangeRate,
        exchangeCurrencyCode: currencyData.currencyCode,
        exchangeCurrencySymbol: currencyData.currencySymbol,
        userLocale: currencyData.locale,
        baseCurrencyCode: "USD", // Assuming base is always USD
        baseCurrencySymbol: "$",
      };

      const response = await setUserPlan(userPlan);
      if (response?.success) {
        setPlanUpdateSuccess(response.success);
        toast.success(response.success);
        // Trigger a session update to reflect planSet: true
        await update({
          ...session,
          user: {
            ...session.user,
            planSet: true,
          },
        });
      } else if (response?.error) {
        setPlanUpdateError(response.error);
        toast.error(response.error);
        setIsPriceDialogForcedOpen(true); // Re-open dialog if plan setting failed
      } else {
        setPlanUpdateError(
          "An unexpected response was received from the server."
        );
        toast.error("An unexpected response was received from the server.");
        setIsPriceDialogForcedOpen(true); // Re-open dialog
      }
    } catch (error) {
      console.error("Error in onPlanSelect for user dashboard:", error);
      setPlanUpdateError(
        "An error occurred while updating the plan. Please try again."
      );
      toast.error(
        "An error occurred while updating the plan. Please try again."
      );
      setIsPriceDialogForcedOpen(true); // Re-open dialog
    } finally {
      setIsPlanUpdating(false);
    }
  }

  if (isPriceDialogForcedOpen) {
    return (
      <Dialog
        open={isPriceDialogForcedOpen}
        onOpenChange={(openStatusFromRadix) => {
          if (isPriceDialogForcedOpen && !openStatusFromRadix) {
            return;
          }
          setIsPriceDialogForcedOpen(openStatusFromRadix);
        }}
      >
        <DialogTitle>Select a Plan</DialogTitle>{" "}
        {/* Added for accessibility and context */}
        <DialogPortal>
          <DialogOverlay className="z-[9999] bg-black/80" />
          <div className="fixed inset-0 z-[10000] flex items-center justify-center p-4 overflow-auto">
            <DialogPrimitive.Content
              onEscapeKeyDown={(e) => {
                if (isPriceDialogForcedOpen) e.preventDefault();
              }}
              onInteractOutside={(e) => {
                if (isPriceDialogForcedOpen) e.preventDefault();
              }}
              className="relative bg-background border shadow-lg rounded-lg w-full max-w-5xl max-h-[calc(100vh-2rem)] overflow-y-auto p-0"
            >
              <PriceSelectTable onPlanSelect={onPlanSelect} />
            </DialogPrimitive.Content>
          </div>
        </DialogPortal>
      </Dialog>
    );
  }

  const dashboardSections: DashboardCardProps[] = [
    {
      title: "My Profile",
      description:
        "View and update your personal information, resume, and preferences.",
      link: "/home/<USER>/profile",
      linkText: "Go to Profile",
      icon: <UserCircle className="h-6 w-6" />,
    },
    {
      title: "My Applications",
      description: "Track the status of jobs you've applied for.",
      link: "/home/<USER>/applications",
      linkText: "View Applications",
      icon: <FilePenLine className="h-6 w-6" />,
    },
    {
      title: "Job Matches",
      description: "Access jobs you're matched with for your consideration.",
      link: "/home/<USER>/matches",
      linkText: "View Matches",
      icon: <FileInputIcon className="h-6 w-6" />,
    },
    {
      title: "Favorites",
      description:
        "Access jobs you've bookmarked for later viewing or application.",
      link: "/home/<USER>/favorites",
      linkText: "View Favorites",
      icon: <Heart className="h-6 w-6" />,
    },
  ];

  async function handleVerifyEmail() {
    try {
      setIsVerifyingEmail(true);
      const response = await verifyEmail(session?.user?.id as string);
      if (response?.success) {
        setIsEmailSent(true);
        toast.success(response.success);
      } else if (response?.error) {
        setIsEmailSent(false);
        toast.error(response.error);
      }
    } catch (error) {
      toast.error(
        `An error occurred while verifying your email. Please try again.`
      );
    } finally {
      setIsVerifyingEmail(false);
    }
  }

  return (
    <div className="container mx-auto p-4 md:p-4 bg-gray-50 min-h-screen">
      {!session?.user?.verified &&
        (!isEmailSent ? (
          <div className="flex w-full items-center justify-left bg-rose-50 border rounded-lg p-2 mb-4">
            Your email remains unverified. Please verify now! &nbsp;&nbsp;
            <Button
              onClick={handleVerifyEmail}
              variant={"outline"}
              className="cursor-pointer h-[25px]"
            >
              {isVerifyingEmail ? "Sending Email..." : "Verify Email"}
            </Button>
          </div>
        ) : (
          <div className="flex w-full items-center justify-left bg-green-50 border rounded-lg p-2 mb-4">
            Email sent! Please check your email and verify your registration.
          </div>
        ))}

      <header className="mb-8 md:mb-12">
        <h1 className="text-3xl md:text-3xl font-bold text-gray-800">
          Welcome back{session?.user?.name ? `, ${session.user.name}` : ""}!
        </h1>
        <p className="text-md text-gray-600 mt-2">
          This is your personal dashboard. Manage your job search and profile
          information here.
        </p>
        {isPlanUpdating && (
          <div className="mt-4">
            <LoadingStateHorizontal text="Updating your plan, please wait..." />
          </div>
        )}
        <ErrorMessage message={planUpdateError} />
        <SuccessMessage message={planUpdateSuccess} />
      </header>

      {/* Future Enhancements Section */}
      <section className="mt-10 md:mt-16">
        <h2 className="text-2xl font-semibold text-gray-700 mb-6">
          Dashboard Insights & Actions
        </h2>

        {/* Prominent Search Bar Section */}
        <div className="p-6 mb-6 bg-white shadow-md rounded-lg">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">
            Find Your Next Opportunity
          </h3>
          <div className="flex items-center space-x-2">
            {" "}
            {/* Changed form to div */}
            <input
              type="text"
              placeholder="Search jobs by title, company, description..."
              className="flex-grow p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={handleSearchChange}
              aria-label="Search jobs"
            />
            {/* Search button removed */}
          </div>
          {searchTerm.trim().length > 0 && searchTerm.trim().length < 2 && (
            <p className="mt-2 text-sm text-gray-500">
              Please type at least 2 characters to search.
            </p>
          )}

          {/* Display Search Results */}
          {isLoadingSearch && debouncedSearchTerm.length >= 2 && (
            <p className="mt-4 text-gray-600">Searching for jobs...</p>
          )}
          {searchError && <p className="mt-4 text-red-500">{searchError}</p>}
          {!isLoadingSearch && searchResults.length > 0 && (
            <div className="mt-6">
              <h4 className="text-md font-semibold text-gray-700 mb-3">
                Search Results:
              </h4>
              <ul className="space-y-2">
                {searchResults.map((job) => (
                  <li
                    key={job.id}
                    className="p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <Link
                      href={`/public/job/${job.id}`}
                      className="flex items-center space-x-3"
                    >
                      {" "}
                      {/* Changed items-start to items-center for better vertical alignment with logo */}
                      {job.company?.logo ? (
                        <Image
                          src={job.company.logo}
                          alt={job.company.name || "Company"}
                          width={40}
                          height={40}
                          className="rounded-md object-contain"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center text-gray-500 text-xs">
                          Logo
                        </div>
                      )}
                      <div className="flex-grow">
                        <span className="font-medium text-blue-700 hover:underline block">
                          {job.jobTitle}
                        </span>
                        {job.company && (
                          <span className="text-sm text-gray-600 block">
                            at {job.company.name}
                          </span>
                        )}
                        <span className="text-xs text-gray-500 block">
                          Posted: {new Date(job.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {!isLoadingSearch &&
            !searchError &&
            searchResults.length === 0 &&
            debouncedSearchTerm.length >= 2 && ( // Check debouncedSearchTerm here
              <p className="mt-4 text-gray-600">
                No jobs found matching &quot;{debouncedSearchTerm}&quot;.
              </p>
            )}
        </div>

        {!isLoadingSearch && searchResults.length > 0 && totalPages > 1 && (
          <div className="mt-6 flex justify-center">
            <MainPagination
              totalPages={totalPages}
              currentPage={currentPage}
              onPageChange={handlePageChange} // Ensure MainPagination supports this prop
            />
          </div>
        )}

        {/* Placeholder for Quick Stats */}
        <div className="mb-8 p-6 bg-white shadow-md rounded-lg">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">
            Quick Stats
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-3xl font-bold text-blue-600">--</p>
              <p className="text-sm text-gray-600">Active Applications</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-3xl font-bold text-green-600">--</p>
              <p className="text-sm text-gray-600">Saved Jobs</p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <p className="text-3xl font-bold text-yellow-600">--</p>
              <p className="text-sm text-gray-600">Profile Views</p>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-4 text-center italic"></p>
        </div>

        {/* Placeholder for Recent Activity / Notifications */}
        {/* <div className="mb-8 p-6 bg-white shadow-md rounded-lg">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">Recent Activity / Notifications</h3>
          <ul className="space-y-3">
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">Applied for "Senior Developer" - 2 days ago</li>
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">Saved job "UX Designer" - 3 days ago</li>
            <li className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">Your application for "Project Manager" was viewed - 1 week ago</li>
          </ul>
          <p className="text-xs text-gray-500 mt-4 text-center italic"></p>
        </div> */}
      </section>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 mt-10">
        {dashboardSections.map((section) => (
          <DashboardCard
            key={section.title}
            title={section.title}
            description={section.description}
            link={section.link}
            linkText={section.linkText}
            icon={section.icon}
          />
        ))}
      </div>
    </div>
  );
}
