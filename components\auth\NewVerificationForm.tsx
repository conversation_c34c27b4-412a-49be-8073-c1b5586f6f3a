"use client";

import { useSearchParams } from "next/navigation";
import { CardWrapper } from "@/components/info/CardWrapper";
import { <PERSON>Loader } from "react-spinners";
import { useCallback, useEffect, useState } from "react";
import { newVerification } from "@/actions/user/newVerification";
import { ErrorMessage } from "@/components/info/ErrorMessage";
import { SuccessMessage } from "@/components/info/SuccessMessage";

export const NewVerificationForm = () => {
  const [error, setError] = useState<string | undefined>();
  const [succes, setSucces] = useState<string | undefined>();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const onSubmit = useCallback(() => {
    if (succes || error) return;

    if (!token) {
      setError("No token provided!");
      return;
    }

    newVerification(token)
      .then((data) => {
        setSucces(data.success);
        setError(data.error);
      })
      .catch(() => {
        setError("Something went wrong!");
      });
  }, [token, succes, error]);

  useEffect(() => {
    onSubmit();
  }, [onSubmit]);

  return (
    <CardWrapper
      captionLabel="Verification"
      headerLabel="Confirming your verification"
      backButtonLabel="Back to sign-in"
      backButtonHref="/auth/signin"
      customClassName="w-[400px] shadow-md border-1"
    >
      <div className="flex w-full items-center justify-center">
        {!succes && !error && <BeatLoader color="#00BFFF" loading={true} />}
        <SuccessMessage message={succes} />
        {!succes && <ErrorMessage message={error} />}
      </div>
    </CardWrapper>
  );
};
