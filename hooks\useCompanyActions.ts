import { GetCompany, GetCompanyInfo, GetCompanyStats } from "@/actions/company/getCompany";
import { GetCompanyActionPayload, GetCompanyStatsActionPayload, CompanyStatsData, FetchedCompanyDetails } from "@/data/zod/zodSchema"; 
import { useQuery, useQueryClient } from "@tanstack/react-query";

const fetchCompanyData = async (
  companyId: string
): Promise<GetCompanyActionPayload> => {
  try {
    const companyDetails: FetchedCompanyDetails | null = await GetCompany(companyId);
    if (companyDetails) {
      return {
        success: true,
        company: companyDetails,
      };
    } else {
      return {
        success: false,
        company: null,
        error: `Company with ID ${companyId} not found.`, 
      };
    }
  } catch (error) {
    // console.error(`Error fetching company data for ID ${companyId}:`, error);
    return {
        success: false,
        company: null,
        error: error instanceof Error ? error.message : "Failed to fetch data",
    };
  }
};

const fetchCompanyStatsData = async (
  companyId: string
): Promise<GetCompanyStatsActionPayload> => {
  try {
    const statsDetails: CompanyStatsData | null = await GetCompanyStats(companyId);

    if (statsDetails) {
      return {
        success: true,
        stats: statsDetails,
      };
    } else {
      return {
        success: false,
        stats: null,
        error: `Company stats for ID ${companyId} not found or error fetching.`,
      };
    }
  } catch (error) {
    // console.error(`Error fetching company stats for ID ${companyId}:`, error);
    return {
      success: false,
      stats: null,
      error: error instanceof Error ? error.message : "Failed to fetch stats",
    };
  }
};

const fetchCompanyOtherInfo = async (companyId: string, userId?: string) => {
    try {
        const data = GetCompanyInfo(companyId, userId);
         return data;
    } catch (error) {
        return null;
    }
}

export function useCompanyActions(companyId: string, userId?: string) {
  const queryClient = useQueryClient();

  const companyQuery = useQuery<GetCompanyActionPayload, Error>({
    queryKey: ["companyData", companyId],
    queryFn: () => fetchCompanyData(companyId),
    enabled: !!companyId,
    staleTime: 30000,
  });

  const companyStatsQuery = useQuery<GetCompanyStatsActionPayload, Error>({
    queryKey: ["companyStats", companyId],
    queryFn: () => fetchCompanyStatsData(companyId),
    enabled: !!companyId,
    staleTime: 30000,
  });

const companyInfoQuery = useQuery({
    queryKey: ["companyInfo", companyId, userId],
    queryFn: () => fetchCompanyOtherInfo(companyId, userId),
    enabled: !!companyId && !!userId,
    staleTime: 30000,
  });

  return {
    isLoading: companyQuery.isLoading,
    isError: companyQuery.isError,
    company: companyQuery.data?.company,
    companyStatsQuery,
    stats: companyStatsQuery.data?.stats,
    isStatsLoading: companyStatsQuery.isLoading,
    isStatsError: companyStatsQuery.isError,
    companyTeam: companyInfoQuery.data?.[0],
    companyPlan: companyInfoQuery.data?.[1],
    companyInfoQuery
  };
};

