"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { GetUser } from "@/data/user/user";
import { compareHashedString, hashString } from "@/utils/hashingHelpers";
import { auth } from "@/lib/auth/auth";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const ChangePassword = async (user: any) => {
  const session = await auth();
      
      if (!session?.user?.id) {
          throw new Error("User not authenticated.");
      }
  
      // Arcjet protection
      await ensureServerActionProtection();
  

  if (!user) {
    return { error: "Missing user info!" };
  }

  const existingUser = await GetUser(user.userId);

  if (!existingUser) {
    return { error: "User does not exist!" };
  }

  // Check if password is correct
  const isPasswordCorrect = await compareHashedString(
    user.currentPassword,
    existingUser.password!
  );

  if (!isPasswordCorrect) {
    return { error: "Current password is not correct." };
  }

  try {
    if (user.newPassword) {
      await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          password: await hashString(user.newPassword),
        },
      });
    } else {
      return { error: "Password cannot be blank." };
    }
  } catch (err) {
    return { error: "Error updating password. error", err };
  }

  return { success: "Your password has been changed." };
};
