'use client';

import { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { MAX_USERNAME_LENGTH } from '@/lib/config';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/components/ui/use-mobile';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useUserActions } from '@/hooks/useJobSeekerActions';

interface UsernameEditorContentProps {
  resumeId: string;
  initialUsername: string;
  onClose: () => void;
  prefix?: string;
}

function UsernameEditorContent({
    resumeId,
    initialUsername,
    onClose,
  }: UsernameEditorContentProps) {
    const [newUsername, setNewUsername] = useState<string>(initialUsername);
    const [debouncedUsername, setDebouncedUsername] = useState<string>(initialUsername);
  
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
    const { updateUsernameMutation } = useUserActions();
  
    const isInitialUsername = newUsername === initialUsername;
  
    // Debounce username input
    useEffect(() => {
      if (debounceTimerRef.current) clearTimeout(debounceTimerRef.current);
  
      debounceTimerRef.current = setTimeout(() => {
        setDebouncedUsername(newUsername);
      }, 500);
  
      return () => {
        if (debounceTimerRef.current) clearTimeout(debounceTimerRef.current);
      };
    }, [newUsername]);
  
    // Use checkUsername query only when newUsername is different from initial
    const { checkUsername } = useUserActions(
      debouncedUsername,
      debouncedUsername !== "" && !isInitialUsername
    );
  
    const isAvailable = checkUsername.data?.available ?? null;
    const checking = checkUsername.isFetching;
  
    const isValid =
      /^[a-zA-Z0-9-]+$/.test(newUsername) &&
      newUsername.length > 0 &&
      !isInitialUsername &&
      isAvailable === true;
  
    const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
        .replace(/[^a-zA-Z0-9-]/g, '')
        .slice(0, MAX_USERNAME_LENGTH);
      setNewUsername(value);
    };
  
    const handleSave = async () => {
      try {
        await updateUsernameMutation.mutateAsync({ newUsername, resumeId});
        toast.success('Username updated successfully');
        onClose();
      } catch (error) {
        toast.error(`Failed to update username: ${error}`);
      }
    };
  
    return (
      <div className="flex flex-col gap-4 py-4">
        <div className="flex flex-col gap-2">
          <Label htmlFor="current-username">Current Username</Label>
          <div className="w-full overflow-hidden rounded bg-neutral-100 border-[0.5px] border-neutral-300">
            <Input
              id="current-username"
              type="text"
              value={initialUsername}
              disabled
              className="w-full p-3 text-sm text-neutral-500 border-none outline-none focus:ring-0 bg-transparent cursor-not-allowed"
            />
          </div>
        </div>
  
        <div className="flex flex-col gap-2">
          <Label htmlFor="new-username">New Username</Label>
          <div className="w-full overflow-hidden rounded bg-white border-[0.5px] border-neutral-300">
            <div className="flex items-center">
              <input
                id="new-username"
                type="text"
                value={newUsername}
                onChange={handleUsernameChange}
                maxLength={MAX_USERNAME_LENGTH}
                placeholder="Enter new username"
                className="w-full p-3 text-sm text-[#5d5d5d] border-none outline-none focus:ring-0 bg-transparent"
              />
              <div className="pr-3">
                {isInitialUsername ? null : checking ? (
                  <div className="w-4 h-4 rounded-full border-2 border-gray-300 border-t-primary animate-spin" />
                ) : isAvailable === true ? (
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20 6L9 17L4 12"
                      stroke="#009505"
                      strokeWidth="1.3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : isAvailable === false ? (
                  <X className="w-5 h-5 text-[#950000]" />
                ) : null}
              </div>
            </div>
          </div>
        </div>
  
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!isValid || updateUsernameMutation.isPending}
          >
            {updateUsernameMutation.isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
    );
  }

export default function UsernameEditorView({
  resumeId,
  initialUsername,
  isOpen,
  onClose,
  prefix = 'athena.edisonaix.com/',
}: {
  resumeId: string;
  initialUsername: string;
  isOpen: boolean;
  onClose: () => void;
  prefix?: string;
}) {
  const isMobile = useIsMobile();

  if (!isMobile) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Username</DialogTitle>
          </DialogHeader>
          <UsernameEditorContent
            resumeId={resumeId}
            initialUsername={initialUsername}
            onClose={onClose}
            prefix={prefix}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Edit Username</DrawerTitle>
        </DrawerHeader>
        <UsernameEditorContent
          resumeId={resumeId}
          initialUsername={initialUsername}
          onClose={onClose}
          prefix={prefix}
        />
      </DrawerContent>
    </Drawer>
  );
}
