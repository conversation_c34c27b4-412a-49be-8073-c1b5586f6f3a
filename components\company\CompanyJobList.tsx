"use client";

import { CopyLinkMenuItem } from "@/components/general/CopyLink";
import { QuestionTooltip } from "@/components/info/InfoTooltip";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@radix-ui/react-dropdown-menu";
import { EyeIcon, PenBoxIcon, XCircleIcon } from "lucide-react";
import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { AppliedJobsDialog } from "./AppliedJobsDialog";
import { ResumeMatchesDialog } from "./ResumeMatchesDialog";

export function CompanyJobList({ data }: any) {
    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="text-2xl">Job Lists</CardTitle>
                    <CardDescription>
                        <div className="flex items-center justify-between gap-2">
                            <span>Manage job listings and applications here.</span>
                            <Link 
                                className={buttonVariants({ size: "lg" })} 
                                href="/home/<USER>/job/post"
                            >
                                Post a Job
                            </Link>
                        </div>
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                            <TableHead></TableHead>
                            <TableHead>Company</TableHead>
                            <TableHead>Job Title</TableHead>
                            <TableHead>
                                Matched <QuestionTooltip 
                                content="Number of candidates that match your job requirements based on our AI matching algorithm with scores of 70% or higher."
                                />
                                </TableHead>
                            <TableHead>
                                Applied 
                                <QuestionTooltip 
                                content="Number of candidates that have applied to your job."
                                />
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Posted</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {data?.map((job: any) => (
                            <TableRow key={job.id} className="cursor-pointer hover:bg-muted/50">
                                <TableCell>
                                <Image
                                    src={job.company.logo || ""}
                                    alt={job.company.name || ""}
                                    width={32}
                                    height={32}
                                />
                                </TableCell>
                                <TableCell>{job.company.name}</TableCell>
                                <TableCell className="hover:underline cursor-pointer" onClick={() => {window.location.href = `/home/<USER>/job/${job.id}`}}>
                                    {job.jobTitle}
                                </TableCell>
                                <TableCell>
                                    <ResumeMatchesDialog
                                        count={job._count.resumeMatches || 0}
                                        resumeId=""
                                        companyId={job.companyId}
                                        jobId={job.id}
                                        userName=""
                                    />
                                </TableCell>
                                <TableCell>                                   
                                    <AppliedJobsDialog
                                        count={job._count.applications || 0}
                                        jobId={job.id}
                                        userName={job.jobTitle}
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                </TableCell>
                                <TableCell>
                                {job.status.charAt(0).toUpperCase() +
                                    job.status.slice(1).toLowerCase()}
                                </TableCell>
                                <TableCell>{job.createdAt.toLocaleDateString()}</TableCell>
                                <TableCell className="text-right">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="cursor-pointer">
                                        <MoreHorizontal />
                                    </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuItem asChild className="cursor-pointer">
                                        <Link href={`/home/<USER>/job/${job.id}`}>
                                        <EyeIcon className="mr-2" />
                                        Details
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild className="cursor-pointer">
                                        <Link href={`/home/<USER>/job/${job.id}/edit`}>
                                        <PenBoxIcon className="mr-2" />
                                        Edit Job
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild className="cursor-pointer">
                                        <CopyLinkMenuItem
                                        jobUrl={`${process.env.NEXT_PUBLIC_APP_URL}/public/job/${job.id}`}
                                        />
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem asChild className="cursor-pointer">
                                        <Link href={`/home/<USER>/job/${job.id}/delete`}>
                                        <XCircleIcon className="mr-2" />
                                        Delete
                                        </Link>
                                    </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                                </TableCell>
                            </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    );
}
