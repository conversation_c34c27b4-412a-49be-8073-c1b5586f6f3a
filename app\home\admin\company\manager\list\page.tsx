import { getJobList } from "@/actions/job/getJob";
import { CopyLinkMenuItem } from "@/components/general/CopyLink";
import { EmptyState } from "@/components/general/EmptyState";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardTitle,
  CardDescription,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@radix-ui/react-dropdown-menu";
import { FilePlusIcon, PenBoxIcon, XCircleIcon } from "lucide-react";
import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { MainPagination } from "@/components/general/MainPagination";
import { GetAdminUsers } from "@/actions/user/getUser";

export default async function AdminManagersPage({ searchParams, }: { searchParams: Promise<{ page?: string }>; }) {

  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50; // Or any other page size you prefer

  const list = await GetAdminUsers(currentPage, pageSize);

  // Assuming getJobList will be updated to return totalItems or you fetch it separately
  // For now, let's assume `list` might contain all items and we paginate client-side,
  // or `list.totalItems` is returned by `getJobList`
  const totalItems = list?.totalItems || list?.data?.length || 0; // Adjust based on getJobList's return
  const totalPages = Math.ceil(totalItems / pageSize);

  return (
    <>
      {list?.data?.length === 0 ? (
        <EmptyState
          title="No job posts found"
          description="You don't have job posts yet."
          buttonText="Post a job"
          href="/job"
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Company HR Managers</CardTitle>
            <CardDescription>
              Manage job listings and applications here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>About</TableHead>
                  <TableHead>SignedUp</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {list?.data?.map((user) => ( // If getJobList handles pagination, this is already the paginated list
                  <TableRow key={user.id}>
                    <TableCell className="flex items-center whitespace-nowrap gap-2">
                      <Image
                        src={user.image || "/icons/profile.png"}
                        alt={user.name || ""}
                        width={32}
                        height={32}
                        className="mr-2 rounded-full" // Added rounded-full and margin
                      /><span>{user.name || ""}</span>
                    </TableCell>
                    <TableCell>{user.role || ""}</TableCell>
                    <TableCell>{user.email || ""} {user.mobilePhone || ""}</TableCell>
                    <TableCell className="flex items-center whitespace-nowrap gap-4">
                      <Image
                        src={user.company?.logo || ""}
                        alt={user.company?.name || ""}
                        width={32}
                        height={32}
                      /><span>{user.company?.name || ""}</span>
                    </TableCell>
                    <TableCell className="max-w-sm truncate whitespace-normal break-words">
                        {user.company?.location || ""}: {user.company?.address || ""}
                    </TableCell>
                    <TableCell className="max-w-sm truncate whitespace-normal break-words">
                        {user.company?.about || ""}
                    </TableCell>
                    <TableCell>{user.createdAt.toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/manager/${user.id}`}>
                              <PenBoxIcon className="mr-2" />
                              Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/manager/add?id=${user.company?.id}`}> 
                              <FilePlusIcon className="mr-2" />
                              Add Admin
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/manager/${user.id}/edit`}>
                              <PenBoxIcon className="mr-2" />
                              Edit User
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link href={`/admin/company/user/${user.id}/delete`}>
                              <XCircleIcon className="mr-2" />
                              Delete User
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <MainPagination totalPages={totalPages} currentPage={currentPage} />
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
