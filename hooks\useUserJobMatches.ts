import {
  GetApplicationsJobMatches,
  GetUserJobMatches,
} from "@/actions/job/getJobMatches";
import { useQuery } from "@tanstack/react-query";

const fetchJobMatches = async (
  resumeId: string,
  companyId: string
): Promise<any[]> => {
  try {
    const data = await GetUserJobMatches(resumeId, companyId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

const fetchJobApplicationMatches = async (jobId: string): Promise<any[]> => {
  try {
    const data = await GetApplicationsJobMatches(jobId);
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error("Resume evaluation fetch error:", error);
    throw new Error(
      error instanceof Error
        ? error.message
        : "Failed to fetch resume evaluation"
    );
  }
};

export function useUserJobMatches(
  resumeId?: string,
  companyId?: string,
  jobId?: string
) {
  const matchesQuery = useQuery({
    queryKey: ["jobMatches", resumeId, companyId],
    queryFn: () => {
      if (!resumeId) return null;
      return fetchJobMatches(resumeId, companyId as string);
    },
    enabled: !!resumeId && !!companyId,
    staleTime: 30000,
  });

  const jobMatchesQuery = useQuery({
    queryKey: ["jobApplicationMatches", jobId],
    queryFn: () => {
      if (!jobId) return null;
      return fetchJobApplicationMatches(jobId as string);
    },
    enabled: !!jobId,
    staleTime: 30000,
  });

  return {
    matchesQuery,
    jobMatchesQuery,
  };
}
