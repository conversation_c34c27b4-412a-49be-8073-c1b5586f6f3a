import { EmptyState } from "@/components/general/EmptyState";
import { auth } from "@/lib/auth/auth";
import { getJobApplicantsCount } from "@/actions/job/getJob";
import { getCompanyJobList } from "@/actions/company/getCompany";
import { CompanyJobApplications } from "@/components/company/CompanyJobApplications";

export default async function CompanyApplicationsPage({ searchParams, }: { searchParams: Promise<{ page?: string }>; }) {
  const session = await auth();
  const params = await searchParams;
  const currentPage = Number(params?.page) || 1;
  const pageSize = 50;

  const data = await getCompanyJobList(session?.user?.companyId as string);
  
  // Get shortlist counts for all jobs
  const applicantsCounts = await Promise.all(
    data.map(job => getJobApplicantsCount(job.id))
  );
  
  // Add shortlist counts to job data
  const jobsWithApplicantstlistCounts = data.map((job, index) => ({
    ...job,
    applicantsCount: applicantsCounts[index] || 0
  })).filter(job => job.applicantsCount > 0); // Only show jobs with applicants

  // Pagination logic
  const totalPages = Math.ceil(jobsWithApplicantstlistCounts.length / pageSize);
  const paginatedJobs = jobsWithApplicantstlistCounts.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <>
      {jobsWithApplicantstlistCounts?.length === 0 ? (
        <div className="grid grid-cols-1 mt-5 gap-4">
          <h1 className="text-2xl font-semibold">Job Applications</h1>
          <EmptyState
            title="No applicants found"
            description="You don't have job applicants yet."
            buttonText="Go to Job List"
            href="/home/<USER>/job/list"
          />
        </div>
      ) : (
        <div className="space-y-6">
          <CompanyJobApplications 
            paginatedJobs={paginatedJobs}
            currentPage={currentPage}
            totalPages={totalPages}
          />      
        </div>
      )}
    </>
  );
}


