"use server";

import { GetUserResume } from "@/actions/resume/userResumeActions";
import { GetUserIdByUsername } from "@/actions/user/getUser";

export async function getUserData(username: string) {
  const data = await GetUserIdByUsername(username);
  const user_id = data?.userId as string;

  if (!user_id) {
    return { user_id: undefined, resume: undefined };
  }

  const resume = await GetUserResume(user_id);

  if (!resume?.resumeData || resume.status !== "LIVE") {
    return { user_id, resume: undefined };
  }

  return { user_id, resume };
}
