import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useMemo } from "react";
import { TextEditor } from "@/components/general/TextEditor";

interface WorkExperience {
  title?: string | undefined;
  company?: string | undefined;
  employmentType?: string | undefined;
  location?: string | undefined;
  description?: string | undefined; // Changed to string for TextEditor
  highlights?: string[] | undefined;
  technologies?: string[] | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  _tempId?: string; // Assuming _tempId is used here from EditResume
}

interface WorkExperienceFieldProps {
  work: WorkExperience;
  index: number;
  onUpdate: (index: number, updatedWork: WorkExperience) => void;
  onDelete: (index: number) => void;
}

export const WorkExperienceField: React.FC<WorkExperienceFieldProps> = ({
  work,
  index,
  onUpdate,
  onDelete,
}) => {
  // Adapt the 'field' prop for Description TextEditor
  const fieldForDescriptionEditor = useMemo(() => ({
    value: work.description || "",
    onChange: (newContent: string) => {
      onUpdate(index, {
        ...work,
        description: newContent,
      });
    },
    onBlur: () => {},
    ref: () => {},
  }), [work, index, onUpdate]);
  
  return (
    <div className="relative p-4 border rounded-md group">
      <button
        type="button"
        title="Delete Work Experience"
        className="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors"
        onClick={() => onDelete(index)}
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="md:col-span-2">
          <Label
            htmlFor={`work-title-${index}`}
            className="text-sm font-medium"
          >
            Job Title
          </Label>
          <Input
            id={`work-title-${index}`}
            value={work.title}
            onChange={(e) => {
              onUpdate(index, {
                ...work,
                title: e.target.value,
              });
            }}
            placeholder="Job Title"
            required
          />
        </div>

        <div>
          <Label
            htmlFor={`work-company-${index}`}
            className="text-sm font-medium"
          >
            Company
          </Label>
          <Input
            id={`work-company-${index}`}
            value={work.company}
            onChange={(e) => {
              onUpdate(index, {
                ...work,
                company: e.target.value,
              });
            }}
            placeholder="Company"
            required
          />
        </div>

        <div>
          <Label
            htmlFor={`work-employment-type-${index}`}
            className="text-sm font-medium"
          >
            Employment Type
          </Label>
          <Input
            id={`work-employment-type-${index}`}
            value={work.location}
            onChange={(e) => {
              onUpdate(index, {
                ...work,
                employmentType: e.target.value,
              });
            }}
            placeholder="Full-time, Part-time, Contract, etc."
            required
          />
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor={`work-location-${index}`}
            className="text-sm font-medium"
          >
            Location
          </Label>
          <Input
            id={`work-location-${index}`}
            value={work.location}
            onChange={(e) => {
              onUpdate(index, {
                ...work,
                location: e.target.value,
              });
            }}
            placeholder="Location"
            required
          />
        </div>

        <div className="md:col-span-2">
          <Label className="text-sm font-medium">Date Range</Label>
          <DateRangePicker
            startDate={work.startDate ?? undefined}
            endDate={work.endDate ?? undefined}
            onStartDateChange={(date) => {
              onUpdate(index, {
                ...work,
                startDate: date,
              });
            }}
            onEndDateChange={(date) => {
              onUpdate(index, {
                ...work,
                endDate: date,
              });
            }}
          />
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor={`work-description-${index}`}
            className="text-sm font-medium"
          >
            Description
          </Label>          
          <TextEditor
            key={work._tempId || `work-description-${index}`} // Prefer _tempId
            field={fieldForDescriptionEditor}
          />
        </div>

        <div className="md:col-span-2">
          <Label
            htmlFor={`work-highlights-${index}`}
            className="text-sm font-medium"
          >
            Highlights
          </Label>
          <textarea
            id={`work-highlights-${index}`}
            className="w-full p-2 border rounded-md font-mono text-sm"
            value={Array.isArray(work.highlights) ? work.highlights.join('\n') : work.highlights || ''}
            onChange={(e) => {
              onUpdate(index, {
                ...work,
                highlights: e.target.value.split('\n'),
              });
            }}
            placeholder="Highlights"
            rows={3}
            required
          />
        </div>
      </div>
    </div>
  );
};
