"use client";

import { SaveUserProfilePicture } from "@/actions/file/saveFile";
import { UploadDropzone } from "@/utils/uploadthing";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CameraIcon, Upload } from "lucide-react";
import { useState } from "react";
import "@/styles/uploadthing.css";

export function UploadPicture({
  type,
  resumeId,
}: {
  type: number;
  resumeId?: string;
}) {
  const [open, setOpen] = useState(false);

  //1=profile pic, 2=resume pic
  const buttonText = (() => {
    switch (type) {
      case 1:
        return "Change Profile Picture";
      case 2:
        return "Change Resume Picture";
      default:
        return "";
    }
  })();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center justify-center gap-2 cursor-pointer"
          onClick={() => setOpen(true)}
        >
          <CameraIcon className="w-4 h-4" />
          <span className="text-sm text-muted-foreground">Change</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{buttonText}</DialogTitle>
          <DialogDescription>Upload your recent picture.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 justify-center">
          <UploadDropzone
            endpoint="imageUploader"
            className="custom-class h-60 cursor-pointer w-full"
            onClientUploadComplete={(res) => {
              // Do something with the response
              toast.success("Picture uploaded successfully.");

              // Create the file data directly from the response
              const data = res;
              if (data && data[0]) {
                SaveUserProfilePicture(type, data[0].ufsUrl, resumeId)
                  .then(() => {
                    toast.success("Picture saved successfully.");
                    // Close the dialog on successful upload
                    setOpen(false);
                  })
                  .catch((error) => {
                    console.error("Failed to save to database:", error);
                    toast.error("Failed to save file information");
                  });
              }
            }}
            onUploadError={(error: Error) => {
              toast.error(`ERROR! A problem was encountered. ${error.message}`);
            }}
            onUploadBegin={(name) => {
              // Do something once upload begins
              toast.info(`Uploading: ${name}`);
            }}
            onChange={(acceptedFiles) => {
              // Do something with the accepted files
            }}
            content={{
              allowedContent({ ready, isUploading }) {
                if (!ready) return "Checking what you allow";
                if (isUploading) return "Uploading your picture...";
                return <>Images (max size of 4MB per file)</>;
              },
            }}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
