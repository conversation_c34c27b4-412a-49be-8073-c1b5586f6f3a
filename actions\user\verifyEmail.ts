"use server";

import { auth } from "@/lib/auth/auth";;
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { SendEmail } from "@/lib/sendEmail";
import { DeleteVerificationToken, GetUser } from "@/data/user/user";
import { GenerateVerificationToken } from "@/data/site/tokens";

export async function verifyEmail(userId: string) {
  const session = await auth();

   if (!session?.user?.id) {
    return { error: "Error! User not authenticated." };
  }

  // Arcjet protection
  await ensureServerActionProtection();

  try {

    const existingUser = await GetUser(userId);

    if (existingUser) {
        if (!existingUser.emailVerified) {
            const verificationToken = await GenerateVerificationToken(existingUser.email!);
        
            if (verificationToken) {
                const name = existingUser.firstName + " " + existingUser.lastName;
                const subject = "Your verification token";
                const html = `
                            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                                <div>
                                <h2>Welcome, ${name}!</h2>
                                <p>Please verify your email by clicking the link below:</p>
                            </div>
                            <div>
                                <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/verification?token=${verificationToken.token}" 
                                    style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                                    Verify Email
                                </a>
                            </div>
                            <div>
                                <p>If you did not sign up for an account, please ignore this email. The token will expire and your information will be deleted.</p>
                            </div>
                            <div>
                                <p>Regards, <br /> Edison AIX Team</p>
                            </div>
                            </div>
                        `;
            
                try {
                    const sendingEmail = await SendEmail({
                        sendTo: existingUser.email!,
                        subject: subject,
                        html: html,
                    });
        
                    if (sendingEmail) {
                        return {
                            success: "Email sent! Please check your email and verify your registration.",
                        };
                    } else {
                        return {
                            error: "Failed to send email. Please try again later.",
                        };
                    }
                    
                } catch (error) {         
                    return {
                        error: `Failed to send email. Error: ${error}` 
                    };
                }
            }
      } else {
        await DeleteVerificationToken(existingUser.email!);
      }
    } else {    
        return {
          error: "User with provided ID not found.",
        };
    }
    
  } catch (error) {
      return {
            error: `Failed to send email. Error: ${error}` 
      };
  }
}
