import { ControllerRenderProps } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { jobListingDurationPricing } from "@/utils/jobListingDurationPricing";
import { Label } from "../ui/label";
import { Card } from "../ui/card";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import { CheckCircleIcon } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/utils/formatCurrency"; 

interface JobListingDurationProps {
  field: Pick<ControllerRenderProps, "value" | "onChange" | "onBlur" | "ref">;
  plan: string; // e.g., "free", "basic", "essential", "enterprise"
}

// Define the maximum duration (in days) allowed for each plan tier
const MAX_DURATION_BY_PLAN: { [key: string]: number } = {
  free: 30,
  basic: 60,
  essential: 90,
  enterprise: 120,
};
const DEFAULT_MAX_DURATION_FALLBACK = 0; // Fallback if plan is unrecognized

// Define the duration (in days) that is considered "included" with each plan tier
const INCLUDED_DURATION_BY_PLAN: { [key: string]: number } = {
  free: 30,
  basic: 60,
  essential: 90,
  enterprise: 120,
};

export function JobListingDuration({ field, plan }: JobListingDurationProps) {
  const normalizedPlan = plan ? plan.toLowerCase() : "free"; 
  const maxAllowedDurationForCurrentPlan = MAX_DURATION_BY_PLAN[normalizedPlan] ?? DEFAULT_MAX_DURATION_FALLBACK;

  return (
    <RadioGroup
      value={field.value?.toString()}
      onValueChange={(valueString) => {
        const selectedDays = parseInt(valueString);
        const isAttemptedOptionAllowed = selectedDays <= maxAllowedDurationForCurrentPlan;

        if (isAttemptedOptionAllowed) {
          field.onChange(selectedDays);
        } else {
          toast.info(
            `Please upgrade your plan to select ${selectedDays} days.`,
            {
              action: {
                label: "View Plans",
                onClick: () => {
                  // router.push('/pricing'); // Example: navigate to pricing page
                  console.log("User clicked View Plans from toast: navigate to /pricing");
                },
              },
            }
          );
          // Do not change field.value if the option is not allowed
        }
      }}
    >
      <div className="flex flex-col gap-2">
        {jobListingDurationPricing.map((duration) => {
          const currentDurationDays = duration.days;
          const isAllowedForCurrentPlan = currentDurationDays <= maxAllowedDurationForCurrentPlan;
          const isSelected = field.value === currentDurationDays;

          const planIncludedDuration = INCLUDED_DURATION_BY_PLAN[normalizedPlan];
          const isExactPlanBenefit = currentDurationDays === planIncludedDuration;
          const isLowerTierBenefit = currentDurationDays < planIncludedDuration;

          return (
            <div key={duration.days} className="flex relative">
              <Label
                htmlFor={duration.days.toString()}
                className="flex flex-col w-full cursor-pointer"
              >
                <Card
                  className={cn(
                    "p-4 border transition-all w-full",
                    isSelected && isAllowedForCurrentPlan
                      ? "border-primary bg-primary/10 shadow-md" 
                      : !isAllowedForCurrentPlan
                      ? "border-dashed bg-muted/30 opacity-70 cursor-not-allowed" 
                      : "hover:bg-secondary hover:shadow-sm"
                  )}
                >
                  <div className="flex items-center space-x-2 w-full">
                    <RadioGroupItem
                      value={duration.days.toString()}
                      id={duration.days.toString()}
                      className="sr-only"
                      disabled={!isAllowedForCurrentPlan}
                    />
                    <div className="flex justify-between items-center w-full">
                      <div>
                        <p className="text-base font-semibold">
                          {duration.days} Days
                        </p>
                        <div className="flex items-center gap-2">
                          <p className="text-sm text-muted-foreground">
                            {duration.description}
                          </p>
                          {isExactPlanBenefit ? (
                            <span className="text-xs text-primary font-semibold">
                              (Your current plan)
                            </span>
                          ) : isLowerTierBenefit ? (
                            <span className="text-xs text-green-600 font-semibold">
                              (Included with your plan)
                            </span>
                          ) : duration.price !== undefined && duration.price > 0 ? (
                            <span className="text-sm text-muted-foreground font-semibold">
                              ({formatCurrency(duration.price, 'USD')})
                            </span>
                          ) : null // Don't display anything extra if price is 0 and not the included benefit
                          }
                        </div>
                      </div>
                      <div className="flex items-center justify-center" style={{ minWidth: '80px' }}>
                        {isAllowedForCurrentPlan ? (
                          isSelected ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-600" />
                          ) : (
                            <div className="h-5 w-5" />
                          )
                        ) : (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault(); 
                              e.stopPropagation();
                              toast.info(
                                `Upgrade to select ${currentDurationDays} days.`,
                                { action: { label: "View Plans", onClick: () => console.log("Navigate to /pricing") } }
                              );
                            }}
                            className="text-primary hover:text-primary-foreground hover:bg-primary text-xs h-auto px-2 py-1 rounded-md cursor-pointer"
                          >
                            Upgrade
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              </Label>
            </div>
          );
        })}
      </div>
    </RadioGroup>
  );
}
