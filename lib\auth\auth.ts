import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/lib/prisma/prismaClient";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google";
import GitHub from "next-auth/providers/github";
import LinkedIn from "next-auth/providers/linkedin";
import { LoginSchema } from "@/data/zod/zodSchema";
import {
  GetUserByEmail,
  GetUserByHashedEmail,
} from "@/data/user/user";
import { compareHashedString, hashEmail } from "@/utils/hashingHelpers";
import { encryptToBuffer } from "@/utils/security/encryptionHelper";
import { ZodError } from "zod";
import { GetTwoFactorConfirmationByUserId } from "@/data/site/twoFactorConfirmation";
import { UserRole, UserType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { ExtendedAdapterUser } from "@/next-auth";
import { GetUserById } from "@/actions/user/getUser";

export const { handlers, signIn, signOut, auth } = NextAuth({
  secret: process.env.AUTH_SECRET,
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  events: {
    async signOut(params) { // params is a union type depending on session strategy
        // console.log("Auth.js SignOut Event Triggered. Full Params:", JSON.stringify(params, null, 2));

        // For JWT strategy (which you are using)
        if ("token" in params && params.token) {
          const token = params.token; // params.token is of type JWT | null
            //   console.log(`User with token sub: ${token.sub} (ID: ${token.id}) has signed out from the server (JWT strategy).`);
            //   console.log("Full token object received in signOut event:", JSON.stringify(token, null, 2));
            // 'session' is not directly passed with JWT strategy in the signOut event.
            // The 'token' contains the data that would have formed the session.
        }
            // For Database strategy (not active, but good for completeness)
        else if ("session" in params && params.session) {
          const session = params.session; // params.session is of type AdapterSession | null
            //   console.log(`User session ${session.sessionToken} associated with User ID: ${session.userId} has signed out (Database strategy).`);
            //   console.log("Full session object received in signOut event:", JSON.stringify(session, null, 2));
        } else {
            //   console.log("Neither token (for JWT strategy) nor session (for DB strategy) was available in signOut event params, or they were null/undefined.");
        }

        // Existing or other server-side actions here
        // Example: if ("token" in params && params.token) { /* use params.token.id for logging */ }
    }
  },
  providers: [
    Credentials({
      credentials: {
        email: {},
        password: {},
      },
      authorize: async (credentials) => {
        try {
          const validatedFields = LoginSchema.safeParse(credentials);

          if (validatedFields.success) {
            const { email, password } = validatedFields.data;
            const user = await GetUserByEmail(email);

            if (!user || !user.password) return null;

            const isPasswordCorrect = await compareHashedString(
              password,
              user.password
            );

            if (isPasswordCorrect) {
              // Decrypt fields expected to be strings
              const decryptedUser = {
                ...user,
                email: safeDecrypt(user.email),
                name: safeDecrypt(user.name),
                firstName: safeDecrypt(user.firstName),
                lastName: safeDecrypt(user.lastName),
                image: safeDecrypt(user.image),
                companyId: user.companyId || null,
                companyName: user.company?.name || null,
                role: user.role ? user.role : UserRole.USER,
                userType: user.userType ? user.userType : UserType.JOB_SEEKER,
                onboarded: user.onboarded ? user.onboarded : false,
                planSet: user.planSet ? user.planSet : false,
                verified: user.emailVerified ? true : false,
              };

              return decryptedUser;
            }
          }
        } catch (error) {
          if (error instanceof ZodError) {
            // Return `null` to indicate that the credentials are invalid
            return null;
          }
        }
        return null;
      },
    }),
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    }),
    LinkedIn({
      clientId: process.env.LINKEDIN_CLIENT_ID as string,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET as string,
    }),
  ],
  callbacks: {
    async jwt({ token, user, account, trigger, session: clientUpdateData }) {
      // On initial sign-in (credentials or OAuth) or account linking
      if (user) {
        token.id = user.id; // Always present

        // If 'user' comes from our Credentials provider's 'authorize'
        if (account?.provider === "credentials") {
          // 'user' is the 'decryptedUser' object, fields are already strings/primitives
          const credUser = user as ExtendedAdapterUser & { email: string, name?: string | null, image?: string | null };
          token.email = credUser.email;
          token.name = credUser.name;
          token.role = credUser.role;
          token.userType = credUser.userType;
          token.onboarded = credUser.onboarded;
          token.planSet = credUser.planSet;
          token.verified = credUser.verified;
          token.companyId = credUser.companyId;
        } else if (account) { // For OAuth, account should be present
          // 'user' comes from OAuth (via PrismaAdapter.getUser/createUser/updateUser)
          // Fields like email, name, image from Prisma will be Buffers if encrypted in DB
          // We need to fetch the user from DB to decrypt and get all necessary fields.
          // This is a one-time fetch during the OAuth sign-in process for JWT population.
          const dbUser = await prisma.user.findUnique({
            where: { id: user.id }
            // No longer need to include: { company: true } just for companyName here
          });

          if (dbUser) {
            token.email = safeDecrypt(dbUser.email);
            token.name = dbUser.name ? safeDecrypt(dbUser.name) : null;
            token.role = dbUser.role || UserRole.USER;
            token.userType = dbUser.userType || UserType.JOB_SEEKER;
            token.onboarded = dbUser.onboarded || false;
            token.planSet = dbUser.planSet || false;
            token.verified = dbUser.emailVerified ? true : false;
            token.companyId = dbUser.companyId || null;
          }
        }
      }

      // Handle token updates if `trigger: "update"` is used (e.g., client calls update())
      if (trigger === "update" && clientUpdateData?.user) {
        // console.log("AUTH.TS JWT CALLBACK (update trigger) - clientUpdateData:", JSON.stringify(clientUpdateData, null, 2));

        const updatedUserData = clientUpdateData.user;

        // Access properties from the nested updatedUserData object
        if (updatedUserData.name !== undefined) token.name = updatedUserData.name;
        if (updatedUserData.email !== undefined) token.email = updatedUserData.email;
        if (updatedUserData.role !== undefined) token.role = updatedUserData.role as UserRole;
        if (updatedUserData.userType !== undefined) token.userType = updatedUserData.userType as UserType;
        if (updatedUserData.onboarded !== undefined) token.onboarded = updatedUserData.onboarded as boolean;
        if (updatedUserData.planSet !== undefined) token.planSet = updatedUserData.planSet as boolean;
        if (updatedUserData.verified !== undefined) token.verified = updatedUserData.verified as boolean;
        if (updatedUserData.companyId !== undefined) token.companyId = updatedUserData.companyId as string | null;
      }

    //   console.log("AUTH.TS JWT CALLBACK - Final token before return:", JSON.stringify(token, null, 2));
      return token;
    },
    async session({ session, token }) {
        // console.log("AUTH.TS SESSION CALLBACK - Received token:", JSON.stringify(token, null, 2));
        
      if (token && session.user) {
        session.user.id = (token.id as string) ?? (token.sub as string);
        session.user.email = token.email as string;
        session.user.name = token.name as string | null;
        session.user.image = null; // Explicitly set to null as it's removed from token
        session.user.role = token.role as UserRole;
        session.user.userType = token.userType as UserType;
        session.user.onboarded = token.onboarded as boolean;
        session.user.planSet = token.planSet as boolean;
        session.user.verified = token.verified as boolean;
        session.user.companyId = token.companyId as string | null;
        session.user.companyName = null; // Explicitly set to null as it's removed from token
      }

    // console.log("AUTH.TS SESSION CALLBACK - Returning Session:", JSON.stringify(session, null, 2));

      return session;
    },
    async signIn({ user, account }) {
      // 1. Handle Credentials Provider
      if (account?.provider === "credentials") {
        // The `authorize` callback has already validated credentials.
        // Additional checks like 2FA or email verification specific to credentials
        // should ideally be in `authorize` or here if `user` (from authorize) is available.
        // For now, assuming `authorize` handles all necessary checks for credentials.
        return true;
      }

      // 2. Handle OAuth Providers
      // Ensure it's an OAuth provider with a valid account object
      if (!account?.provider || !user?.id) {
        // If account or provider is missing, or user.id (expected from OAuth) is missing, block.
        return false;
      }

      let existingUser = user.id ? await GetUserById(user.id as string) : null;
      
      if (!existingUser && user.email) {
        existingUser = (await GetUserByHashedEmail(
          await hashEmail(user.email as string)
        )) as typeof existingUser;
      }

      if (existingUser) {
        const placeholderEmail = `${account.provider}-${account.providerAccountId}@noemail.com`;
        const fakeEmailHash = await hashEmail(placeholderEmail);

        // Update email if the user had a placeholder email
        if (existingUser.data?.emailHash === fakeEmailHash && user.email) {
          await prisma.user.update({
            where: { id: existingUser.data?.id },
            data: {
              email: encryptToBuffer(user.email),
              emailHash: await hashEmail(user.email),
            },
          });
        }

        // Check if this provider is already linked
        const providerExists = existingUser.data?.accounts.some(
          (acc) => acc.provider === account.provider
        );

        if (!providerExists) {
          // Link the new provider to the existing user
          await prisma.account.create({
            data: {
              userId: existingUser.data?.id as string,
              type: account.type,
              provider: account.provider,
              providerAccountId: account.providerAccountId,
              refresh_token: account.refresh_token,
              access_token: account.access_token,
              expires_at: account.expires_at,
              token_type: account.token_type,
              scope: account.scope,
            },
          });
        }
      } else {
        const userEmail =
          user.email ||
          `${account.provider}-${account.providerAccountId}@noemail.com`;

        // Extract first and last name safely
        let firstName: string | null = null;
        let lastName: string | null = null;

        if (user.name && typeof user.name === "string") {
          const parts = user.name.trim().split(" ");
          firstName = parts.shift() ?? null;
          lastName = parts.length > 0 ? parts.join(" ") : null;
        }

        // Ensure non-nullable fields have valid values
        firstName = firstName?.trim() || "";
        lastName = lastName?.trim() || "";

        // Create a new user if it doesn't exist
        await prisma.user.create({
          data: {
            id: user.id ?? uuidv4(),
            email: encryptToBuffer(userEmail),
            emailHash: await hashEmail(userEmail),
            name: encryptToBuffer(user.name || ""),
            firstName: encryptToBuffer(firstName),
            lastName: encryptToBuffer(lastName),
            image: user.image ? encryptToBuffer(user.image) : null,
            accounts: {
              create: {
                type: account.type,
                provider: account.provider,
                providerAccountId: account.providerAccountId,
                refresh_token: account.refresh_token,
                access_token: account.access_token,
                expires_at: account.expires_at,
                token_type: account.token_type,
                scope: account.scope,
                id_token: account.id_token,
              },
            },
          },
        });
      }

      // If we've reached here, it's an OAuth sign-in.
      // User has been found/created and account linked.
      // Email verification and 2FA for OAuth are typically handled by the provider
      // or would require specific logic here checking `existingUser.data` if you enforce it post-OAuth.
      return true;

      // The previous emailVerified and 2FA checks were largely bypassed for OAuth
      // and redundant for credentials. If specific post-OAuth checks are needed,
      // they would go here, operating on `existingUser.data`.
    },
  },
});
