"use server";

import * as z from "zod";
import { LoginSchema } from "@/data/zod/zodSchema";
import { signIn } from "@/lib/auth/auth"; // Assuming this is your next-auth signIn
import {
  DEFAULT_LOGIN_REDIRECT,
  ONBOARDING_PATH, // Import ONBOARDING_PATH
  adminRoute,      // Import adminRoute
  companyRoute,    recruiterRoute,    tenantRoute,    // Import companyRoute
  userRoute        // Import userRoute
} from "@/routes";
import { AuthError } from "next-auth";
import {
  GenerateVerificationToken,
  GenerateTwoFactorToken,
} from "@/data/site/tokens";
import { DeleteVerificationToken, GetUserByEmail } from "@/data/user/user";
import { GetTwoFactorTokenByEmail } from "@/data/site/twoFactorToken";
import { prisma } from "@/lib/prisma/prismaClient";
import { GetTwoFactorConfirmationByUserId } from "@/data/site/twoFactorConfirmation";
import { v4 as uuidv4 } from "uuid";
import { SendEmail } from "@/lib/sendEmail";
import { hashEmail } from "@/utils/hashingHelpers";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { UserRole, UserType } from "@prisma/client";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";
import { UserStatus } from "@prisma/client";

export const signin = async (values: z.infer<typeof LoginSchema>) => {
  
    // Arcjet protection
  await ensureServerActionProtection();

  const validatedFields = LoginSchema.safeParse(values);
  let redirectTo = DEFAULT_LOGIN_REDIRECT;

  if (!validatedFields.success) {
    return { error: "Invalid fields!" };
  }

  const { email, password, code } = validatedFields.data;
  const hashedEmail = await hashEmail(email);

  const existingUser = await GetUserByEmail(email);

  if (!existingUser || !existingUser.email || !existingUser.password) {
    return { error: "Email does not exist." };
  }

  if (existingUser.status === UserStatus.INACTIVE || existingUser.status === UserStatus.BLOCKED) {
    return { error: "Email was set to inactive or blocked from signing in. Please contact support." };
  }

  if (!existingUser.emailVerified) {
    const verificationToken = await GenerateVerificationToken(email);

    if (verificationToken) {
      const name = safeDecrypt(existingUser.name);
      const subject = "Your verification token";
      const html = `
                  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                      <div>
                      <h2>Welcome, ${name}!</h2>
                      <p>Please verify your email by clicking the link below:</p>
                  </div>
                  <div>
                      <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/verification?token=${verificationToken.token}" 
                         style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                          Verify Email
                      </a>
                  </div>
                  <div>
                      <p>If you did not sign up for an account, please ignore this email. The token will expire and your information will be deleted.</p>
                  </div>
                  <div>
                      <p>Regards, <br /> Edison AIX Team</p>
                  </div>
                  </div>
              `;

      await SendEmail({
        sendTo: email,
        subject: subject,
        html: html,
      });
    }

    // return {
    //   success:
    //     "Email sent! Please check your email and verify your registration.",
    // };
  } else {
    await DeleteVerificationToken(email);
  }

  if (existingUser.isTwoFactorEnabled && existingUser.email) {
    if (code) {
      const twoFactorToken = await GetTwoFactorTokenByEmail(hashedEmail);
      if (!twoFactorToken) {
        return { error: "Invalid code." };
      }

      if (twoFactorToken.token !== code) {
        return { error: "Invalid code." };
      }

      const hasExpired = new Date(twoFactorToken.expires) < new Date();
      if (hasExpired) {
        return { error: "Code expired." };
      }

      await prisma.userTwoFactorToken.delete({
        where: { id: twoFactorToken.id },
      });

      const existingConfirmation = await GetTwoFactorConfirmationByUserId(
        existingUser.id
      );
      if (existingConfirmation) {
        await prisma.userTwoFactorConfirmation.delete({
          where: {
            id: existingConfirmation.id,
          },
        });
      }

      await prisma.userTwoFactorConfirmation.create({
        data: {
          id: uuidv4(),
          userId: existingUser.id,
        },
      });
    } else {
      const twoFactorToken = await GenerateTwoFactorToken(hashedEmail);

      if (twoFactorToken) {
        const name = safeDecrypt(existingUser.name);
        const subject = "Your two factor code";
        const html = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div>
                    <h2>Dear ${name},</h2>
                    <p>Here is your code for the two factor authentication:</p>
                </div>
                <div>
                    <p style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                       ${twoFactorToken.token}
                    </a>
                </div>
                <div>
                    <p>If you did not request for this code, please ignore this email. The token will expire and your information will be deleted.</p>
                </div>
                <div>
                    <p>Regards, <br /> Edison AIX Team</p>
                </div>
                </div>
            `;

        await SendEmail({
          sendTo: email,
          subject: subject,
          html: html,
        });
      }

      return { twoFactor: true };
    }
  }

  if (existingUser) {
    if (existingUser.onboarded === false) {
      redirectTo = ONBOARDING_PATH;
    } else {
      if (existingUser.userType === UserType.COMPANY) {
        redirectTo = `${companyRoute}/dashboard`;
      } else if (existingUser.userType === UserType.JOB_SEEKER) {
        redirectTo = `${userRoute}/dashboard`;
      } else if (existingUser.userType === UserType.RECRUITER) {
        redirectTo = `${recruiterRoute}/dashboard`;
      } else if (existingUser.userType === UserType.TENANT) {
        redirectTo = `${tenantRoute}/dashboard`;
      } else if (existingUser.role === UserRole.SUPERADMIN && existingUser.userType === UserType.OWNER) {
        redirectTo = `${adminRoute}/dashboard`;
      }
    }
  }

  try {
    await signIn("credentials", {
      email,
      password,
      redirectTo: redirectTo,
    });
  } catch (error) {
    if (error instanceof AuthError) {
      switch (error.type) {
        case "CredentialsSignin":
          return { error: "Invalid credentials" };
        default:
          return { error: error.message };
      }
    }

    throw error;
  }
};
