"use client";

import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Suspense, useEffect, useState } from "react";
import { JobSeekerSchema } from "@/data/zod/zodSchema";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, XIcon } from "lucide-react";
import Image from "next/image";
import { UploadButton } from "@/utils/uploadthing";
import { createJobSeeker } from "@/actions/user/createJobSeeker";
import PdfImage from "@/public/icons/pdf.png";
import DocxImage from "@/public/icons/docx.png";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { countryList } from "@/data/location/countryList";
import "@uploadthing/react/styles.css";
import { toast } from "sonner";
import { useUser } from "@/hooks/useUser";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

export function JobSeekerForm() {
  const { data: session, status, update } = useSession();
  const [pending, setPending] = useState(false);
  const [onboarded, setOnboarded] = useState(false);
  const [docType, setDocType] = useState("");
  const { firstName, lastName, loading } = useUser(); // Keep original name
  const [uploadResult, setUploadResult] = useState("");
  const router = useRouter();


  const form = useForm<z.infer<typeof JobSeekerSchema>>({
    resolver: zodResolver(JobSeekerSchema),
    defaultValues: {
      firstname: "",
      lastname: "",
      title: "",
      location: "",
      about: "",
      resume: "",
      portfolio: "",
      linkedin: "",
      github: "",
      writing: "",
    },
  });

  useEffect(() => {
    if (!loading && firstName !== undefined && lastName !== undefined) { // Use original name
      form.reset({
        firstname: firstName || "",
        lastname: lastName || "",
        title: "",
        location: "",
        about: "",
        resume: "",
        portfolio: "",
        linkedin: "",
        github: "",
        writing: "",
      });
    } // Dependency on 'form' is standard for react-hook-form reset
  }, [loading, firstName, lastName, form]);

  async function onSubmit(data: z.infer<typeof JobSeekerSchema>) {
    try {
      setPending(true);

      const response = await createJobSeeker(data, uploadResult);

      if (response) {
        if (response.success) {
            setOnboarded(true);
            toast.success(response.success);
            if (session?.user) {
                await update({ 
                    ...session,
                    user: {
                        ...session.user,
                        onboarded: true,
                    }
                });
            }            
            window.location.href = "/home/<USER>/dashboard";
        } else if (response.error) {
          toast.error(response.error);
        }
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error(`Unexpected error. ${error}`);
    } finally {
      setPending(false);
    }
  }

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    !onboarded && (
        
        <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
            <div className="text-center">
            <h2 className="text-2xl font-bold">Your Profile</h2>
            <br />
            <p className="text-muted-foreground text-left">
                Share details about yourself so companies can get to know you
                better.
            </p>
            <p className="text-muted-foreground text-left text-sm pt-4 border-2 rounded-lg p-2">
                We adhere to the Data Privacy regulations worldwide. All your
                personal and sentive data, including links to your portfolio and
                social media profiles, are encrypted and stored securely.
            </p>
            </div>
            <div className="py-2 pb-2 text-muted-foreground text-sm">
            <p>
                Your first and last names are taken from your registration. You can
                edit them later on your account page.
            </p>
            <br />
            <span className="text-red-500 text-sm">*</span> Required fields
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
                control={form.control}
                name="firstname"
                render={({ field }) => (
                <FormItem>
                    <FormLabel>
                    First Name<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                    <Input {...field} placeholder="Your first name" />
                    </FormControl>
                    <FormMessage />
                </FormItem>
                )}
            />

            <FormField
                control={form.control}
                name="lastname"
                render={({ field }) => (
                <FormItem>
                    <FormLabel>
                    Last Name<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                    <Input {...field} placeholder="Your last name" />
                    </FormControl>
                    <FormMessage />
                </FormItem>
                )}
            />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                <FormItem>
                    <FormLabel>
                    Current Location<span className="text-red-500">*</span>
                    </FormLabel>
                    <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    >
                    <FormControl>
                        <SelectTrigger className="w-full cursor-pointer">
                        <SelectValue placeholder="Select Country" />
                        </SelectTrigger>
                    </FormControl>
                    <SelectContent className="p-4">
                        <SelectGroup>
                        {countryList.map((country) => (
                            <SelectItem
                            key={country.code}
                            value={country.code}
                            className="cursor-pointer"
                            >
                            <span className="emoji">{country.flagEmoji}</span>
                            <span className="p-2">{country.name}</span>
                            </SelectItem>
                        ))}
                        </SelectGroup>
                    </SelectContent>
                    </Select>
                    <FormMessage />
                </FormItem>
                )}
            />

            <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                <FormItem>
                    <FormLabel>
                    Current or Aspired Job Title
                    </FormLabel>
                    <FormControl>
                    <Input placeholder="e.g. Frontend Developer" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
                )}
            />
            </div>

            <FormField
            control={form.control}
            name="about"
            render={({ field }) => (
                <FormItem>
                <FormLabel>Short Bio</FormLabel>
                <FormControl>
                    <Textarea
                    placeholder="Tell us about yourself. Enter or paste text."
                    {...field}
                    />
                </FormControl>
                <FormMessage />
                </FormItem>
            )}
            />

            <FormField
            control={form.control}
            name="resume"
            render={({ field }) => (
                <FormItem>
                <FormLabel>
                    Resume (PDF or Word Document)
                    <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                    <div className="flex justify-center items-center border-1 border-dashed p-2">
                    {field.value ? (
                        <div className="flex justify-center items-center">
                        <div className="relative w-fit">
                            <Image
                            src={
                                docType === "application/pdf" ? PdfImage : DocxImage
                            }
                            alt="Resume"
                            width={100}
                            height={100}
                            className="rounded-lg"
                            />
                            <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute -top-2 -right-2"
                            onClick={() => field.onChange("")}
                            >
                            <XIcon className="size-4" />
                            </Button>
                        </div>
                        </div>
                    ) : (
                        <div className="w-full flex justify-center items-center h-[150px]">
                        <UploadButton
                            endpoint="docUploader"
                            onClientUploadComplete={(res) => {
                            setUploadResult(JSON.stringify(res));
                            setDocType(res[0].type);
                            field.onChange(res[0].ufsUrl);
                            toast.success("Resume uploaded successfully.");
                            }}
                            onUploadError={(error: Error) => {
                            toast.success(
                                `ERROR! A problem was encountered. ${error.message}`
                            );
                            }}
                            onUploadBegin={(name) => {
                            // Do something once upload begins
                            toast.info(`Uploading: ${name}`);
                            }}
                            appearance={{
                            button:
                                "ut-ready:bg-green-500 ut-uploading:cursor-not-allowed rounded-r-none bg-blue-500 bg-none after:bg-orange-400",
                            container:
                                "w-full p-4 flex-row rounded-md border-cyan-300",
                            allowedContent:
                                "flex h-8 flex-col items-center justify-center px-2 text-gray",
                            }}
                            content={{
                            button({ ready }) {
                                if (ready) return <div>Upload File</div>;
                                return "Getting ready...";
                            },
                            allowedContent({ ready, isUploading }) {
                                if (!ready) return "Checking what you allow";
                                if (isUploading) return "Please wait, uploading your resume...";
                                return `File you can upload: PDF or Word Document (max size of 1MB)`;
                            },
                            }}
                        />
                        </div>
                    )}
                    </div>
                </FormControl>
                <FormMessage />
                </FormItem>
            )}
            />
            <div className="flex justify-center mt-6">
            <Button
                type="submit"
                className="w-full cursor-pointer"
                disabled={pending}
                size="lg"
            >
                {pending ? "Submitting..." : "Complete Setup"}
            </Button>
            </div>
        </form>
        </Form>
    )
  );
}
