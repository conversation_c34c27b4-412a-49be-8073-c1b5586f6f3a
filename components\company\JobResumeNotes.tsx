"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useJobNotes } from "@/hooks/useJobNotes";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Image from "next/image";
import { MatchScoreCircle } from "./MatchScoreCircle";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { JobNoteSchema } from "@/data/zod/zodSchema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from "@/components/ui/form";
import { Textarea } from "../ui/textarea";
import { Input } from "../ui/input";
import { useState } from "react";
import { AddJobNote, DeleteJobNote } from "@/actions/job/jobNoteActions";
import { toast } from "sonner";
import { AlertDialogDelete } from "../general/AlertDialogDelete";
import { normalizeHeaders } from "@/utils/stringHelpters";

// Define a basic type for the resume match data, if it's part of JobNote
interface ResumeMatchDataType {
  id: string; 
  overall_score?: number | null;
  skills_match?: number | null;
  experience_alignment?: number | null;
  education_fit?: number | null;
  match_analysis?: string | null;
  createdAt?: string | Date;
}

export function JobResumeNotes({ 
  jobId,
  resumeId
}: { 
  jobId: string;
  resumeId: string;
}) {

    const ATHENA_AI_USER_ID = process.env.NEXT_PUBLIC_ATHENA_AI_USER_ID || "bnh8f18h13kzwvP6iaNhaab42f076a8757"; // Define Athena AI's user ID

    const { jobNotesQuery } = useJobNotes(jobId, resumeId);
    const [pending, setPending] = useState(false);
    const [deleting, setDeleting] = useState(false);

        // Initialize useForm hook unconditionally at the top level
    const form = useForm<z.input<typeof JobNoteSchema>>({
        resolver: zodResolver(JobNoteSchema),
            defaultValues: {
                jobId: jobId,
                resumeId: resumeId,
                note: "",
                overall_score: 0,
                skills_match: 0,
                experience_alignment: 0,
                education_fit: 0,
                other_notes: [],
                culturalFitNote: "",
                attitudeWorkEthicNote: "",
                softSkillsNote: "",
                problemSolvingNote: "",
                potentialLearningAgilityNote: "",
                professionalismPresentationNote: "",
                referencesReputationNote: "",
                passionInterestNote: "",
                availabilityFlexibilityNote: "",
                salaryExpectationsNote: "",
            },
      });

  const onSubmit: SubmitHandler<z.input<typeof JobNoteSchema>> = async (rawValues) => {
    setPending(true);
    try { 
        // Cast rawValues to its effective runtime type (z.infer) for type safety
      // when constructing the payload for AddJobNote.
      const validatedValues = rawValues as z.infer<typeof JobNoteSchema>;

      const otherNotesArray = [];
      if (validatedValues.culturalFitNote) {
        otherNotesArray.push({ note_type: "cultural", content: validatedValues.culturalFitNote });
      }
      if (validatedValues.attitudeWorkEthicNote) {
        otherNotesArray.push({ note_type: "attitude", content: validatedValues.attitudeWorkEthicNote });
      }
      if (validatedValues.softSkillsNote) {
        otherNotesArray.push({ note_type: "interpersonal", content: validatedValues.softSkillsNote });
      }
      if (validatedValues.problemSolvingNote) {
        otherNotesArray.push({ note_type: "thinking", content: validatedValues.problemSolvingNote });
      }
      if (validatedValues.potentialLearningAgilityNote) {
        otherNotesArray.push({ note_type: "agility", content: validatedValues.potentialLearningAgilityNote });
      }
      if (validatedValues.professionalismPresentationNote) {
        otherNotesArray.push({ note_type: "professionalism", content: validatedValues.professionalismPresentationNote });
      }
      if (validatedValues.referencesReputationNote) {
        otherNotesArray.push({ note_type: "reputation", content: validatedValues.referencesReputationNote });
      }
      if (validatedValues.passionInterestNote) {
        otherNotesArray.push({ note_type: "passion", content: validatedValues.passionInterestNote });
      }
      if (validatedValues.availabilityFlexibilityNote) {
        otherNotesArray.push({ note_type: "availability", content: validatedValues.availabilityFlexibilityNote });
      }
      if (validatedValues.salaryExpectationsNote) {
        otherNotesArray.push({ note_type: "expectations", content: validatedValues.salaryExpectationsNote });
      }

      const payload = {
        note: validatedValues.note,
        jobId: validatedValues.jobId,
        resumeId: validatedValues.resumeId,
        id: validatedValues.id,
        userId: validatedValues.userId,
        companyId: validatedValues.companyId,
        createdAt: validatedValues.createdAt,
        overall_score: validatedValues.overall_score,
        skills_match: validatedValues.skills_match,
        experience_alignment: validatedValues.experience_alignment,
        education_fit: validatedValues.education_fit,
        other_notes: otherNotesArray,
      };
      const result = await AddJobNote(payload);
      if (result?.success) {
        toast.success(result.success);
        form.reset(); // Clear the form
        jobNotesQuery.refetch(); // Refetch notes to show the new one
      } else {
        toast.error(result?.error || "Failed to add note.");
      }
    } catch (error) {
      console.error("Error adding note:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setPending(false);
    }
  }

  const onDelete = async (id: string) => {
        try {
            setPending(true);
            setDeleting(true);
            
            const result = await DeleteJobNote(id);
            if (result?.success) {
                toast.success(result.success);
                form.reset(); 
                jobNotesQuery.refetch(); 
            } else {
                toast.error(result?.error || "Failed to delete note.");
            }
        } catch (error) {
            console.error("Error adding note:", error);
            toast.error("An unexpected error occurred.");            
        } finally {
            setPending(false);
            setDeleting(false);
        }
  }

    // Extract AI match data from the first note's resumeMatches array, if available
  const aiMatchData: ResumeMatchDataType | undefined = jobNotesQuery.data?.[0]?.resume?.resumeMatches?.[0];
  const hasAiMatchNote = !!(aiMatchData && aiMatchData.match_analysis);
  const allNotes = jobNotesQuery.data || [];

  return (
    <div className="w-full max-h-[70vh] overflow-y-auto">
         {(!hasAiMatchNote && allNotes.length === 0) ? (          
            <div className="p-4">
                <p className="text-sm text-red-500">
                    No note available for this specific job and talent. Add a note!
                </p>
            </div>
        ):(
            <div className="flex w-full items-center gap-2 mt-1 mb-4">
                <Table className="table-fixed w-full">
                    <TableHeader className="text-xs font-bold">
                    <TableRow>
                        <TableHead style={{ width: '8%' }}>Date</TableHead>
                        <TableHead style={{ width: '50%' }}>Notes</TableHead>
                        <TableHead className="text-center" style={{ width: '4%' }}>Exp</TableHead>
                        <TableHead className="text-center" style={{ width: '4%' }}>Skl</TableHead>
                        <TableHead className="text-center" style={{ width: '4%' }}>Edu</TableHead>
                        <TableHead className="text-center" style={{ width: '4%' }}>Ave</TableHead>
                        <TableHead style={{ width: '10%' }}>Name</TableHead>
                        <TableHead className="text-center" style={{ width: '4%' }}>Del</TableHead>
                    </TableRow>
                    </TableHeader>
                    <TableBody>
                    {/* Display AI Resume Match Note First if available */}
                    {hasAiMatchNote && aiMatchData && (
                        <TableRow className="text-sm bg-sky-50" key={`ai-match-${aiMatchData.id}`}>
                            <TableCell className="text-xs" style={{ width: '8%', verticalAlign: 'top' }}>
                            {aiMatchData.createdAt ? new Date(aiMatchData.createdAt).toLocaleDateString() : 'N/A'}
                            </TableCell>
                            <TableCell style={{ width: '50%', verticalAlign: 'top' }} className="whitespace-normal break-words">
                            {aiMatchData.match_analysis}
                            </TableCell>
                            <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                <MatchScoreCircle score={aiMatchData.experience_alignment} />
                            </TableCell>
                            <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                <MatchScoreCircle score={aiMatchData.skills_match} />
                            </TableCell>
                            <TableCell className="font-medium text-center" style={{ width: '3.5%', verticalAlign: 'top' }}>
                                <MatchScoreCircle score={aiMatchData.education_fit} />
                            </TableCell>
                            <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                <MatchScoreCircle score={aiMatchData.overall_score} />
                            </TableCell>
                            <TableCell style={{ width: '10%', verticalAlign: 'top' }}>
                            <div className="flex items-center gap-2">
                                <Image
                                src="/icons/athena_avatar.png" 
                                alt="Athena AI"
                                width={24}
                                height={24}
                                className="rounded-full"
                                />
                                <span className="text-xs">Athena AI</span>
                            </div>
                            </TableCell>
                            <TableCell className="text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                            {/* No action items for this AI note */}
                            </TableCell>
                        </TableRow>
                    )}

                    {/* Display User-Added Notes / Other JobNotes */}
                    {allNotes.map((note, index) => {
                        const isNoteByAthenaAI = note.user?.id === ATHENA_AI_USER_ID;
                        
                        // Avoid displaying the main AI analysis again if it's also stored as a JobNote
                        if (hasAiMatchNote && isNoteByAthenaAI && note.note === aiMatchData?.match_analysis) {
                            return null; 
                        }

                        const displayName = isNoteByAthenaAI ? "Athena AI" : note.user?.firstName;
                        const displayImage = isNoteByAthenaAI ? "/icons/athena_avatar.png" : (note.user?.image || "/icons/profile.png");

                        return (
                            <TableRow
                            key={note.id}
                            className={index % 2 === 1 ? "bg-muted/30 text-sm" : "text-sm"}
                            >
                                <TableCell className="text-xs" style={{ width: '8%', verticalAlign: 'top' }}>{note.createdAt.toLocaleDateString()}</TableCell>
                                <TableCell
                                    style={{
                                        width: '50%',
                                        verticalAlign: 'top',
                                    }}
                                    className="whitespace-normal break-words"
                                >
                                    <div>{note.note}</div>
                                    {note.otherNotes && note.otherNotes.length > 0 && (
                                        <div className="mt-2 pt-1 border-t border-dashed border-gray-300">
                                            {note.otherNotes.map((otherNote: any, idx: any) => (
                                                <div key={idx} className="text-xs mt-1">
                                                    <span className="font-semibold">
                                                        {normalizeHeaders(otherNote.note_type)}:
                                                    </span>
                                                    <span className="ml-1">
                                                        {otherNote.content}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </TableCell>
                                <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                    {note.experience_alignment != null && note.experience_alignment > 0 ? (<MatchScoreCircle score={note.experience_alignment} />) : ("")}                               
                                </TableCell>
                                <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                    {note.skills_match != null && note.skills_match > 0 ? (<MatchScoreCircle score={note.skills_match} />) : ("")}
                                </TableCell>
                                <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                    {note.education_fit != null && note.education_fit > 0 ? (<MatchScoreCircle score={note.education_fit} />) : ("")}
                                </TableCell>
                                <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>
                                    {note.overall_score != null && note.overall_score > 0 ? (<MatchScoreCircle score={note.overall_score} />) : ("")}
                                </TableCell>
                                <TableCell style={{ width: '10%', verticalAlign: 'top' }}>
                                    <div className="flex items-center gap-2">
                                      <Image
                                        src={displayImage}
                                        alt={displayName || "User"}
                                        width={24}
                                        height={24}
                                        className="rounded-full"
                                        />
                                        <span className="text-xs">{displayName}</span>
                                    </div>
                                </TableCell>
                                <TableCell className="font-medium text-center" style={{ width: '4%', verticalAlign: 'top' }}>                        
                                    {!isNoteByAthenaAI ? ( 
                                        <AlertDialogDelete id={note.id} title="note" description="This action cannot be undone. This will permanently delete your note and remove your data from our servers." onDelete={onDelete} />
                                    ) : null}
                                </TableCell>
                            </TableRow>
                        );
                    })}
                    </TableBody>
                </Table>
            </div>  
        )}   

        <div className="flex flex-col w-fullmb-1 border-2 p-2 rounded-lg">
            {/* Add Note Form */}
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <div className="grid grid-cols-[4fr_1fr] gap-4 w-full item"> {/* Defines an 80%/20% column split */}
                        <div className="flex w-full">
                            <FormField
                                control={form.control}
                                name="note"
                                render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormControl>
                                    <Textarea
                                        placeholder="Add your own note/remark as well as your rating on top of the AI analysis. Overall is auto-computed."
                                        className="min-h-[80px] w-full"
                                        {...field}
                                    />
                                    </FormControl>
                                </FormItem>
                                )}
                            />                            
                        </div>
                        <div className="flex flex-col w-full gap-2">
                            <p className="text-sm font-semibold">
                                Optional:
                            </p>
                                <FormField
                                control={form.control}
                                name="experience_alignment"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormLabel className="text-xs text-muted-foreground">Experience Alignment (0-100)</FormLabel>
                                    <FormControl>
                                        <Input
                                        type="number"
                                        placeholder="Experience (0-100)"
                                        min={0}
                                        max={100}
                                        {...field}
                                        onChange={event => {
                                            const value = event.target.valueAsNumber;
                                            if (isNaN(value)) {
                                            field.onChange(0); // Handle NaN by setting to 0
                                            } else if (value < 0) field.onChange(0);
                                            else if (value > 100) field.onChange(100);
                                            else field.onChange(value); // Pass valid, clamped number
                                        }}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                                />
                                <FormField
                                control={form.control}
                                name="skills_match"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormLabel className="text-xs text-muted-foreground">Skills Match (0-100)</FormLabel>
                                    <FormControl>
                                        <Input
                                        type="number"
                                        placeholder="Skills Match (0-100)"
                                        min={0}
                                        max={100}
                                        {...field}
                                        onChange={event => {
                                            const value = event.target.valueAsNumber;
                                            if (isNaN(value)) {
                                            field.onChange(0); // Handle NaN by setting to 0
                                            } else if (value < 0) field.onChange(0);
                                            else if (value > 100) field.onChange(100);
                                            else field.onChange(value); // Pass valid, clamped number
                                        }}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                                />
                                <FormField
                                control={form.control}
                                name="education_fit"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormLabel className="text-xs text-muted-foreground">Education Fit (0-100)</FormLabel>
                                    <FormControl>
                                        <Input
                                        type="number"
                                        placeholder="Education (0-100)"
                                        min={0}
                                        max={100}
                                        {...field}
                                        onChange={event => {
                                            const value = event.target.valueAsNumber;
                                            if (isNaN(value)) {
                                            field.onChange(0); // Handle NaN by setting to 0
                                            } else if (value < 0) field.onChange(0);
                                            else if (value > 100) field.onChange(100);
                                            else field.onChange(value); // Pass valid, clamped number
                                        }}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                                />                            
                        </div>
                    </div>
                    
                    <div className="grid grid-cols-2 grid-rows-5 gap-4">
                        <div className="flex flex-col w-full text-sm">
                            <b>Cultural Fit <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="culturalFitNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Alignment with the company’s values and culture, adaptability, and ability to collaborate with others."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Attitude and Work Ethic <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="attitudeWorkEthicNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Positivity, initiative, and motivation. Willingness to learn and go beyond the job description. Dependability and consistency."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Soft Skills / Interpersonal Skills <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="softSkillsNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Communication (verbal and written). Emotional intelligence and empathy. Conflict resolution, listening, and collaboration."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Problem-Solving and Critical Thinking <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="problemSolvingNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Ability to think logically and independently. Creativity in approaching challenges."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Potential and Learning Agility <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="potentialLearningAgilityNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Growth mindset. Willingness and ability to learn new things quickly."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Professionalism and Presentation <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="professionalismPresentationNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: How the candidate presents themselves during the interview (punctuality, dress, demeanor). Preparation and research about the company."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>References and Reputation <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="referencesReputationNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Endorsements from previous employers or colleagues. Professional online presence (e.g., LinkedIn). Background checks or public reviews (if applicable)."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Passion and Interest <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="passionInterestNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Genuine enthusiasm/motivation for the role and industry. Knowledge of the company’s work/products/services."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Availability and Flexibility <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="availabilityFlexibilityNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Start date availability. Willingness to travel, relocate, or work flexible hours if needed."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col w-full text-sm">
                            <b>Salary Expectations and Negotiability <em className="text-xs text-muted-foreground">(optional)</em></b>
                            <FormField
                                control={form.control}
                                name="salaryExpectationsNote"
                                render={({ field }) => (
                                    <FormItem>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Ex: Alignment between the candidate’s compensation expectations and the company’s budget. Openness to negotiation or benefits trade-offs."
                                            className="min-h-[80px] w-full"
                                            {...field}
                                        />
                                    </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
    
                    <div className="flex w-full pt-4 items-center justify-center">
                        <Button type="submit" disabled={pending} className="w-full cursor-pointer">
                            {pending ? (deleting ? "Deleting Note..." : "Adding Note...") : "Add Note"}
                        </Button>
                    </div>
                </form>
              </Form>
        </div>
        &nbsp;
    </div>
  );
};