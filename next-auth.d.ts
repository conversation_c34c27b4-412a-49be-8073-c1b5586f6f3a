
import { UserRole, UserType } from "@prisma/client";
import type { AdapterUser } from "@auth/core/adapters";
import type { DefaultSession, JWT as NextAuthJWT } from "next-auth";

export interface ExtendedAdapterUser extends AdapterUser {
  emailHash?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  companyId?: string | null;
  companyName?: string | null;
  role: UserRole;
  userType: UserType;
  onboarded: boolean;
  planSet: boolean;
  verified: boolean;
}

// Define ExtendedUser for the session object
export interface ExtendedUser extends DefaultSession["user"] {
  id: string;
  role: UserRole;
  userType: UserType;
  onboarded: boolean;
  planSet: boolean;
  verified: boolean;
  companyId?: string | null;
  companyName?: string | null;
  email?: string | null; 
  name?: string | null;
  image?: string | null;
}

declare module "next-auth" {
    interface Session {
        user: ExtendedUser;
    }
}

declare module "next-auth/jwt" {
    // Extend JWT with the same custom fields as Extended<PERSON>ser (or a subset if not all are needed in JWT directly)
    interface JWT extends NextAuthJWT { // NextAuthJWT provides sub, name, email, picture by default
        id?: string; // Your custom user ID, if different from sub
        role: UserRole;
        userType: UserType;
        onboarded: boolean;
        planSet: boolean;
        verified: boolean;
        companyId: string | null; 
        // name & email are typically part of NextAuthJWT.
        // If you are consistently overriding them in the jwt callback, ensure their types here match.
        // e.g., email: string; name: string | null;
    }
}