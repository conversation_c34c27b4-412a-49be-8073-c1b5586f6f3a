"use server";

import { prisma } from "@/lib/prisma/prismaClient";
import { safeDecrypt } from "@/utils/security/safeDecrypt";
import { UserStatus } from "@prisma/client";

export const getCompanyResumesData = async (companyId: string) => {
    try {
      const data = await prisma.userResume.findMany({
        where: {
            companyId: companyId
        },
        select: {
                id: true,
                userId: true,
                name: true,
                email: true,
                phone: true,
                companyId: true,
                picture: true,
                resumeEvaluation: true,
                fileContent: true,
                createdAt: true,
                shortlists: {
                    where: {
                        jobPost: {
                            companyId: companyId
                        }
                    }
                },
                resumeMatches: {
                    where: {
                        jobPost: {
                            companyId: companyId
                        },
                        overall_score: {
                            gte: 70 // Only include matches with 70% or higher score
                        }
                    }
                },
                file: {
                    select: {
                        id: true,
                        fileType: true,
                        fileName: true,
                        url: true,
                    }
                },
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        appliedJobPosts: {
                            where: {
                                jobPost: {
                                    companyId: companyId
                                }
                            }
                        }
                    }
                }
            }
      });

      const resumes = data.map(resume => ({
        ...resume,
        name: resume.name ? safeDecrypt(resume.name) : null,
        email: resume.email ? safeDecrypt(resume.email) : null,
        phone: resume.phone ? safeDecrypt(resume.phone) : null,
        picture: resume.picture ? safeDecrypt(resume.picture) : null,
        fileContent: resume.fileContent ? safeDecrypt(resume.fileContent) : null,
        file: resume.file ? {
            ...resume.file,
            url: resume.file.url ? safeDecrypt(resume.file.url) : null
        } : null
      }));

      // Sort by lastName after decryption
      resumes.sort((a, b) => {
        const nameA = a.name?.toLowerCase() || '';
        const nameB = b.name?.toLowerCase() || '';
        return nameA.localeCompare(nameB);
      });

        // console.log({COMPANYRESUMES: JSON.stringify(resumes, null, 2)});

      return resumes;
    } catch (error) {
      return [];
    }
};

export const getCompanyFilesData = async (id: string) => {
    try {
      const data = await prisma.userFile.findMany({
        where: {
            companyId: id
        },
        include: {
            resume: {
                select: {
                    id: true
                }
            }
        },
        orderBy: {
            createdAt: "desc"
        }
      });

      if (!data) {
        return [];
      }

      const files = data.map(file => ({
        ...file,
        url: file.url ? safeDecrypt(file.url) : null
      }));

      return files;
    } catch (error) {
      return [];
    }
};

export const getCompanyJobListData = async (companyId: string) => {
  try {
    const data = await prisma?.jobPost.findMany({
        where: {
          companyId: companyId,
          status: { not: "DELETED" },
        },
        select: {
          id: true,
          jobTitle: true,
          jobDescription: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          company: {
            select: {
                id: true,
              name: true,
              logo: true,
            },
          },
          _count: {
            select: {
              applications: true,
            },
          },
          resumeMatches: {
            where: {
              overall_score: {
                gte: 70 // Only count matches with 80% or higher score
              }
            }
          }
        },
    
        orderBy: {
          createdAt: "desc",
        },
      });

      console.log({COMPANYJOBLIST: JSON.stringify(data, null, 2)});

    // Transform the data to include the count of high-scoring matches
    return data?.map(job => ({
        ...job,
        _count: {
        ...job._count,
        resumeMatches: job.resumeMatches.length
        }
    }));
  } catch (error) {
    // console.error("Error fetching job list:", error);
    return null;
  }
};

export async function getCompanyData(id: string) {
 
    try {
        const company = await prisma.company.findUnique({
            where: { id },
            select: {
                id: true,
                name: true,
                location: true,
                address: true,
                phone: true,
                email: true,
                description: true,
                logo: true,
                about: true,
                website: true,
                xAccount: true,
                linkedIn: true,
                tin: true,
                benefits: true,
                foreignerRatio: true,
                englishUsageRatio: true
            }
        });

        return company;
    } catch (error) {
      return null;
    }
};

export async function SaveCompanyLogoData(companyId: string, url: string) {

    try {
        const result = await prisma.company.update({
            where: {
                id: companyId
            },
            data: {
                logo: url
            }
        });

        return result;
    } catch (error) {        
      return null;
    }

};

export const getCompanyStatsData = async (companyId: string) => {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const [totalActiveJobs, totalApplicants, totalResumes, totalMatches, totalFiles, totalJobs, newApplicants, newShortlist, companyName] = await Promise.all([
        prisma.jobPost.count({
            where: { companyId: companyId, status: "ACTIVE" },
        }),
        prisma.appliedJobPost.count({
            where: { jobPost: { companyId: companyId } },
        }),
        prisma.userResume.count({
            where: { companyId: companyId },
        }),
        prisma.jobResumeMatch.count({
            where: { jobPost: { companyId: companyId } },
        }),
        prisma.userFile.count({
            where: { companyId: companyId },
        }),
        prisma.jobPost.count({
            where: { companyId: companyId, status: { not: "DELETED" } },
        }),
        prisma.appliedJobPost.count({
            where: {
            jobPost: { companyId: companyId },
            createdAt: {
                gte: twentyFourHoursAgo,
            },
            },
        }),
        prisma.jobShortlist.count({
            where: {
            jobPost: { companyId: companyId },
            createdAt: {
                gte: twentyFourHoursAgo,
            },
            },
        }),
        prisma.company.findUnique({
            where: { id: companyId },
            select: { name: true },
        }),
    ]);

    return {
        totalActiveJobs,
        totalApplicants,
        totalResumes,
        totalMatches,
        totalFiles,
        totalJobs,
        newApplicants,
        newShortlist,
        companyName
    };

  } catch (error) {
    console.error("Error fetching company stats:", error);
    return null;
  }
};

export const getCompanyInfoData = async (companyId: string, userId?: string) => {
    try {
        const [team, companyPlan] = await Promise.all([
            prisma.user.findMany({
                where: { companyId: companyId, status: UserStatus.ACTIVE },
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    createdAt: true,
                    _count: {
                        select: {
                            jobPosts: true // This will count job posts for each user
                        }
                    }
                }
            }),
            prisma.userPlan.findFirst({
                where: { companyId: companyId },
                select: {
                    id: true,
                    pricingPlanId: true,
                    amount: true,
                    paymentFrequency: true,
                    exchangeRate: true,
                    exchangeCurrencyCode: true,
                    exchangeCurrencySymbol: true,
                    userLocale: true,
                    baseCurrencyCode: true,
                    baseCurrencySymbol: true,
                    createdAt: true,
                    updatedAt: true,
                    endedAt: true,
                    plan: {
                        select: {
                            planName: true
                        }
                    }
                }
            })
        ]);

        const companyTeam = team.map(user => ({
            ...user,
            firstName: safeDecrypt(user.firstName),
            lastName: safeDecrypt(user.lastName),
            jobCount: user._count.jobPosts, // Add jobCount
            // Optionally remove the _count object if you don't need it anymore
            // _count: undefined 
        }));

        console.log({companyTeam, companyPlan})

        return Promise.all([
            companyTeam,
            companyPlan, 
        ]);

    } catch (error) {
      console.error("Error in getCompanyInfoData:", error);
      return null;
    }
};
